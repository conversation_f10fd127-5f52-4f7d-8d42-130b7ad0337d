import { BASE_URL } from '../router.js';

export default class Sidebar {
    constructor() {
        this.state = {
            brand: {
                text: 'LockedPay',
                icon: 'shield-lock',
                href: `${BASE_URL}/`
            },
            nav: {
                apps: {
                    section: {
                        heading: 'Navigation'
                    },
                    items: [
                        { 
                            text: 'Dashboard', 
                            href: `${BASE_URL}/`,
                            icon: 'home',
                            iconClass: 'text-white',
                        },
                        { 
                            text: 'Profile', 
                            href: `${BASE_URL}/profile`,
                            icon: 'user',
                            iconClass: 'text-white',
                        },
                        { 
                            text: 'Sign Out',
                            href: `${BASE_URL}/logout`,
                            icon: 'power',
                            iconClass: 'text-white',
                            id: 'logout-btn'
                        }
                    ]
                }
            },
        };
    }

    getBrandLogo() {
        return `
            <a class="navbar-brand p-3" href="${BASE_URL}">
                <div class="d-flex align-items-center">
                    <div class="brand-icon rounded-circle bg-transparent p-2 me-2">
                        <i class="bi bi-${this.state.brand.icon} text-white fs-3"></i>
                    </div>
                    <h3 class="mb-0 fw-bold text-white">${this.state.brand.text}</h3>
                </div>
            </a>
        `;
    }

    getNavItem(item) {
        const currentPath = window.location.pathname;
        const isActive = currentPath === item.href;
        const activeClass = isActive ? ' active' : '';
        const hasArrow = item.children ? ' has-arrow' : '';

        return `
            <li class="nav-item mb-2">
                <a class="nav-link${hasArrow}${activeClass} p-3 rounded-3" 
                   href="${item.href || '#!'}"
                   ${item.children ? 'data-bs-toggle="collapse"' : ''}>
                    <div class="d-flex align-items-center">
                        ${item.icon ? `
                            <div class="nav-icon-wrapper rounded-circle ${isActive ? 'bg-primary bg-opacity-10' : 'bg-light'} p-2 me-3">
                                <i data-feather="${item.icon}" class="nav-icon icon-sm ${item.iconClass || 'text-secondary'}"></i>
                            </div>
                        ` : ''}
                        <span class="flex-grow-1 fw-medium text-white">${item.text}</span>
                        ${item.children ? `
                            <i class="bi bi-chevron-right ms-2 ${isActive ? 'rotate-90' : ''}"></i>
                        ` : ''}
                    </div>
                </a>
                ${item.children ? this.getNavChildren(item) : ''}
            </li>
        `;
    }

    getAppsNav() {
        const { section, items } = this.state.nav.apps;
        return `
            <li class="nav-item mb-3">
                <div class="navbar-heading text-uppercase fw-bold text-white small px-3">
                    ${section.heading}
                </div>
            </li>
            ${items.map(item => this.getNavItem(item)).join('')}
        `;
    }

    getNavChildren(parent) {
        const items = parent.children.map(child => `
            <li class="nav-item">
                <a class="nav-link${child.isActive ? ' active' : ''} p-3 ms-4 rounded-3" 
                   href="${child.href}">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-dot me-2"></i>
                        <span>${child.text}</span>
                    </div>
                </a>
            </li>
        `).join('');

        return `
            <div id="nav${parent.text}" class="collapse ${parent.isActive ? 'show' : ''}" data-bs-parent="#sideNavbar">
                <ul class="nav flex-column">
                    ${items}
                </ul>
            </div>`;
    }

    render() {
        return `
            <div class="navbar-vertical navbar nav-dashboard bg-white border-end">
                <div class="d-flex flex-column h-100">
                    ${this.getBrandLogo()}
                    <hr class="mx-3 my-2 text-white" />
                    
                    <div class="flex-grow-1 overflow-auto" data-simplebar>
                        <ul class="navbar-nav flex-column px-3" id="sideNavbar">
                            ${this.getAppsNav()}
                        </ul>
                    </div>
                    
                    <div class="p-3 mt-auto">
                        <div class="card border-0 ">
                            <div class="card-body p-4 text-center rounded-3">
                                <div class="icon-wrapper rounded-circle bg-white p-3 mx-auto mb-3" style="width: fit-content">
                                    <i class="bi bi-shop fs-4 text-white"></i>
                                </div>
                                <h6 class="fw-bold text-white">Explore Marketplace</h6>
                                <p class="small text-white mb-3">
                                    Discover services to help you grow, whether for business or personal use, all in one place.
                                </p>
                                <a href="https://centurionbd.com" 
                                class="btn btn-warning btn-sm rounded-pill w-100 shadow-sm">
                                <i class="bi bi-arrow-right-circle me-2"></i>Visit Now
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>`;
    }
}