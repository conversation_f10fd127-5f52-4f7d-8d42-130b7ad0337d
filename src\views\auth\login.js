import { BASE_URL, navigateTo } from '../../router.js';
import ApiService from '../../services/api.js';
import { authHandler } from '../../utils/authHandler.js';

export default class LoginView {
    constructor() {
        this.state = {
            title: 'Sign in to your account',
            subtitle: 'Access your merchant cashier portal',
            logo: {
                src: 'assets/images/brand/logo/logo.svg',
                alt: 'Paypoint Logo'
            },
            formFields: {
                merchantId: {
                    id: 'merchant_id',
                    label: 'Merchant ID',
                    type: 'number',
                    placeholder: 'Enter your merchant ID',
                    required: true,
                    icon: 'bi-building'
                },
                email: {
                    id: 'email',
                    label: 'Username or Email',
                    type: 'text',
                    placeholder: 'Enter your username or email',
                    required: true,
                    icon: 'bi-person'
                },
                password: {
                    id: 'password',
                    label: 'Password',
                    type: 'password',
                    placeholder: 'Enter your password',
                    required: true,
                    icon: 'bi-lock'
                }
            },
            links: {
                
            },
            error: {
                visible: false,
                message: ''
            }
        };
    }

    getErrorMessage() {
        if (!this.state.error.visible) return '';
        return `
            <div class="alert alert-danger alert-dismissible fade show mb-4" role="alert">
                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                ${this.state.error.message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;
    }

    getFormField(field) {
        const { id, label, type, placeholder, required, icon } = field;
        let value = '';
        if (id === 'email' && localStorage.getItem('remembered-username')) {
            value = localStorage.getItem('remembered-username');
        } else if (id === 'merchant_id' && localStorage.getItem('remembered-merchant-id')) {
            value = localStorage.getItem('remembered-merchant-id');
        }
        return `
            <div class="mb-3">
                <label for="${id}" class="form-label fw-semibold">${label}</label>
                <div class="input-group input-group-lg border rounded-3 p-1">
                    <span class="input-group-text bg-light-soft border-0 px-2" style="min-width: auto;">
                        <i class="bi ${icon} text-dark me-1 fw-bold"></i>
                    </span>
                    <input type="${type}" id="${id}" class="form-control border-0 bg-light-soft shadow-none"
                        name="${id}" placeholder="${placeholder}"
                        ${required ? 'required' : ''}
                        value="${value}" autocomplete="${id === 'password' ? 'current-password' : 'username'}" />
                </div>
            </div>
        `;
    }

    getLoginForm() {
        return `
            <form class="mb-4" autocomplete="on">
                ${this.getErrorMessage()}
                ${this.getFormField(this.state.formFields.merchantId)}
                ${this.getFormField(this.state.formFields.email)}
                ${this.getFormField(this.state.formFields.password)}
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="rememberme"
                            ${localStorage.getItem('remembered-username') ? 'checked' : ''} />
                        <label class="form-check-label" for="rememberme">Remember me</label>
                    </div>
                </div>
                <div class="d-grid">
                    <button id="signin" class="btn btn-primary btn-lg d-flex align-items-center justify-content-center gap-2" type="submit">
                        <i class="bi bi-box-arrow-in-right fs-5"></i>
                        <span class="fw-bold">Sign in</span>
                    </button>
                </div>
            </form>
        `;
    }

    render() {
        return `
            <main class="container-fluid d-flex flex-column bg-light min-vh-100">
                <div class="row align-items-center justify-content-center g-0 min-vh-100">
                    <div class="col-12 col-md-8 col-lg-5 col-xxl-4 py-8 py-xl-0">
                        <div class="card border-0 shadow-lg rounded-4 auth-card">
                            <div class="card-body p-5">
                                <div class="mb-4 text-center">
                                    <a href="#!">
                                        <img src="${this.state.logo.src}" class="mb-3" style="height: 48px;" alt="${this.state.logo.alt}" />
                                    </a>
                                    <h2 class="fw-bold mb-1 text-white">${this.state.title}</h2>
                                    <p class="text-white mb-4">${this.state.subtitle}</p>
                                </div>
                                ${this.getLoginForm()}
                                <div class="text-center mt-4">
                                    <small class="text-white">
                                        &copy; ${new Date().getFullYear()} Paypoint. All rights reserved.
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        `;
    }

    async handleSignin(event) {
        event.preventDefault();
        const merchant_id = document.getElementById('merchant_id').value.trim();
        const username = document.getElementById('email').value.trim();
        const password = document.getElementById('password').value.trim();
        const rememberMe = document.getElementById('rememberme').checked;

        try {
            const response = await ApiService.login({ merchant_id, username, password });

            if (response.role !== "cashier") {
                throw new Error('Access denied');
            }

            localStorage.setItem('user-token', response.token);
            localStorage.setItem('user_id', response.user_id);
            localStorage.setItem('is_new_user', 'false');
            localStorage.setItem('username', username);
            localStorage.setItem('is_authenticated', 'true');
            localStorage.setItem('merchant_code', response.merchant_code);
            window.is_authenticated = true;

            if (rememberMe) {
                localStorage.setItem('remembered-username', username);
                localStorage.setItem('remembered-merchant-id', merchant_id);
            } else {
                localStorage.removeItem('remembered-username');
                localStorage.removeItem('remembered-merchant-id');
            }

            // Initialize session timeout after successful login
            authHandler.initSessionTimeout(30, 5); // 30 minutes timeout, 5 minutes warning

            navigateTo(`${BASE_URL}/`);
        } catch (error) {
            let errorMessage = 'An unexpected error occurred. Please try again.';
            if (error.status === 401) {
                errorMessage = 'Invalid username or password.';
            } else if (error.status === 404) {
                errorMessage = 'User not found.';
            } else if (error.status === 429) {
                errorMessage = 'Too many login attempts. Please try again later.';
            } else if (!navigator.onLine) {
                errorMessage = 'No internet connection. Please check your network.';
            } else if (error.message === 'Access denied') {
                errorMessage = 'Access denied. You do not have permission to access this portal.';
            }

            this.state.error = {
                visible: true,
                message: errorMessage
            };

            const formContainer = document.querySelector('.card-body');
            if (formContainer) {
                formContainer.innerHTML = `
                    <div class="mb-4 text-center">
                        <a href="#!">
                            <img src="${this.state.logo.src}" class="mb-3" style="height: 48px;" alt="${this.state.logo.alt}" />
                        </a>
                        <h2 class="fw-bold mb-1 text-white">${this.state.title}</h2>
                        <p class="text-white mb-4">${this.state.subtitle}</p>
                    </div>
                    ${this.getLoginForm()}
                    <div class="text-center mt-4">
                        <small class="text-white">
                            &copy; ${new Date().getFullYear()} Paypoint. All rights reserved.
                        </small>
                    </div>
                `;
                this.initializeEventListeners();
            }
        }
    }

    afterRender() {
        this.clearStoredData();
        this.preventBackNavigation();
        this.initializeEventListeners();
    }

    clearStoredData() {
        // Only clear session-related data, preserve remembered credentials
        localStorage.removeItem('user-token');
        localStorage.removeItem('nav-toggled');
        localStorage.removeItem('is_authenticated');
        localStorage.removeItem('username');
        localStorage.removeItem('user_id');
        localStorage.removeItem('merchant_code');
        sessionStorage.clear();
        // Do not remove 'remembered-username' and 'remembered-merchant-id'
    }

    clearError() {
        this.state.error = {
            visible: false,
            message: ''
        };
    }

    preventBackNavigation() {
        history.pushState(null, null, location.href);
        window.onpopstate = () => history.go(1);
    }

    initializeEventListeners() {
        const signinBtn = document.getElementById('signin');
        if (signinBtn) {
            signinBtn.addEventListener('click', (e) => this.handleSignin(e));
        }

        // Enter key submits form
        const form = document.querySelector('form');
        if (form) {
            form.addEventListener('submit', (e) => this.handleSignin(e));
        }

        // Error dismissal
        const errorCloseBtn = document.querySelector('.alert .btn-close');
        if (errorCloseBtn) {
            errorCloseBtn.addEventListener('click', () => {
                this.clearError();
                const formContainer = document.querySelector('.card-body');
                if (formContainer) {
                    formContainer.innerHTML = `
                        <div class="mb-4 text-center">
                            <a href="#!">
                                <img src="${this.state.logo.src}" class="mb-3" style="height: 48px;" alt="${this.state.logo.alt}" />
                            </a>
                            <h2 class="fw-bold mb-1 text-white">${this.state.title}</h2>
                            <p class="text-white mb-4">${this.state.subtitle}</p>
                        </div>
                        ${this.getLoginForm()}
                        <div class="text-center mt-4">
                            <small class="text-white">
                                &copy; ${new Date().getFullYear()} Paypoint. All rights reserved.
                            </small>
                        </div>
                    `;
                    this.initializeEventListeners();
                }
            });
        }
    }
}