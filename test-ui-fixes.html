<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI Freeze Fixes Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    <link href="src/styles/modal-fixes.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }
        .test-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        .test-section {
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            padding: 1.5rem;
        }
        .test-section:last-child {
            border-bottom: none;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-pass { background-color: #28a745; }
        .status-fail { background-color: #dc3545; }
        .status-pending { background-color: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="test-card">
                    <div class="card-header bg-primary text-white">
                        <h3 class="mb-0">
                            <i class="bi bi-bug me-2"></i>
                            UI Freeze Fixes Test Suite
                        </h3>
                        <p class="mb-0 mt-2">Testing critical UI freeze fixes for modal management, file uploads, and session timeouts</p>
                    </div>
                    
                    <!-- Test 1: Modal Management -->
                    <div class="test-section">
                        <h5><span class="status-indicator status-pending" id="modal-status"></span>Modal Management Tests</h5>
                        <p class="text-muted">Testing safe modal transitions and race condition prevention</p>
                        <div class="d-flex gap-2 flex-wrap">
                            <button class="btn btn-primary" id="test-modal-basic">Basic Modal</button>
                            <button class="btn btn-info" id="test-modal-transition">Modal Transition</button>
                            <button class="btn btn-warning" id="test-modal-rapid">Rapid Open/Close</button>
                            <button class="btn btn-success" id="test-modal-cleanup">Cleanup Test</button>
                        </div>
                        <div id="modal-results" class="mt-3"></div>
                    </div>

                    <!-- Test 2: File Upload -->
                    <div class="test-section">
                        <h5><span class="status-indicator status-pending" id="upload-status"></span>File Upload Tests</h5>
                        <p class="text-muted">Testing asynchronous file processing and UI freeze prevention</p>
                        <div class="row">
                            <div class="col-md-6">
                                <input type="file" class="form-control" id="test-file-input" accept="image/*">
                                <small class="text-muted">Select an image file to test async processing</small>
                            </div>
                            <div class="col-md-6">
                                <button class="btn btn-primary" id="test-large-file">Test Large File</button>
                                <button class="btn btn-info" id="test-multiple-files">Multiple Files</button>
                            </div>
                        </div>
                        <div id="upload-preview" class="mt-3"></div>
                        <div id="upload-results" class="mt-3"></div>
                    </div>

                    <!-- Test 3: Session Timeout -->
                    <div class="test-section">
                        <h5><span class="status-indicator status-pending" id="timeout-status"></span>Session Timeout Tests</h5>
                        <p class="text-muted">Testing enhanced timeout popups with user confirmation</p>
                        <div class="d-flex gap-2 flex-wrap">
                            <button class="btn btn-warning" id="test-timeout-warning">Timeout Warning</button>
                            <button class="btn btn-danger" id="test-timeout-immediate">Immediate Timeout</button>
                            <button class="btn btn-info" id="test-session-extend">Extend Session</button>
                        </div>
                        <div id="timeout-results" class="mt-3"></div>
                    </div>

                    <!-- Test 4: SweetAlert Integration -->
                    <div class="test-section">
                        <h5><span class="status-indicator status-pending" id="swal-status"></span>SweetAlert Integration Tests</h5>
                        <p class="text-muted">Testing enhanced SweetAlert with freeze prevention</p>
                        <div class="d-flex gap-2 flex-wrap">
                            <button class="btn btn-success" id="test-swal-basic">Basic Alert</button>
                            <button class="btn btn-info" id="test-swal-confirm">Confirmation</button>
                            <button class="btn btn-warning" id="test-swal-timer">Timer Alert</button>
                            <button class="btn btn-danger" id="test-swal-error">Error Alert</button>
                        </div>
                        <div id="swal-results" class="mt-3"></div>
                    </div>

                    <!-- Test Results Summary -->
                    <div class="test-section">
                        <h5><i class="bi bi-clipboard-check me-2"></i>Test Results Summary</h5>
                        <div id="test-summary" class="row">
                            <div class="col-md-3">
                                <div class="text-center">
                                    <div class="h2 text-success" id="passed-count">0</div>
                                    <small class="text-muted">Passed</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <div class="h2 text-danger" id="failed-count">0</div>
                                    <small class="text-muted">Failed</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <div class="h2 text-warning" id="pending-count">4</div>
                                    <small class="text-muted">Pending</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <div class="h2 text-info" id="total-count">4</div>
                                    <small class="text-muted">Total</small>
                                </div>
                            </div>
                        </div>
                        <div class="mt-3">
                            <button class="btn btn-primary" id="run-all-tests">
                                <i class="bi bi-play-circle me-2"></i>Run All Tests
                            </button>
                            <button class="btn btn-secondary" id="reset-tests">
                                <i class="bi bi-arrow-clockwise me-2"></i>Reset Tests
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Test Modals -->
    <div class="modal fade" id="testModal1" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-primary">
                    <h5 class="modal-title text-white">Test Modal 1</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>This is a test modal to verify safe modal management.</p>
                    <button class="btn btn-info" id="transition-to-modal2">Transition to Modal 2</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="testModal2" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-success">
                    <h5 class="modal-title text-white">Test Modal 2</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>This is the second test modal for transition testing.</p>
                    <button class="btn btn-primary" id="back-to-modal1">Back to Modal 1</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script type="module">
        // Import our enhanced utilities
        import { modalManager } from './src/utils/modalManager.js';
        import { authHandler } from './src/utils/authHandler.js';

        // Test state
        let testResults = {
            modal: 'pending',
            upload: 'pending', 
            timeout: 'pending',
            swal: 'pending'
        };

        // Update status indicators
        function updateStatus(test, status) {
            testResults[test] = status;
            const indicator = document.getElementById(`${test}-status`);
            indicator.className = `status-indicator status-${status}`;
            updateSummary();
        }

        function updateSummary() {
            const passed = Object.values(testResults).filter(s => s === 'pass').length;
            const failed = Object.values(testResults).filter(s => s === 'fail').length;
            const pending = Object.values(testResults).filter(s => s === 'pending').length;
            
            document.getElementById('passed-count').textContent = passed;
            document.getElementById('failed-count').textContent = failed;
            document.getElementById('pending-count').textContent = pending;
        }

        // Modal Tests
        document.getElementById('test-modal-basic').addEventListener('click', async () => {
            try {
                await modalManager.displayModal(
                    document.getElementById('testModal1').outerHTML,
                    'testModal1'
                );
                updateStatus('modal', 'pass');
                document.getElementById('modal-results').innerHTML =
                    '<div class="alert alert-success">✓ Basic modal display successful</div>';
            } catch (error) {
                updateStatus('modal', 'fail');
                document.getElementById('modal-results').innerHTML =
                    `<div class="alert alert-danger">✗ Basic modal failed: ${error.message}</div>`;
            }
        });

        document.getElementById('test-modal-transition').addEventListener('click', async () => {
            try {
                // Show first modal
                await modalManager.displayModal(
                    document.getElementById('testModal1').outerHTML,
                    'testModal1'
                );

                // Wait a bit then transition
                setTimeout(async () => {
                    await modalManager.transitionModal(
                        'testModal1',
                        document.getElementById('testModal2').outerHTML,
                        'testModal2'
                    );
                    updateStatus('modal', 'pass');
                    document.getElementById('modal-results').innerHTML =
                        '<div class="alert alert-success">✓ Modal transition successful</div>';
                }, 1000);
            } catch (error) {
                updateStatus('modal', 'fail');
                document.getElementById('modal-results').innerHTML =
                    `<div class="alert alert-danger">✗ Modal transition failed: ${error.message}</div>`;
            }
        });

        document.getElementById('test-modal-rapid').addEventListener('click', async () => {
            try {
                // Rapid open/close test
                for (let i = 0; i < 5; i++) {
                    const modal = await modalManager.displayModal(
                        `<div class="modal fade" id="rapidTest${i}"><div class="modal-dialog"><div class="modal-content">
                        <div class="modal-header"><h5 class="modal-title text-white">Rapid Test ${i}</h5></div>
                        <div class="modal-body">Testing rapid operations...</div></div></div></div>`,
                        `rapidTest${i}`
                    );
                    await new Promise(resolve => setTimeout(resolve, 100));
                    await modalManager.safeHideModal(`rapidTest${i}`);
                }
                updateStatus('modal', 'pass');
                document.getElementById('modal-results').innerHTML =
                    '<div class="alert alert-success">✓ Rapid modal operations successful</div>';
            } catch (error) {
                updateStatus('modal', 'fail');
                document.getElementById('modal-results').innerHTML =
                    `<div class="alert alert-danger">✗ Rapid modal test failed: ${error.message}</div>`;
            }
        });

        // File Upload Tests
        document.getElementById('test-file-input').addEventListener('change', async (e) => {
            const file = e.target.files[0];
            if (!file) return;

            try {
                const startTime = performance.now();

                // Simulate our async file handling
                await new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onload = () => {
                        // Simulate canvas operations with requestAnimationFrame
                        requestAnimationFrame(() => {
                            const img = new Image();
                            img.onload = () => {
                                setTimeout(() => {
                                    const canvas = document.createElement('canvas');
                                    canvas.width = 200;
                                    canvas.height = 200;
                                    const ctx = canvas.getContext('2d');
                                    ctx.drawImage(img, 0, 0, 200, 200);

                                    document.getElementById('upload-preview').innerHTML =
                                        `<img src="${canvas.toDataURL()}" class="img-thumbnail" style="max-width: 200px;">`;
                                    resolve();
                                }, 0);
                            };
                            img.src = reader.result;
                        });
                    };
                    reader.onerror = reject;
                    reader.readAsDataURL(file);
                });

                const endTime = performance.now();
                updateStatus('upload', 'pass');
                document.getElementById('upload-results').innerHTML =
                    `<div class="alert alert-success">✓ File processed in ${Math.round(endTime - startTime)}ms without UI freeze</div>`;
            } catch (error) {
                updateStatus('upload', 'fail');
                document.getElementById('upload-results').innerHTML =
                    `<div class="alert alert-danger">✗ File upload test failed: ${error.message}</div>`;
            }
        });

        // Session Timeout Tests
        document.getElementById('test-timeout-warning').addEventListener('click', async () => {
            try {
                const result = await modalManager.safeSweetAlert({
                    icon: 'warning',
                    title: 'Session Warning Test',
                    text: 'This is a test of the session warning system.',
                    confirmButtonText: 'Extend Session',
                    cancelButtonText: 'Logout',
                    showCancelButton: true,
                    allowOutsideClick: false,
                    allowEscapeKey: false
                });

                updateStatus('timeout', 'pass');
                document.getElementById('timeout-results').innerHTML =
                    `<div class="alert alert-success">✓ Timeout warning test successful. User chose: ${result.isConfirmed ? 'Extend' : 'Logout'}</div>`;
            } catch (error) {
                updateStatus('timeout', 'fail');
                document.getElementById('timeout-results').innerHTML =
                    `<div class="alert alert-danger">✗ Timeout test failed: ${error.message}</div>`;
            }
        });

        // SweetAlert Tests
        document.getElementById('test-swal-basic').addEventListener('click', async () => {
            try {
                await modalManager.safeSweetAlert({
                    icon: 'success',
                    title: 'Test Alert',
                    text: 'This is a test of the enhanced SweetAlert system.',
                    timer: 2000
                });

                updateStatus('swal', 'pass');
                document.getElementById('swal-results').innerHTML =
                    '<div class="alert alert-success">✓ SweetAlert test successful</div>';
            } catch (error) {
                updateStatus('swal', 'fail');
                document.getElementById('swal-results').innerHTML =
                    `<div class="alert alert-danger">✗ SweetAlert test failed: ${error.message}</div>`;
            }
        });

        // Run all tests
        document.getElementById('run-all-tests').addEventListener('click', async () => {
            document.getElementById('test-modal-basic').click();
            await new Promise(resolve => setTimeout(resolve, 1000));
            document.getElementById('test-timeout-warning').click();
            await new Promise(resolve => setTimeout(resolve, 1000));
            document.getElementById('test-swal-basic').click();
        });

        // Reset tests
        document.getElementById('reset-tests').addEventListener('click', () => {
            testResults = { modal: 'pending', upload: 'pending', timeout: 'pending', swal: 'pending' };
            Object.keys(testResults).forEach(test => updateStatus(test, 'pending'));
            document.querySelectorAll('[id$="-results"]').forEach(el => el.innerHTML = '');
            document.getElementById('upload-preview').innerHTML = '';
        });

        console.log('UI Freeze Fixes Test Suite Loaded');
        console.log('Modal Manager:', modalManager);
        console.log('Auth Handler:', authHandler);
    </script>
</body>
</html>
