export const attachmentHandlers = {
    initializeAttachmentHandlers(modalElement) {
        if (!modalElement) {
            console.error('Modal element not provided to initializeAttachmentHandlers');
            return;
        }

        // Store elements in an object that can be updated (for cameraInput replacement)
        let elements = {
            fileInput: modalElement.querySelector('.file-input'),
            cameraInput: modalElement.querySelector('.camera-input'),
            attachBtn: modalElement.querySelector('.attach-file-btn'),
            attachmentPreview: modalElement.querySelector('.attachment-preview'),
            attachmentName: modalElement.querySelector('.attachment-name'),
            imagePreview: modalElement.querySelector('.image-preview'),
            removeAttachmentBtn: modalElement.querySelector('.remove-attachment')
        };

        const essentialElementSelectors = [
            '.file-input', '.camera-input', '.attachment-preview',
            '.attachment-name', '.image-preview'
        ];
        if (essentialElementSelectors.some(selector => !modalElement.querySelector(selector))) {
            console.error('One or more critical attachment UI elements are missing in the modal. Initialization aborted. Missing elements for selectors:',
                essentialElementSelectors.filter(selector => !modalElement.querySelector(selector))
            );
            return;
        }

        // Define the event handler for file/camera inputs
        // This needs to be a named function or a const if we re-attach it after cloning
        const onFileSelected = (e) => {
            handleFile(e.target.files[0], e.target);
        };

        const showMobileChoice = () => {
            if (window.innerWidth <= 768) {
                if (typeof Swal === 'undefined') {
                    console.error('SweetAlert2 (Swal) is not available. Attempting direct camera input.');
                    if (elements.cameraInput) {
                        // Force reset by cloning for camera input even in this fallback
                        const parent = elements.cameraInput.parentNode;
                        if (parent) {
                            const newCameraInput = elements.cameraInput.cloneNode(true);
                            newCameraInput.value = null;
                            parent.replaceChild(newCameraInput, elements.cameraInput);
                            elements.cameraInput = newCameraInput; // Update reference
                            elements.cameraInput.addEventListener('change', onFileSelected); // Re-attach listener
                        } else {
                             elements.cameraInput.value = null; // Fallback to simple reset
                        }
                        elements.cameraInput.click();
                    } else if (elements.fileInput) {
                        elements.fileInput.value = null;
                        elements.fileInput.click();
                    }
                    return;
                }
                Swal.fire({
                    title: 'Select Attachment Source',
                    showCancelButton: true,
                    confirmButtonText: 'Use Camera',
                    cancelButtonText: 'Choose File',
                    customClass: { popup: 'swal2-mobile' }
                }).then(result => {
                    if (result.isConfirmed) { // User chose Camera
                        if (elements.cameraInput) {
                            const parent = elements.cameraInput.parentNode;
                            if (parent) {
                                const newCameraInput = elements.cameraInput.cloneNode(true);
                                newCameraInput.value = null; // Ensure the cloned input is clear
                                parent.replaceChild(newCameraInput, elements.cameraInput);
                                elements.cameraInput = newCameraInput; // Update the reference
                                elements.cameraInput.addEventListener('change', onFileSelected); // Re-attach listener
                                elements.cameraInput.click();
                            } else {
                                // Fallback if parentNode is somehow null
                                console.warn("cameraInput parentNode is null, cannot replace. Using value reset.");
                                elements.cameraInput.value = null;
                                elements.cameraInput.click();
                            }
                        }
                    } else if (result.dismiss === Swal.DismissReason.cancel) { // User chose File
                        if (elements.fileInput) {
                            elements.fileInput.value = null; // Standard reset for file input
                            elements.fileInput.click();
                        }
                    }
                });
            } else { // Desktop or larger screens
                if (elements.fileInput) {
                    elements.fileInput.value = null; // Standard reset for file input
                    elements.fileInput.click();
                }
            }
        };

        elements.attachBtn?.addEventListener('click', showMobileChoice);

        const clearPreview = (clearFileInputs = false) => {
            elements.attachmentPreview?.classList.add('d-none');
            elements.imagePreview?.classList.add('d-none');
            if (elements.imagePreview) elements.imagePreview.src = '';
            if (elements.attachmentName) elements.attachmentName.textContent = '';

            if (clearFileInputs) {
                if (elements.fileInput) elements.fileInput.value = null;
                if (elements.cameraInput) elements.cameraInput.value = null;
            }
        };

        const handleFile = (file, eventSourceInput) => {
            if (!file) {
                clearPreview();
                if (eventSourceInput) eventSourceInput.value = null;
                return;
            }

            elements.attachmentPreview?.classList.remove('d-none');
            if (elements.attachmentName) elements.attachmentName.textContent = file.name;
            if (elements.imagePreview) {
                 elements.imagePreview.classList.add('d-none');
                 elements.imagePreview.src = '';
            }

            if (file.type.startsWith('image/')) {
                const MAX_PREVIEW_WIDTH = 800;
                const MAX_PREVIEW_HEIGHT = 600;
                const JPEG_QUALITY = 0.8;
                const tempImg = new Image();
                const objectUrl = URL.createObjectURL(file);

                tempImg.onload = () => {
                    URL.revokeObjectURL(objectUrl);
                    let { width: oWidth, height: oHeight } = tempImg;
                    let renderWidth = oWidth;
                    let renderHeight = oHeight;
                    const aspectRatio = oWidth / oHeight;

                    if (oWidth > MAX_PREVIEW_WIDTH || oHeight > MAX_PREVIEW_HEIGHT) {
                        if (oWidth / MAX_PREVIEW_WIDTH > oHeight / MAX_PREVIEW_HEIGHT) {
                            renderWidth = MAX_PREVIEW_WIDTH;
                            renderHeight = Math.round(renderWidth / aspectRatio);
                        } else {
                            renderHeight = MAX_PREVIEW_HEIGHT;
                            renderWidth = Math.round(renderHeight * aspectRatio);
                        }
                    }
                    if (renderWidth > MAX_PREVIEW_WIDTH) {
                        renderWidth = MAX_PREVIEW_WIDTH;
                        renderHeight = Math.round(renderWidth / aspectRatio);
                    }
                    if (renderHeight > MAX_PREVIEW_HEIGHT) {
                        renderHeight = MAX_PREVIEW_HEIGHT;
                        renderWidth = Math.round(renderHeight * aspectRatio);
                    }

                    const canvas = document.createElement('canvas');
                    canvas.width = renderWidth;
                    canvas.height = renderHeight;
                    const ctx = canvas.getContext('2d');
                    ctx.drawImage(tempImg, 0, 0, renderWidth, renderHeight);

                    try {
                        const dataUrl = canvas.toDataURL(file.type === 'image/png' ? 'image/png' : 'image/jpeg', JPEG_QUALITY);
                        if (elements.imagePreview) {
                            elements.imagePreview.src = dataUrl;
                            elements.imagePreview.classList.remove('d-none');
                            elements.imagePreview.style.maxWidth = '100%';
                            elements.imagePreview.style.maxHeight = '300px';
                            elements.imagePreview.style.objectFit = 'contain';
                        }
                    } catch (err) {
                        console.error('Failed to create data URL from canvas:', err);
                        clearPreview();
                        if (elements.attachmentName) elements.attachmentName.textContent = 'Error: Preview failed';
                    }
                };
                tempImg.onerror = () => {
                    URL.revokeObjectURL(objectUrl);
                    console.error('Failed to load image for preview. File might be corrupt or unsupported.');
                    clearPreview();
                    if(elements.attachmentName) elements.attachmentName.textContent = 'Error: Invalid image';
                };
                tempImg.src = objectUrl;
            } else {
                if (elements.imagePreview) {
                    elements.imagePreview.classList.add('d-none');
                    elements.imagePreview.src = '';
                }
            }
        };

        // Initial event listener attachment
        elements.fileInput?.addEventListener('change', onFileSelected);
        elements.cameraInput?.addEventListener('change', onFileSelected);

        elements.removeAttachmentBtn?.addEventListener('click', () => {
            clearPreview(true);
        });
    },

    getAttachmentData(modalElement) {
        // Retrieve elements fresh in case they were replaced, though `elements` in closure should be up-to-date.
        // However, this function is exported and might be called in a context where `elements` from init isn't directly available.
        // For safety, query here, or ensure `elements` object passed/accessible.
        // For now, assuming standard usage where modalElement is consistent.
        if (!modalElement) return null;

        const fileInput = modalElement.querySelector('.file-input'); // Could be the original or replaced one
        const cameraInput = modalElement.querySelector('.camera-input'); // Could be the original or replaced one
        const imagePreview = modalElement.querySelector('.image-preview');

        const file = cameraInput?.files[0] || fileInput?.files[0];

        if (!file) return null;
        return {
            file,
            type: file.type,
            name: file.name,
            size: file.size,
            preview: (file.type.startsWith('image/') && imagePreview?.src && !imagePreview.classList.contains('d-none')) ? imagePreview.src : null
        };
    },

    resetAttachments(modalElement) {
        if (!modalElement) {
            console.warn('Modal element not provided to reset attachments');
            return;
        }
        // Query fresh elements here as this is an external call
        const fileInput = modalElement.querySelector('.file-input');
        const cameraInput = modalElement.querySelector('.camera-input');
        const attachmentPreview = modalElement.querySelector('.attachment-preview');
        const imagePreview = modalElement.querySelector('.image-preview');
        const attachmentName = modalElement.querySelector('.attachment-name');

        if (fileInput) fileInput.value = null;
        if (cameraInput) cameraInput.value = null; // Also reset the current camera input

        if (attachmentPreview) attachmentPreview.classList.add('d-none');
        if (imagePreview) {
            imagePreview.classList.add('d-none');
            imagePreview.src = '';
        }
        if (attachmentName) attachmentName.textContent = '';

        // Note: If cameraInput was replaced, `modalElement.querySelector('.camera-input')` gets the *current* one.
        // The original `elements.cameraInput` reference inside initializeAttachmentHandlers's closure would have been updated.
    },

    attachmentInputTemplate() {
        // Unchanged
        return `
            <div class="attachment-section mb-4 mt-3">
                <button type="button" class="btn btn-outline-warning text-white w-100 attach-file-btn d-flex align-items-center justify-content-center mb-3">
                    <i class="bi bi-paperclip text-white me-2"></i> Add Attachment
                </button>
                <small class="text-white d-block mb-3">
                    <i class="bi bi-info-circle me-1"></i>
                    Images, PDF, DOC/DOCX (Max: 5MB)
                </small>
                <input type="file" class="file-input d-none" accept="image/*,.pdf,.doc,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document">
                <input type="file" class="camera-input d-none" accept="image/*" capture="environment">
                <div class="attachment-preview mt-3 d-none">
                    <div class="card border-primary">
                        <div class="card-body p-3">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-file-earmark me-2 text-primary"></i>
                                <span class="attachment-name flex-grow-1 text-truncate"></span>
                                <button type="button" class="btn btn-sm btn-outline-danger remove-attachment d-flex align-items-center ms-2">
                                    <i class="bi bi-trash me-2"></i> Remove
                                </button>
                            </div>
                            <img class="image-preview mt-3 img-fluid rounded d-none" alt="Preview">
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
};