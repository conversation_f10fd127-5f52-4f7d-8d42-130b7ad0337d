# Login Page Merchant ID Implementation

## Overview
Updated the login page to comply with the API documentation by adding support for the `merchant_id` field, which must be a 4-6 digit numeric value as required for merchant users.

## Changes Made

### 1. Updated Login Form Fields
**File**: `src/views/auth/login.js`

- Added `merchant_id` field to the `formFields` configuration
- Field specifications:
  - **Type**: text with numeric input mode
  - **Validation**: 4-6 digit numeric pattern
  - **Icon**: Building icon (bi-building)
  - **Placeholder**: "Enter 4-6 digit merchant code"
  - **Required**: Yes
  - **Pattern**: `[0-9]{4,6}`
  - **Min/Max length**: 4-6 characters

### 2. Enhanced Form Field Rendering
- Updated `getFormField()` method to handle additional validation attributes
- Added support for `pattern`, `maxlength`, `minlength` attributes
- Added `inputmode="numeric"` for better mobile experience
- Added helpful text for merchant ID field
- Added remember functionality for merchant ID

### 3. Updated Login Form Layout
- Modified `getLoginForm()` to include merchant_id field between username and password
- Updated remember me checkbox logic to check for both username and merchant_id
- Maintained consistent styling with other form fields

### 4. Enhanced Login Processing
**Updated `handleSignin()` method**:
- Added merchant_id extraction from form
- Added client-side validation for 4-6 digit numeric format
- Updated API call to include merchant_id parameter
- Added merchant_id to localStorage storage
- Enhanced remember me functionality to store merchant_id

### 5. Local Storage Management
**New items stored**:
- `merchant_id`: The merchant identification code
- `remembered-merchant-id`: For remember me functionality

**Updated remember me logic**:
- Stores both username and merchant_id when checked
- Removes both when unchecked
- Pre-fills both fields on subsequent visits

### 6. Client-Side Validation
- Real-time input validation for merchant_id field
- Numeric-only input enforcement
- Visual feedback with valid/invalid states
- Length validation (4-6 digits)
- Pattern validation using regex

### 7. Enhanced Error Handling
- Added specific error message for invalid merchant_id format
- Updated form re-rendering logic
- Maintained existing error handling for API responses

### 8. Visual Enhancements
**Added CSS styles** in `assets/css/asthetic.css`:
- Validation state styling for input groups
- Green border/shadow for valid inputs
- Red border/shadow for invalid inputs
- Color-coded input group text icons

## Technical Implementation Details

### Form Field Configuration
```javascript
merchant_id: {
    id: 'merchant_id',
    label: 'Merchant ID',
    type: 'text',
    placeholder: 'Enter 4-6 digit merchant code',
    required: true,
    icon: 'bi-building',
    pattern: '[0-9]{4,6}',
    maxlength: 6,
    minlength: 4
}
```

### API Integration
The login request now includes:
```javascript
const response = await ApiService.login({ 
    username, 
    password, 
    merchant_id 
});
```

### Validation Logic
```javascript
// Client-side validation
if (!/^[0-9]{4,6}$/.test(merchant_id)) {
    // Show error and prevent submission
}

// Real-time input validation
merchantIdInput.addEventListener('input', (e) => {
    // Only allow numeric input
    e.target.value = value.replace(/[^0-9]/g, '');
    // Visual feedback for valid/invalid state
});
```

### Local Storage Structure
```javascript
// Stored on successful login
localStorage.setItem('merchant_id', merchant_id);

// Remember me functionality
if (rememberMe) {
    localStorage.setItem('remembered-username', username);
    localStorage.setItem('remembered-merchant-id', merchant_id);
}
```

## User Experience Improvements

### 1. Input Validation
- **Real-time feedback**: Input turns green when valid (4-6 digits)
- **Error prevention**: Only numeric characters allowed
- **Clear guidance**: Helpful text explains the requirement

### 2. Remember Me Enhancement
- Remembers both username and merchant ID
- Pre-fills both fields on return visits
- Single checkbox controls both fields

### 3. Error Messages
- Specific validation messages for merchant ID
- Clear indication of format requirements
- Consistent error styling with existing design

### 4. Mobile Optimization
- `inputmode="numeric"` triggers numeric keypad on mobile
- Proper autocomplete attributes for better UX
- Responsive design maintained

## API Compliance

### Request Format
The login request now matches the API documentation:
```json
{
  "username": "your_username",
  "password": "your_password",
  "merchant_id": "123456"
}
```

### Response Handling
The implementation properly handles the API response which includes:
- `merchant_code`: The merchant identification
- `merchant_id`: Internal merchant ID
- `merchant_name`: Merchant display name

## Security Considerations

### 1. Input Validation
- Client-side validation for immediate feedback
- Server-side validation enforced by API
- Pattern matching prevents invalid formats

### 2. Data Storage
- Merchant ID stored securely in localStorage
- No sensitive data exposed in client-side code
- Proper cleanup on logout

### 3. Error Handling
- Generic error messages to prevent information disclosure
- Proper validation before API calls
- Graceful degradation for network issues

## Testing Recommendations

### 1. Functional Testing
- Test with valid 4-6 digit merchant IDs
- Test with invalid formats (letters, too short/long)
- Test remember me functionality
- Test form submission and API integration

### 2. UI/UX Testing
- Verify visual feedback for validation states
- Test mobile numeric keypad activation
- Verify responsive design on all screen sizes
- Test error message display and dismissal

### 3. Integration Testing
- Test API integration with merchant_id parameter
- Verify localStorage storage and retrieval
- Test login flow end-to-end
- Verify proper error handling for API responses

## Backward Compatibility
- All existing functionality preserved
- No breaking changes to current login flow
- Enhanced features are additive only
- Existing users can continue using the system seamlessly
