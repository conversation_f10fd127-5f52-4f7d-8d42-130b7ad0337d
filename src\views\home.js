import { attachmentHandlers } from '../utils/attachmentHandlers.js';
import { referenceHandlers } from '../utils/referenceHandlers.js';
import { statsHandlers } from '../utils/statsHandlers.js';
import { networkHandlers } from '../utils/networkHandlers.js';

export default class HomeView {
    constructor() {
        this.header = null;
        this.sidebar = null;
        this.loadComponents();

        // Bind the methods to preserve this context
        this.initializeAttachmentHandlers = attachmentHandlers.initializeAttachmentHandlers.bind(this);
        this.getAttachmentData = attachmentHandlers.getAttachmentData.bind(this);
        this.resetAttachments = attachmentHandlers.resetAttachments.bind(this);
        this.attachmentInputTemplate = attachmentHandlers.attachmentInputTemplate.bind(this);

        this.collectExtraReferences = referenceHandlers.collectExtraReferences.bind(this);
        this.generateExtraRefsHTML = referenceHandlers.generateExtraRefsHTML.bind(this);
        this.resetExtraReferences = referenceHandlers.resetExtraReferences.bind(this);

        this.initMainContentTime = statsHandlers.initMainContentTime.bind(this);
        this.storeQuickStats = statsHandlers.storeQuickStats.bind(this);
        this.populateQuickStats = statsHandlers.populateQuickStats.bind(this);
        this.updateLastTransaction = statsHandlers.updateLastTransaction.bind(this);
        this.storeLastTransaction = statsHandlers.storeLastTransaction.bind(this);
        this.updateLastTransactionUI = statsHandlers.updateLastTransactionUI.bind(this);

        this.createTransactionStepOne = networkHandlers.createTransactionStepOne.bind(this);
        this.createTransactionStepTwo = networkHandlers.createTransactionStepTwo.bind(this);
        this.fetchBranchID = networkHandlers.fetchBranchID.bind(this);
        this.verifyVoucherCode = networkHandlers.verifyVoucherCode.bind(this);
        this.redeemVoucher = networkHandlers.redeemVoucher.bind(this);
        this.verifyGiftVoucherCode = networkHandlers.verifyGiftVoucherCode.bind(this);
        this.redeemGiftVoucher = networkHandlers.redeemGiftVoucher.bind(this);
        this.confirmGiftRedeemModal = this.confirmGiftRedeemModal.bind(this);
    }

    async loadComponents() {
        const [headerModule, sidebarModule] = await Promise.all([
            import('../components/header.js'),
            import('../components/sidebar.js')
        ]);

        this.header = new headerModule.default();
        this.sidebar = new sidebarModule.default();
        this.updateView();
    }

    getPageHeader() {
        return `
            <div class="row mt-5">
                <div class="col-lg-12 col-md-12 col-12 py-5 px-4">
                    <!-- Page header -->
                    <div class="row mb-4 align-items-center px-1 py-3 rounded shadow-sm bg-white-soft">
                        <div class="col-md-6 mb-3 mb-md-0">
                            <i class="bi bi-cash-stack text-white fs-2 me-3"></i>
                            <div>
                                <h3 class="mb-0 fw-bold text-white">Paypoint</h3>
                                <small class="text-white">Merchant Cashier Portal</small>
                            </div>
                        </div>
                        <div class="col-md-6 d-flex flex-column gap-2">
                            <button id="initiate_transaction" class="btn btn-primary btn-lg w-100 d-flex align-items-center justify-content-center gap-2 shadow-sm">
                                <i class="bi bi-plus-circle me-2 fs-5"></i>
                                Initiate Payment Voucher
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    getMainContent() {
        return `
            <div class="row">
                <div class="col-lg-12 col-md-12 col-12">
                    <div class="card h-100 border-0 shadow-lg">
                        <div class="card-body py-5 px-4">
                            <div class="row mb-4 align-items-center">
                                <div class="col-md-6 mb-3 mb-md-0">
                                    <h3 class="mb-1 fw-bold text-white" id="greeting">
                                        <i class="bi bi-hand-thumbs-up text-white me-2"></i>
                                        Good Morning
                                    </h3>
                                    <small class="text-white">You can start transacting</small>
                                </div>
                                <div class="col-md-6 d-flex flex-column gap-2">
                                    <button id="redeem_voucher" class="btn btn-primary btn-lg w-100 d-flex align-items-center justify-content-center gap-2 shadow-sm">
                                        <i class="bi bi-arrow-repeat me-2"></i>
                                        Redeem Voucher
                                    </button>
                                    <button id="redeem_gift_voucher" class="btn btn-success btn-lg w-100 d-flex align-items-center justify-content-center gap-2 shadow-sm">
                                        <i class="bi bi-gift me-2"></i>
                                        Redeem Gift Voucher
                                    </button>
                                </div>
                            </div>
                            <hr>
                            <div class="row g-4">
                                <div class="col-md-4">
                                    <div class="card border-0 shadow-sm h-100">
                                        <div class="card-body d-flex align-items-start flex-column bg-dark rounded-3">
                                            <div class="d-flex align-items-center w-100 mb-2">
                                                <i class="bi bi-graph-up-arrow text-info fs-1 me-3"></i>
                                                <div>
                                                    <h5 class="fw-bold mb-0 text-white">Quick Stats</h5>
                                                    <small class="text-white">Today's Summary</small>
                                                </div>
                                            </div>
                                            <div class="mt-3 w-100 small text-muted">
                                                <div><span class="fw-semibold text-white">Transactions:</span> <span class="text-white" id="txn_count">--</span></div>
                                                <div><span class="fw-semibold text-white">Total Value:</span> <span class="text-white" id="txn_total">--</span></div>
                                                <div><span class="fw-semibold text-white">Success Rate:</span> <span class="text-white" id="txn_success">--</span></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <div class="card border-0 shadow-sm h-100">
                                        <div class="card-body d-flex align-items-start flex-column bg-dark rounded-3">
                                            <div class="d-flex align-items-center w-100 mb-2">
                                                <i class="bi bi-receipt-cutoff text-success fs-1 me-3"></i>
                                                <div>
                                                    <h5 class="fw-bold mb-0 text-white">Last Transaction</h5>
                                                    <small class="text-white">Most recent activity</small>
                                                </div>
                                            </div>
                                            <div class="mt-3 w-100 small text-muted">
                                                <div><span class="fw-semibold text-white">Type:</span> <span class="text-white" id="last_txn_type">--</span></div>
                                                <div><span class="fw-semibold text-white">Amount:</span> <span class="text-white" id="last_txn_amount">--</span></div>
                                                <div><span class="fw-semibold text-white">Status:</span> <span class="text-white" id="last_txn_status">--</span></div>
                                                <div><span class="fw-semibold text-white">Time:</span> <span class="text-white" id="last_txn_time">--</span></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <div class="card border-0 shadow-sm h-100">
                                        <div class="card-body d-flex align-items-start flex-column bg-dark rounded-3">
                                            <div class="d-flex align-items-center w-100 mb-2">
                                                <i class="bi bi-calendar2-event text-warning fs-1 me-3"></i>
                                                <div>
                                                    <h5 class="fw-bold mb-0 text-white">Current Time</h5>
                                                    <small class="text-white">Date & Local Time</small>
                                                </div>
                                            </div>
                                            <div class="mt-3 w-100 small text-muted">
                                                <div><span class="fw-semibold text-white">Date:</span> <span class="text-white" id="current_date">--</span></div>
                                                <div><span class="fw-semibold text-white">Time:</span> <span class="text-white" id="current_time">--</span></div>
                                                <div><span class="fw-semibold text-white">Login Time:</span> <span class="text-white" id="session_start">--</span></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>
                            <div class="alert alert-info alert-dismissible fade show d-flex align-items-center mt-5" role="alert">
                                <i class="bi bi-info-circle me-2 fs-5"></i>
                                <div>
                                    <span class="fw-bold">Tip:</span> Use the <span class="text-primary">Initiate Payment Voucher</span> button above to start a new transaction, <span class="text-primary">Redeem Voucher</span> to process a customer voucher, or <span class="text-primary">Redeem Gift Voucher</span> to process gift vouchers.
                                </div>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    newVoucherRedeemModal() {
        // Enhanced modal HTML with icons and better UX
        return `
            <div class="modal fade newTransaction" id="newVoucherModal" tabindex="-1" role="dialog" aria-labelledby="newVoucherModalTitle" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
                    <div class="modal-content shadow-lg border-0">
                        <div class="modal-header bg-light">
                            <h5 class="modal-title text-white d-flex align-items-center" id="newVoucherModalTitle">
                                <i class="bi bi-credit-card-2-front me-2 text-white fs-4"></i>
                                Redeem Voucher
                            </h5>
                            <button type="button" class="btn-close btn-close-white text-white" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <label class="form-label fw-bold" for="payment_code">
                                    <i class="bi bi-upc-scan me-1 text-info"></i>PAYMENT CODE
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text bg-white" style="min-width: auto;"><i class="bi bi-key text-white"></i></span>
                                    <input type="text" maxlength="6" id="payment_code" class="form-control" placeholder="Enter Payment Code" autocomplete="off">
                                </div>
                                <small class="form-text text-white ms-2">
                                    Enter the 6-digit code provided to the customer.
                                </small>
                            </div>
                            <hr>
                            <div class="alert alert-info d-flex align-items-center py-2 mb-0" role="alert">
                                <i class="bi bi-info-circle me-2"></i>
                                Please ensure the code is correct before proceeding.
                            </div>
                        </div>
                        <div class="modal-footer flex-column flex-sm-row gap-2">
                            <button type="button"
                                class="btn btn-danger w-100 w-sm-auto d-flex align-items-center justify-content-center text-white"
                                data-bs-dismiss="modal">
                                <i class="bi bi-x-circle me-2"></i>
                                <span>Cancel</span>
                            </button>
                            <button type="button" class="btn btn-primary w-100 w-sm-auto d-flex align-items-center justify-content-center" id="verify_voucher">
                                <i class="bi bi-shield-lock me-2"></i>
                                <span>Proceed</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    newGiftVoucherRedeemModal() {
        // Enhanced modal HTML with icons and better UX for gift vouchers
        return `
            <div class="modal fade newTransaction" id="newGiftVoucherModal" tabindex="-1" role="dialog" aria-labelledby="newGiftVoucherModalTitle" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
                    <div class="modal-content shadow-lg border-0">
                        <div class="modal-header bg-light">
                            <h5 class="modal-title text-white d-flex align-items-center" id="newGiftVoucherModalTitle">
                                <i class="bi bi-gift me-2 text-white fs-4"></i>
                                Redeem Gift Voucher
                            </h5>
                            <button type="button" class="btn-close btn-close-white text-white" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <label class="form-label fw-bold" for="gift_pin_code">
                                    <i class="bi bi-pin-angle me-1 text-info"></i>PIN CODE
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text bg-white" style="min-width: auto;"><i class="bi bi-key text-white"></i></span>
                                    <input type="text" maxlength="6" id="gift_pin_code" class="form-control" placeholder="Enter 6-digit PIN" autocomplete="off">
                                </div>
                                <small class="form-text text-white ms-2">
                                    Enter the 6-digit PIN code from the gift voucher.
                                </small>
                            </div>
                            <hr>
                            <div class="alert alert-info d-flex align-items-center py-2 mb-0" role="alert">
                                <i class="bi bi-info-circle me-2"></i>
                                Please ensure the PIN code is correct before proceeding.
                            </div>
                        </div>
                        <div class="modal-footer flex-column flex-sm-row gap-2">
                            <button type="button"
                                class="btn btn-danger w-100 w-sm-auto d-flex align-items-center justify-content-center text-white"
                                data-bs-dismiss="modal">
                                <i class="bi bi-x-circle me-2"></i>
                                <span>Cancel</span>
                            </button>
                            <button type="button" class="btn btn-success w-100 w-sm-auto d-flex align-items-center justify-content-center" id="verify_gift_voucher">
                                <i class="bi bi-shield-lock me-2"></i>
                                <span>Proceed</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    confirmGiftRedeemModal() {
        // Enhanced modal HTML with icons and better UX for gift voucher confirmation
        const modalHTML = `
            <div class="modal fade newTransaction" id="confirmGiftRedeemModal" tabindex="-1" role="dialog" aria-labelledby="confirmGiftRedeemModalTitle" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
                    <div class="modal-content shadow-lg border-0">
                        <div class="modal-header bg-light">
                            <h5 class="modal-title text-white d-flex align-items-center" id="confirmGiftRedeemModalTitle">
                                <i class="bi bi-gift me-2 text-white fs-4"></i>
                                Confirm Gift Voucher Redemption
                            </h5>
                            <button type="button" class="btn-close btn-close-white text-white" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <label class="form-label fw-bold text-white" for="gift_amount_used">
                                    <i class="bi bi-cash-coin me-1 text-white"></i>AMOUNT TO USE
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text bg-white" style="min-width: auto;"><i class="bi bi-currency-dollar text-white"></i></span>
                                    <input type="number" id="gift_amount_used" class="form-control" placeholder="Enter amount to use" step="0.01" min="0">
                                </div>
                                <small class="form-text text-muted ms-2">Enter the amount you want to use from this gift voucher.</small>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-bold text-white" for="gift_reference">
                                    <i class="bi bi-card-text me-1 text-white"></i>REFERENCE <span class="text-muted">(Optional)</span>
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text bg-white" style="min-width: auto;"><i class="bi bi-pencil text-white"></i></span>
                                    <input type="text" id="gift_reference" class="form-control" placeholder="Enter Reference">
                                </div>
                            </div>
                            <hr>
                            <div class="alert alert-info d-flex align-items-center py-2 mb-0" role="alert">
                                <i class="bi bi-info-circle me-2"></i>
                                Please confirm the amount and reference before authorizing gift voucher redemption.
                            </div>
                        </div>
                        <div class="modal-footer bg-light">
                            <button type="button" class="btn btn-outline-danger" data-bs-dismiss="modal">
                                <i class="bi bi-x-circle me-1"></i>Cancel
                            </button>
                            <button type="button" class="btn btn-success d-flex align-items-center" id="authorize_gift_redeem">
                                <i class="bi bi-shield-lock me-1"></i>
                                <span>Authorize</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if any
        const existingModal = document.getElementById('confirmGiftRedeemModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to body
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // Initialize and show modal
        const modal = new bootstrap.Modal(document.getElementById('confirmGiftRedeemModal'));
        modal.show();

        const authorizeGiftRedemption = document.getElementById('authorize_gift_redeem');
        if (authorizeGiftRedemption) {
            // Remove any existing listeners
            authorizeGiftRedemption.replaceWith(authorizeGiftRedemption.cloneNode(true));
            // Add new listener
            document.getElementById('authorize_gift_redeem')
            .addEventListener('click', (e) => {
                this.redeemGiftVoucher(e);
            });
        }
    }

    confirmRedeemModal(amount) {
        // Enhanced modal HTML with icons and better UX
        const modalHTML = `
            <div class="modal fade newTransaction" id="confirmRedeemModal" tabindex="-1" role="dialog" aria-labelledby="confirmRedeemModalTitle" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
                    <div class="modal-content shadow-lg border-0">
                        <div class="modal-header bg-light">
                            <h5 class="modal-title text-white d-flex align-items-center" id="confirmRedeemModalTitle">
                                <i class="bi bi-shield-check me-2 text-white fs-4"></i>
                                Confirm Details
                            </h5>
                            <button type="button" class="btn-close btn-close-white text-white" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <small class="d-none text-success" id="confirmSuccess">CUSTOMER PAYMENT WAS SUCCESSFUL.</small>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-bold text-white" for="v_amount">
                                    <i class="bi bi-cash-coin me-1 text-white"></i>AMOUNT TENDERED
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text bg-white" style="min-width: auto;"><i class="bi bi-currency-dollar text-white"></i></span>
                                    <input type="text" id="v_amount" class="form-control bg-light text-white" placeholder="Enter Amount" readonly>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-bold text-white" for="v_payable">
                                    <i class="bi bi-wallet2 me-1 text-white"></i>AMOUNT PAYABLE
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text bg-white" style="min-width: auto;"><i class="bi bi-calculator text-white"></i></span>
                                    <input type="number" id="v_payable" class="form-control" placeholder="Enter Amount">
                                </div>
                                <small class="form-text text-muted ms-2">Enter the amount to be paid out to the customer.</small>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-bold text-white" for="v_ref">
                                    <i class="bi bi-card-text me-1 text-white"></i>REFERENCE <span class="text-muted">(Optional)</span>
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text bg-white" style="min-width: auto;"><i class="bi bi-pencil text-white"></i></span>
                                    <input type="text" id="v_ref" class="form-control" placeholder="Enter Reference">
                                </div>
                            </div>
                            <hr>
                            <div class="alert alert-info d-flex align-items-center py-2 mb-0" role="alert">
                                <i class="bi bi-info-circle me-2"></i>
                                Please confirm the details before authorizing redemption.
                            </div>
                        </div>
                        <div class="modal-footer bg-light">
                            <button type="button" class="btn btn-outline-danger" data-bs-dismiss="modal">
                                <i class="bi bi-x-circle me-1"></i>Cancel
                            </button>
                            <button type="button" class="btn btn-primary d-flex align-items-center" id="authorize_redeem">
                                <i class="bi bi-shield-lock me-1"></i>
                                <span>Authorize</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if any
        const existingModal = document.getElementById('confirmRedeemModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to body
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // Initialize and show modal
        const modal = new bootstrap.Modal(document.getElementById('confirmRedeemModal'));
        document.getElementById('v_amount').value = amount;
        modal.show();

        const authorizeRedemption = document.getElementById('authorize_redeem');
        if (authorizeRedemption) {
            // Remove any existing listeners
            authorizeRedemption.replaceWith(authorizeRedemption.cloneNode(true));
            // Add new listener
            document.getElementById('authorize_redeem')
            .addEventListener('click', (e) => {
                this.redeemVoucher(e);
            });
        }
    }

    newTransactionModal() {
        // Enhanced modal HTML with icons and elite UI/UX
        return `
            <div class="modal fade newTransaction" id="newTransactionModal" tabindex="-1" role="dialog" aria-labelledby="newTransactionModalTitle" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
                    <div class="modal-content shadow-lg border-0">
                        <div class="modal-header bg-light">
                            <h5 class="modal-title text-white d-flex align-items-center" id="newTransactionModalTitle">
                                <i class="bi bi-credit-card me-2 text-white fs-4"></i>
                                New Payment Voucher
                            </h5>
                            <button type="button" class="btn-close btn-close-white text-white" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-4">
                                <div class="input-group">
                                    <span class="input-group-text" style="min-width: auto;"><i class="bi bi-phone me-2 text-primary"></i></span>
                                    <input type="tel" maxlength="8" class="form-control" id="phone" placeholder="Enter customer's phone number">
                                </div>
                                <small class="form-text text-white ms-2">
                                    Enter an 8-digit Swazi phone number.
                                </small>
                            </div>
                            <div class="mb-4">
                                <div class="input-group">
                                    <span class="input-group-text" style="min-width: auto;"><i class="bi bi-currency-exchange me-2 text-primary"></i></span>
                                    <input type="number" id="amount" class="form-control" placeholder="Enter Amount">
                                </div>
                            </div>
                            <div class="mb-4">
                                <div class="input-group">
                                    <span class="input-group-text" style="min-width: auto;"><i class="bi bi-wallet2 me-2 text-primary"></i></span>
                                    <select class="form-select" id="pay_from">
                                        <option value="">Please select payment platform</option>
                                        <option value="MOMO">Mobile Money</option>
                                    </select>
                                </div>
                            </div>
                            <div class="mb-4">
                                <div class="input-group">
                                    <span class="input-group-text" style="min-width: auto;"><i class="bi bi-receipt me-2 text-primary"></i></span>
                                    <input type="text" id="default_ref" class="form-control" placeholder="Enter Payment Reference">
                                </div>
                            </div>
                            <div class="accordion mb-4" id="advancedOptionsAccordion">
                                <div class="accordion-item border-0">
                                    <h2 class="accordion-header" id="advancedOptionsHeading">
                                        <button class="accordion-button collapsed bg-light py-2" type="button" data-bs-toggle="collapse"
                                            data-bs-target="#advancedOptionsCollapse" aria-expanded="false" aria-controls="advancedOptionsCollapse" style="min-height: 48px;">
                                            <div class="d-flex align-items-center">
                                                <span class="input-group-text bg-transparent border-0 p-0 me-2" style="height: 38px;">
                                                    <i class="bi bi-ui-checks-grid text-white"></i>
                                                </span>
                                                <div>
                                                    <span class="fw-bold d-block text-white">Additional Details</span>
                                                </div>
                                            </div>
                                        </button>
                                    </h2>
                                    <div id="advancedOptionsCollapse" class="accordion-collapse collapse"
                                        aria-labelledby="advancedOptionsHeading" data-bs-parent="#advancedOptionsAccordion">
                                        <div class="accordion-body">
                                            <div class="mb-4" id="reference-container"></div>
                                            <button type="button" class="btn btn-outline-warning text-white w-100 mt-2" id="addReference">
                                                <i class="bi bi-plus-circle text-white me-2"></i>Add Extra Reference
                                            </button>
                                            ${this.attachmentInputTemplate()}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <hr>
                            <div class="alert alert-info d-flex align-items-center py-2 mb-0" role="alert">
                                <i class="bi bi-info-circle me-2"></i>
                                Please verify all details before creating the voucher.
                            </div>
                        </div>
                        <div class="modal-footer flex-column flex-sm-row gap-2">
                            <button type="button" class="btn btn-danger w-100 w-sm-auto d-flex align-items-center justify-content-center" data-bs-dismiss="modal">
                                <i class="bi bi-x-circle me-1"></i>Cancel
                            </button>
                            <button type="button" class="btn btn-primary w-100 w-sm-auto d-flex align-items-center justify-content-center" id="confirm_transaction">
                                <i class="bi bi-check-circle-fill me-2"></i>
                                <span>Review & Continue</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    confirmTransactionModal(phone, amount, charge, total, payFrom, defReference, extraRefsHTML, attachmentData) {
        // Enhanced modal HTML with icons and elite UI/UX
        const modalHTML = `
            <div class="modal fade newTransaction" id="confirmTransactionModal" tabindex="-1" role="dialog" aria-labelledby="confirmTransactionModalTitle" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
                    <div class="modal-content shadow-lg border-0">
                        <div class="modal-header bg-light">
                            <h5 class="modal-title text-white d-flex align-items-center" id="confirmTransactionModalTitle">
                                <i class="bi bi-shield-check me-2 text-white fs-4"></i>
                                Confirm Payment Details
                            </h5>
                            <button type="button" class="btn-close btn-close-white text-white" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-4">
                                <div class="input-group">
                                    <span class="input-group-text bg-white" style="min-width: auto;"><i class="bi bi-phone me-2 text-white"></i></span>
                                    <input type="text" id="c_phone" class="form-control bg-light text-white" readonly>
                                </div>
                            </div>
                            <div class="mb-4">
                                <div class="input-group">
                                    <span class="input-group-text bg-white" style="min-width: auto;"><i class="bi bi-wallet2 me-2 text-white"></i></span>
                                    <input type="text" id="c_pay_from" class="form-control bg-light text-white" readonly>
                                </div>
                            </div>
                            <div class="mb-4">
                                <div class="input-group">
                                    <span class="input-group-text bg-white" style="min-width: auto;"><i class="bi bi-currency-exchange me-2 text-white"></i></span>
                                    <input type="text" id="c_amount" class="form-control bg-light text-white" readonly>
                                </div>
                            </div>
                            <div class="mb-4">
                                <div class="input-group">
                                    <span class="input-group-text bg-white" style="min-width: auto;"><i class="bi bi-percent me-2 text-white"></i></span>
                                    <input type="text" id="c_charge" class="form-control bg-light text-white" readonly>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="input-group">
                                    <span class="input-group-text bg-white" style="min-width: auto;"><i class="bi bi-clipboard-check me-2 text-white"></i></span>
                                    <input type="text" id="c_total" class="form-control bg-success text-white fw-bold" readonly>
                                </div>
                            </div>
                            <div class="mb-4">
                                <div class="input-group">
                                    <span class="input-group-text bg-white" style="min-width: auto;"><i class="bi bi-receipt me-2 text-white"></i></span>
                                    <input type="text" id="c_ref" class="form-control bg-light text-white" readonly>
                                </div>
                            </div>
                            ${extraRefsHTML}
                            ${attachmentData ? `
                                <div class="mb-4">
                                    <label class="form-label d-flex align-items-center">
                                        <i class="bi bi-paperclip me-2 text-white"></i>
                                        <span>ATTACHMENT</span>
                                    </label>
                                    <div class="card border-primary">
                                        <div class="card-body p-3">
                                            <div class="d-flex align-items-center">
                                                <i class="bi bi-file-earmark me-2 text-white"></i>
                                                <span class="flex-grow-1 text-truncate">${attachmentData.name}</span>
                                            </div>
                                            ${attachmentData.type.startsWith('image/') ? `
                                                <img src="${attachmentData.preview}" class="mt-3 img-fluid rounded" style="max-height: 200px; width: auto;" alt="Preview">
                                            ` : ''}
                                        </div>
                                    </div>
                                </div>
                            ` : ''}
                            <hr>
                            <div class="alert alert-info d-flex align-items-center py-2 mb-0" role="alert">
                                <i class="bi bi-info-circle me-2"></i>
                                Please confirm the payment details before requesting authorization.
                            </div>
                        </div>
                        <div class="modal-footer flex-column flex-sm-row gap-2">
                            <button type="button"
                                class="btn btn-info w-100 w-sm-auto d-flex align-items-center justify-content-center text-white"
                                data-bs-dismiss="modal"
                                data-bs-toggle="modal"
                                data-bs-target="#newTransactionModal">
                                <i class="bi bi-pencil me-2"></i>
                                <span>Edit</span>
                            </button>
                            <button type="button" class="btn btn-primary w-100 w-sm-auto d-flex align-items-center justify-content-center" id="authorize_transaction">
                                <i class="bi bi-shield-lock me-2"></i>
                                <span>Request Authorization</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if any
        const existingModal = document.getElementById('confirmTransactionModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to body
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // Initialize and show modal
        const modal = new bootstrap.Modal(document.getElementById('confirmTransactionModal'));
        document.getElementById('c_charge').value = charge;
        document.getElementById('c_total').value  = total;
        document.getElementById('c_phone').value = phone;
        document.getElementById('c_amount').value = amount;
        document.getElementById('c_ref').value = defReference;
        document.getElementById('c_pay_from').value = payFrom;
        modal.show();

        const authorizeTransaction = document.getElementById('authorize_transaction');
        if (authorizeTransaction) {
            // Remove any existing listeners
            authorizeTransaction.replaceWith(authorizeTransaction.cloneNode(true));
            // Add new listener
            document.getElementById('authorize_transaction')
            .addEventListener('click', (e) => {
                this.createTransactionStepTwo(e);
            });
        }
    }

    successModal(title = 'Voucher Created', paymentInfo) {
        // Elite UI/UX: animated icon, clear info, action button, and modern layout
        const modalHTML = `
            <div class="modal fade" id="successModal" tabindex="-1" role="dialog" aria-labelledby="successModalTitle" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
                    <div class="modal-content border-0 shadow-lg">
                        <div class="modal-header bg-success bg-opacity-10 border-0">
                            <h5 class="modal-title text-white d-flex align-items-center" id="successModalTitle">
                                <i class="bi bi-patch-check-fill me-2 fs-2 text-white animate__animated animate__bounceIn"></i>
                                ${title}
                            </h5>
                            <button type="button" class="btn-close btn-close-white text-white" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body text-center">
                            <div class="mb-4">
                                <i class="bi bi-emoji-smile fs-1 text-success animate__animated animate__tada"></i>
                            </div>
                            <div class="mb-3">
                                <p class="lead fw-bold text-dark">${paymentInfo}</p>
                            </div>
                            <div class="alert alert-success d-flex align-items-center justify-content-center mb-0" role="alert">
                                <i class="bi bi-info-circle me-2"></i>
                                Transaction completed successfully. You may close this window.
                            </div>
                        </div>
                        <div class="modal-footer bg-light border-0 justify-content-center">
                            <button type="button" class="btn btn-success d-flex align-items-center px-4 fw-bold" data-bs-dismiss="modal">
                                <i class="bi bi-check-circle me-2"></i>
                                Done
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if any
        const existingModal = document.getElementById('successModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to body
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // Initialize and show modal
        const modal = new bootstrap.Modal(document.getElementById('successModal'));
        modal.show();
    }

    render() {
        return `
            <div id="main-wrapper" class="main-wrapper">
                <!-- Components will be loaded dynamically -->
            </div>
        `;
    }

    afterRender() {
        this.initializeEventListeners();
        if (window.feather) {
            feather.replace();
        }
    }

    updateView() {
        const mainWrapper = document.getElementById('main-wrapper');
        if (mainWrapper && this.header && this.sidebar) {
            mainWrapper.innerHTML = `
                ${this.header.render()}
                ${this.sidebar.render()}
                <div id="app-content">
                    <div class="app-content-area">
                        <div class="container-fluid">
                            ${this.getPageHeader()}
                            ${this.getMainContent()}
                            ${this.newTransactionModal()}
                            ${this.newVoucherRedeemModal()}
                            ${this.newGiftVoucherRedeemModal()}
                        </div>
                    </div>
                </div>
            `;
            
            // Initialize components after DOM update
            requestAnimationFrame(() => {
                this.header.afterRender();
                this.initializeEventListeners();
                this.setGreeting();
                this.fetchBranchID();
                this.initMainContentTime();
                this.updateLastTransactionUI();
                this.populateQuickStats();

                if (window.feather) {
                    feather.replace();
                }
            });
        }
    }

    initializeEventListeners() {
        const initiateTransaction = document.getElementById('initiate_transaction');
        if (initiateTransaction) {
            // Remove any existing listeners
            initiateTransaction.replaceWith(initiateTransaction.cloneNode(true));
            // Add new listener
            document.getElementById('initiate_transaction')
            .addEventListener('click', (e) => {
                //reset modal data
                document.getElementById("phone").value = "";
                document.getElementById("amount").value = "";
                document.getElementById("pay_from").value = "";
                document.getElementById('default_ref').value = "";
                const modalElement = document.getElementById('newTransactionModal');
                this.resetExtraReferences(modalElement);
                this.resetAttachments(modalElement);
                // Initialize and show modal
                const modal = new bootstrap.Modal(document.getElementById('newTransactionModal'));
                modal.show();
            });
        }

        const confirmTransaction = document.getElementById('confirm_transaction');
        if (confirmTransaction) {
            // Remove any existing listeners
            confirmTransaction.replaceWith(confirmTransaction.cloneNode(true));
            // Add new listener
            document.getElementById('confirm_transaction')
            .addEventListener('click', (e) => {this.createTransactionStepOne(e)});
        }

        const redeemVoucher = document.getElementById('redeem_voucher');
        if (redeemVoucher) {
            // Remove any existing listeners
            redeemVoucher.replaceWith(redeemVoucher.cloneNode(true));
            // Add new listener
            document.getElementById('redeem_voucher')
            .addEventListener('click', (e) => {
                const modal = new bootstrap.Modal(document.getElementById('newVoucherModal'));
                modal.show();
            });
        }
        
        const redeemGiftVoucher = document.getElementById('redeem_gift_voucher');
        if (redeemGiftVoucher) {
            // Remove any existing listeners
            redeemGiftVoucher.replaceWith(redeemGiftVoucher.cloneNode(true));
            // Add new listener
            document.getElementById('redeem_gift_voucher')
            .addEventListener('click', (e) => {
                const modal = new bootstrap.Modal(document.getElementById('newGiftVoucherModal'));
                modal.show();
            });
        }
        
        const verifyVoucher = document.getElementById('verify_voucher');
        if (verifyVoucher) {
            // Remove any existing listeners
            verifyVoucher.replaceWith(verifyVoucher.cloneNode(true));
            // Add new listener
            document.getElementById('verify_voucher')
            .addEventListener('click', (e) => {this.verifyVoucherCode(e)});
        }

        const verifyGiftVoucher = document.getElementById('verify_gift_voucher');
        if (verifyGiftVoucher) {
            // Remove any existing listeners
            verifyGiftVoucher.replaceWith(verifyGiftVoucher.cloneNode(true));
            // Add new listener
            document.getElementById('verify_gift_voucher')
            .addEventListener('click', (e) => {this.verifyGiftVoucherCode(e)});
        }

        // Dynamic "Add Reference" buttons
        let referenceCount = 1;
        const addReferenceBtn = document.getElementById("addReference");
        const referenceContainer = document.getElementById("reference-container");
        if (addReferenceBtn && referenceContainer) {
            // Use a new, cloned button to ensure old listeners are removed if this function is called multiple times
            const newAddReferenceBtn = addReferenceBtn.cloneNode(true);
            addReferenceBtn.parentNode.replaceChild(newAddReferenceBtn, addReferenceBtn);
            newAddReferenceBtn.addEventListener("click", () => {
                const inputId = `ref_${referenceCount}`;
                const newRefInput = document.createElement("input");
                newRefInput.type = "text";
                newRefInput.className = "form-control reference-input mt-2"; // Common class for collection
                newRefInput.placeholder = `Extra reference ${referenceCount}`;
                newRefInput.id = inputId;
                referenceContainer.appendChild(newRefInput);
                referenceCount++;
            });
        }

        // Initialize attachment handlers
        document.getElementById('newTransactionModal')?.addEventListener('shown.bs.modal', (event) => {
            // Pass the modal DOM element, not 'this'
            this.initializeAttachmentHandlers(event.target);
        });
    }

    setGreeting() {
        const username = localStorage.getItem('username') || '';
        const hour = new Date().getHours();
        const greeting =
            hour < 12
                ? "Good morning"
                : hour < 18
                ? "Good afternoon"
                : "Good evening";
    
        const greetingElement = document.getElementById("greeting");
        greetingElement.textContent = `${greeting} ${username}`;
    }
}