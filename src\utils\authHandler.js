import { BASE_URL, navigateTo } from '../router.js';

export const authHandler = {
    clearAuthData() {
        // Only clear session/auth-related keys, preserve remembered credentials
        localStorage.removeItem('user-token');
        localStorage.removeItem('nav-toggled');
        localStorage.removeItem('is_authenticated');
        localStorage.removeItem('username');
        localStorage.removeItem('user_id');
        localStorage.removeItem('merchant_code');
        // Do NOT remove 'remembered-username' and 'remembered-merchant-id'
        sessionStorage.clear();
        // Reset global auth state
        window.is_authenticated = false;
    },

    logout() {
        try {
            // Clear all auth data
            this.clearAuthData();

            // Clear navigation state
            localStorage.removeItem('nav-toggled');

            // Clear any running intervals or timeouts
            const highestTimeoutId = setTimeout(";");
            for (let i = 0; i < highestTimeoutId; i++) {
                clearTimeout(i);
            }

            // Navigate to login
            navigateTo(`${BASE_URL}/login`);
            
        } catch (error) {
            console.error('Logout error:', error);
            // Fallback: force reload to login
            navigateTo(`${BASE_URL}/login`);
        }
    }
};