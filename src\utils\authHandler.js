import { BASE_URL, navigateTo } from '../router.js';
import { modalManager } from './modalManager.js';

export const authHandler = {
    // Track session timeout
    sessionTimeoutId: null,
    sessionWarningTimeoutId: null,

    clearAuthData() {
        // Only clear session/auth-related keys, preserve remembered credentials
        localStorage.removeItem('user-token');
        localStorage.removeItem('nav-toggled');
        localStorage.removeItem('is_authenticated');
        localStorage.removeItem('username');
        localStorage.removeItem('user_id');
        localStorage.removeItem('merchant_code');
        // Do NOT remove 'remembered-username' and 'remembered-merchant-id'
        sessionStorage.clear();
        // Reset global auth state
        window.is_authenticated = false;
    },

    logout() {
        try {
            // Clear session timeouts
            this.clearSessionTimeouts();

            // Clear all auth data
            this.clearAuthData();

            // Clear navigation state
            localStorage.removeItem('nav-toggled');

            // Clear any running intervals or timeouts
            const highestTimeoutId = setTimeout(";");
            for (let i = 0; i < highestTimeoutId; i++) {
                clearTimeout(i);
            }

            // Clean up any active modals
            modalManager.cleanupAllModals();

            // Navigate to login
            navigateTo(`${BASE_URL}/login`);

        } catch (error) {
            console.error('Logout error:', error);
            // Fallback: force reload to login
            navigateTo(`${BASE_URL}/login`);
        }
    },

    /**
     * Enhanced session timeout handling with user confirmation
     * @param {number} timeoutMinutes - Minutes until timeout
     * @param {number} warningMinutes - Minutes before showing warning
     */
    initSessionTimeout(timeoutMinutes = 30, warningMinutes = 5) {
        // Clear any existing timeouts
        this.clearSessionTimeouts();

        const timeoutMs = timeoutMinutes * 60 * 1000;
        const warningMs = (timeoutMinutes - warningMinutes) * 60 * 1000;

        // Set warning timeout
        this.sessionWarningTimeoutId = setTimeout(() => {
            this.showSessionWarning();
        }, warningMs);

        // Set logout timeout
        this.sessionTimeoutId = setTimeout(() => {
            this.handleSessionTimeout();
        }, timeoutMs);
    },

    /**
     * Show session warning popup
     */
    async showSessionWarning() {
        try {
            const result = await modalManager.safeSweetAlert({
                icon: 'warning',
                title: 'Session Warning',
                text: 'Your session will expire in 5 minutes. Do you want to extend your session?',
                confirmButtonText: 'Extend Session',
                cancelButtonText: 'Logout Now',
                showCancelButton: true,
                allowOutsideClick: false,
                allowEscapeKey: false,
                reverseButtons: true,
                timer: 300000, // 5 minutes
                timerProgressBar: true
            });

            if (result.isConfirmed) {
                // User wants to extend session
                this.extendSession();
            } else if (result.isDismissed) {
                // Timer expired or user cancelled
                this.handleSessionTimeout();
            }
        } catch (error) {
            console.error('Error showing session warning:', error);
            // Fallback: extend session on error
            this.extendSession();
        }
    },

    /**
     * Handle session timeout with user confirmation
     */
    async handleSessionTimeout() {
        try {
            // Clear any remaining timeouts
            this.clearSessionTimeouts();

            // Show timeout popup and wait for user confirmation
            await modalManager.showTimeoutPopup(() => {
                this.logout();
            }, 'Your session has expired. You will be logged out.');

        } catch (error) {
            console.error('Error handling session timeout:', error);
            // Fallback: force logout
            this.logout();
        }
    },

    /**
     * Extend the current session
     */
    extendSession() {
        console.log('Session extended');
        // Restart session timeout
        this.initSessionTimeout();

        // Optionally make an API call to refresh the token
        // This would depend on your backend implementation
    },

    /**
     * Clear all session timeouts
     */
    clearSessionTimeouts() {
        if (this.sessionTimeoutId) {
            clearTimeout(this.sessionTimeoutId);
            this.sessionTimeoutId = null;
        }
        if (this.sessionWarningTimeoutId) {
            clearTimeout(this.sessionWarningTimeoutId);
            this.sessionWarningTimeoutId = null;
        }
    },

    /**
     * Reset session timeout (call this on user activity)
     */
    resetSessionTimeout() {
        if (window.is_authenticated) {
            this.initSessionTimeout();
        }
    }
};