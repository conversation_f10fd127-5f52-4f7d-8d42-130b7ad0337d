.auth-card {
    background-color: #0A3D72;
    border-radius: 15px; /* Keep a smooth normal rounding */
    border: 2px solid #06294D;
    padding: 20px;
    box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

/* Optional: Remove these pseudo-elements completely */
.auth-card::before,
.auth-card::after {
    content: none;
}

/* seach box */

  .tt-menu {
    width: 100%;
    background-color: white;
    border: 1px solid #ced4da;
    border-radius: 0.5rem;
    padding: 0.5rem;
    z-index: 1000;
  }

  .tt-suggestion {
    padding: 0.5rem;
    cursor: pointer;
  }

  .tt-suggestion:hover {
    background-color: #f1f1f1;
  }

  /*Grid js table*/
  .gridjs-wrapper {
    overflow-x: auto;
  }  
  
  @media screen and (max-width: 600px) {
    .gridjs-table td,
    .gridjs-table th {
      white-space: nowrap;
    }
  
    .gridjs-wrapper {
      font-size: 12px;
    }
  }
  
/*sidebar ui enhancement*/
.navbar-vertical {
    transition: all 0.2s ease;
}

.nav-link {
    transition: all 0.2s ease;
}

.nav-link:hover {
    background-color: var(--bs-primary-bg-subtle);
}

.nav-link.active {
    background-color: var(--bs-primary-bg-subtle);
    color: var(--bs-primary);
}

.rotate-90 {
    transform: rotate(90deg);
    transition: transform 0.2s ease;
}

.nav-icon-wrapper {
    transition: all 0.2s ease;
}

.navbar-heading {
    letter-spacing: 0.05em;
}

/*OTP page styles*/
.auth-card {
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.auth-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0,0,0,.1) !important;
}

[data-otp-input] {
    transition: all 0.2s ease;
}

[data-otp-input]:focus {
    border-color: var(--bs-primary);
    transform: translateY(-2px);
}

.phone-badge {
    font-weight: 500;
}

.btn-primary {
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(var(--bs-primary-rgb), 0.25);
}

#resendCode {
    transition: opacity 0.3s ease;
}

/*Header styles*/
.navbar-custom {
    backdrop-filter: blur(10px);
    background-color: #0A3D72;
    border-bottom: 1px solid rgba(0,0,0,.05);
    padding: 0.5rem 0;
}

.nav-icon-wrapper {
    transition: all 0.2s ease;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-link:hover .nav-icon-wrapper {
    transform: translateY(-1px);
}

.avatar-md {
    width: 38px;
    height: 38px;
}

.avatar-lg {
    width: 48px;
    height: 48px;
}

.dropdown-menu {
    animation: dropdownAnimation 0.2s ease;
}

.dropdown-submenu {
    position: relative;
}

.dropdown-submenu .submenu {
    display: none;
    position: absolute;
    left: 100%;
    top: 0;
    min-width: 220px;
    margin-left: 1px;
    border-radius: 0.5rem;
    background-color: var(--bs-dropdown-bg);
    padding: 0.5rem 0;
}

.dropdown-submenu:hover .submenu,
.dropdown-submenu .submenu.show {
    display: block;
}

.dropdown-item {
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background-color: var(--bs-primary-bg-subtle);
}

.dropdown-item.active {
    color: var(--bs-primary);
    background-color: var(--bs-primary-bg-subtle);
}

.dropdown-icon-wrapper {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

@keyframes dropdownAnimation {
    from {
        opacity: 0;
        transform: translateY(-8px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

[data-bs-theme="dark"] .navbar-custom {
    background-color: rgba(33,37,41,.95);
    border-bottom-color: rgba(255,255,255,.05);
}


/* Public Receivers Grid Styling */
.gridjs-container {
    border: none !important;
    box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.075);
}

.gridjs-wrapper {
    border: none !important;
    box-shadow: none !important;
}

.gridjs-table {
    font-family: inherit !important;
}

.gridjs-footer {
    border: none !important;
    box-shadow: none !important;
}

.gridjs-pagination .gridjs-pages button {
    border-radius: 0.375rem;
    margin: 0 0.125rem;
    min-width: 32px;
    padding: 0.375rem;
    transition: all 0.2s ease-in-out;
}

.gridjs-pagination .gridjs-pages button:hover {
    background-color: var(--bs-primary-bg-subtle);
    border-color: var(--bs-primary);
    color: var(--bs-primary);
}

.gridjs-pagination .gridjs-pages button.gridjs-currentPage {
    background-color: var(--bs-primary);
    border-color: var(--bs-primary);
    color: white;
}

.pay-btn {
    transition: all 0.2s ease-in-out;
}

.pay-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 0.25rem 0.5rem rgba(var(--bs-primary-rgb), 0.15);
}

/* Transaction History Styling */
.accordion-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.transaction-icon {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.cancel-btn {
    transition: all 0.2s ease;
}

.cancel-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 0.25rem 0.5rem rgba(220, 53, 69, 0.15);
}

/* Status Badge Styling */
.badge {
    font-weight: 500;
    padding: 0.5rem 0.75rem;
}

.badge i {
    opacity: 0.75;
}

/*theme dropdown*/
.theme-dropdown {
    position: relative;
}

.theme-dropdown .theme-submenu {
    position: absolute;
    inset: 0px auto auto 0px;
    margin: 0px;
    transform: translate(0px, 48px);
    min-width: 220px;
    z-index: 1000;
}

@media (max-width: 576px) {
    .theme-dropdown .theme-submenu {
        position: static;
        transform: none;
        box-shadow: none !important;
        border-top: 1px solid var(--bs-border-color);
        border-bottom: 1px solid var(--bs-border-color);
        margin: 0.5rem 0;
    }
}

.theme-dropdown .collapse {
    padding-left: 3rem;
    background-color: var(--bs-light);
    border-radius: 0.5rem;
    margin: 0.5rem 0;
}

.theme-dropdown .collapse .dropdown-item {
    border-radius: 0.375rem;
    transition: all 0.2s ease;
}

.theme-dropdown .collapse .dropdown-item:hover {
    background-color: var(--bs-primary-bg-subtle);
}

.theme-dropdown .collapse .dropdown-item.active {
    background-color: var(--bs-primary-bg-subtle);
    color: var(--bs-primary);
}

[data-bs-theme="dark"] .theme-dropdown .collapse {
    background-color: rgba(255,255,255,0.05);
}

/*Popup styles*/
.swal2-mobile {
  width: 90% !important;
  font-size: 14px;
}

/* search box of gridjs table styles */
.gridjs-search {
  width: 100% !important;
  display: flex;
}

.gridjs-search input {
  width: 100% !important;
  flex-grow: 1;
}

/*used by file picker*/
.visually-hidden {
    opacity: 0;
    position: absolute;
    width: 1px;
    height: 1px;
    z-index: -1;
}

