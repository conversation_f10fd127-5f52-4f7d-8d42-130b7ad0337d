import { BASE_URL, navigateTo } from '../router.js';
import ApiService from '../services/api.js';

export default class ProfileView {
    constructor() {
        this.state = {
            profile: null,
            loading: true,
            error: {
                visible: false,
                message: ''
            },
            success: {
                visible: false,
                message: ''
            },
            editMode: false,
            passwordChangeMode: false
        };
    }

    async loadProfile() {
        try {
            this.state.loading = true;
            const userID = localStorage.getItem('user_id');
            const profile = await ApiService.getProfile(userID);
            this.state.profile = profile;
            this.state.loading = false;
            this.state.error = { visible: false, message: '' };
        } catch (error) {
            this.state.loading = false;
            this.state.error = {
                visible: true,
                message: 'Failed to load profile information. Please try again.'
            };
            console.error('Profile load error:', error);
        }
    }

    getErrorMessage() {
        if (!this.state.error.visible) return '';
        return `
            <div class="alert alert-danger alert-dismissible fade show mb-4" role="alert">
                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                ${this.state.error.message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;
    }

    getSuccessMessage() {
        if (!this.state.success.visible) return '';
        return `
            <div class="alert alert-success alert-dismissible fade show mb-4" role="alert">
                <i class="bi bi-check-circle-fill me-2"></i>
                ${this.state.success.message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;
    }

    getLoadingState() {
        return `
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-3 text-white">Loading profile information...</p>
            </div>
        `;
    }

    getProfileHeader() {
        if (!this.state.profile) return '';
        
        const { first_name, last_name, username, branch_name } = this.state.profile;
        const fullName = `${first_name || ''} ${last_name || ''}`.trim() || username;
        
        return `
            <div class="card border-0 shadow-lg hover-lift mb-4">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <div class="d-flex align-items-center">
                                <div class="stats-icon me-4">
                                    <i class="bi bi-person-circle text-white fs-1"></i>
                                </div>
                                <div>
                                    <h3 class="mb-1 fw-bold text-white">${fullName}</h3>
                                    <p class="text-small text-white mb-1 opacity-75">@${username}</p>
                                    <p class="text-xs text-white mb-0 opacity-50">
                                        <i class="bi bi-building me-1"></i>${branch_name}
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 text-md-end mt-3 mt-md-0">
                            <button class="btn btn-primary hover-lift interactive me-2" onclick="profileView.toggleEditMode()">
                                <i class="bi bi-pencil"></i>
                                <span class="fw-semibold">Edit Profile</span>
                            </button>
                            <button class="btn btn-outline-primary hover-lift interactive" onclick="profileView.togglePasswordMode()">
                                <i class="bi bi-key"></i>
                                <span class="fw-semibold">Change Password</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    getProfileDetails() {
        if (!this.state.profile) return '';
        
        const { first_name, last_name, phone, email, branch_name, merchant_id, user_id, branch_id } = this.state.profile;
        
        if (this.state.editMode) {
            return this.getEditForm();
        }

        return `
            <div class="row g-4">
                <div class="col-md-6">
                    <div class="card border-0 shadow-md h-100">
                        <div class="card-header">
                            <h5 class="mb-0 fw-semibold">
                                <i class="bi bi-person me-2 text-primary"></i>Personal Information
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label text-small fw-semibold text-white">First Name</label>
                                <p class="mb-0 fw-medium">${first_name || 'Not provided'}</p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label text-small fw-semibold text-white">Last Name</label>
                                <p class="mb-0 fw-medium">${last_name || 'Not provided'}</p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label text-small fw-semibold text-white">Phone Number</label>
                                <p class="mb-0 fw-medium">${phone || 'Not provided'}</p>
                            </div>
                            <div class="mb-0">
                                <label class="form-label text-small fw-semibold text-white">Email Address</label>
                                <p class="mb-0 fw-medium">${email || 'Not provided'}</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card border-0 shadow-md h-100">
                        <div class="card-header">
                            <h5 class="mb-0 fw-semibold">
                                <i class="bi bi-building me-2 text-primary"></i>Work Information
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label text-small fw-semibold text-white">Branch Name</label>
                                <p class="mb-0 fw-medium">${branch_name}</p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label text-small fw-semibold text-white">Branch ID</label>
                                <p class="mb-0 fw-medium">${branch_id}</p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label text-small fw-semibold text-white">Merchant ID</label>
                                <p class="mb-0 fw-medium">${merchant_id}</p>
                            </div>
                            <div class="mb-0">
                                <label class="form-label text-small fw-semibold text-white">User ID</label>
                                <p class="mb-0 fw-medium">${user_id}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    getEditForm() {
        if (!this.state.profile) return '';
        
        const { first_name, last_name, phone, email } = this.state.profile;
        
        return `
            <div class="card border-0 shadow-lg">
                <div class="card-header">
                    <h5 class="mb-0 fw-semibold">
                        <i class="bi bi-pencil me-2 text-primary"></i>Edit Profile Information
                    </h5>
                </div>
                <div class="card-body">
                    <form id="profileForm">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="first_name" class="form-label fw-semibold">First Name</label>
                                <input type="text" id="first_name" class="form-control focus-ring" 
                                       value="${first_name || ''}" placeholder="Enter your first name">
                            </div>
                            <div class="col-md-6">
                                <label for="last_name" class="form-label fw-semibold">Last Name</label>
                                <input type="text" id="last_name" class="form-control focus-ring" 
                                       value="${last_name || ''}" placeholder="Enter your last name">
                            </div>
                            <div class="col-md-6">
                                <label for="phone" class="form-label fw-semibold">Phone Number</label>
                                <input type="tel" id="phone" class="form-control focus-ring" 
                                       value="${phone || ''}" placeholder="Enter your phone number">
                            </div>
                            <div class="col-md-6">
                                <label for="email" class="form-label fw-semibold">Email Address</label>
                                <input type="email" id="email" class="form-control focus-ring" 
                                       value="${email || ''}" placeholder="Enter your email address">
                            </div>
                        </div>
                        <div class="d-flex gap-2 mt-4">
                            <button type="submit" class="btn btn-primary hover-lift interactive">
                                <i class="bi bi-check-circle"></i>
                                <span class="fw-semibold">Save Changes</span>
                            </button>
                            <button type="button" class="btn btn-outline-danger hover-lift interactive" 
                                    onclick="profileView.cancelEdit()">
                                <i class="bi bi-x-circle"></i>
                                <span class="fw-semibold">Cancel</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        `;
    }

    getPasswordChangeForm() {
        if (!this.state.passwordChangeMode) return '';
        
        return `
            <div class="card border-0 shadow-lg mt-4">
                <div class="card-header">
                    <h5 class="mb-0 fw-semibold">
                        <i class="bi bi-key me-2 text-primary"></i>Change Password
                    </h5>
                </div>
                <div class="card-body">
                    <form id="passwordForm">
                        <div class="row g-3">
                            <div class="col-12">
                                <label for="old_password" class="form-label fw-semibold">Current Password</label>
                                <input type="password" id="old_password" class="form-control focus-ring" 
                                       placeholder="Enter your current password" required>
                            </div>
                            <div class="col-md-6">
                                <label for="new_password" class="form-label fw-semibold">New Password</label>
                                <input type="password" id="new_password" class="form-control focus-ring" 
                                       placeholder="Enter new password" required minlength="6">
                            </div>
                            <div class="col-md-6">
                                <label for="confirm_password" class="form-label fw-semibold">Confirm New Password</label>
                                <input type="password" id="confirm_password" class="form-control focus-ring" 
                                       placeholder="Confirm new password" required minlength="6">
                            </div>
                        </div>
                        <div class="d-flex gap-2 mt-4">
                            <button type="submit" class="btn btn-primary hover-lift interactive">
                                <i class="bi bi-shield-check"></i>
                                <span class="fw-semibold">Change Password</span>
                            </button>
                            <button type="button" class="btn btn-outline-danger hover-lift interactive" 
                                    onclick="profileView.cancelPasswordChange()">
                                <i class="bi bi-x-circle"></i>
                                <span class="fw-semibold">Cancel</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        `;
    }

    render() {
        return `
            <div id="main-wrapper" class="main-wrapper">
                <!-- Components will be loaded dynamically -->
            </div>
        `;
    }

    async updateView() {
        const mainWrapper = document.getElementById('main-wrapper');
        if (mainWrapper) {
            const [headerModule, sidebarModule] = await Promise.all([
                import('../components/header.js'),
                import('../components/sidebar.js')
            ]);

            const header = new headerModule.default();
            const sidebar = new sidebarModule.default();

            mainWrapper.innerHTML = `
                ${header.render()}
                ${sidebar.render()}
                <div id="app-content">
                    <div class="app-content-area">
                        <div class="container-fluid">
                            <div class="row mt-5 animate-fade-in-up">
                                <div class="col-12">
                                    <div class="d-flex align-items-center mb-4">
                                        <div>
                                            <h2 class="mb-1 fw-bold text-white">Profile Management</h2>
                                            <p class="text-white mb-0">Manage your account information and settings</p>
                                        </div>
                                    </div>
                                    
                                    ${this.getErrorMessage()}
                                    ${this.getSuccessMessage()}
                                    
                                    ${this.state.loading ? this.getLoadingState() : ''}
                                    ${!this.state.loading ? this.getProfileHeader() : ''}
                                    ${!this.state.loading ? this.getProfileDetails() : ''}
                                    ${this.getPasswordChangeForm()}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // Initialize components after DOM update
            requestAnimationFrame(() => {
                header.afterRender();
                this.initializeEventListeners();
                if (window.feather) {
                    feather.replace();
                }
            });
        }
    }

    toggleEditMode() {
        this.state.editMode = !this.state.editMode;
        this.state.passwordChangeMode = false;
        this.updateView();
    }

    togglePasswordMode() {
        this.state.passwordChangeMode = !this.state.passwordChangeMode;
        this.state.editMode = false;
        this.updateView();
    }

    cancelEdit() {
        this.state.editMode = false;
        this.updateView();
    }

    cancelPasswordChange() {
        this.state.passwordChangeMode = false;
        this.updateView();
    }

    async handleProfileUpdate(event) {
        event.preventDefault();

        const formData = {
            first_name: document.getElementById('first_name').value.trim(),
            last_name: document.getElementById('last_name').value.trim(),
            phone: document.getElementById('phone').value.trim(),
            email: document.getElementById('email').value.trim()
        };

        // Basic validation
        if (formData.email && !this.isValidEmail(formData.email)) {
            this.showError('Please enter a valid email address.');
            return;
        }

        try {
            const userID = localStorage.getItem('user_id');
            await ApiService.updateProfile(userID, formData);

            // Update local state
            this.state.profile = { ...this.state.profile, ...formData };
            this.state.editMode = false;

            this.showSuccess('Profile updated successfully!');
            this.updateView();
        } catch (error) {
            console.error('Profile update error:', error);
            let errorMessage = 'Failed to update profile. Please try again.';

            if (error.status === 400) {
                errorMessage = error.message || 'Invalid data provided.';
            } else if (error.status === 401) {
                errorMessage = 'Session expired. Please log in again.';
                setTimeout(() => navigateTo(`${BASE_URL}/login`), 2000);
            }

            this.showError(errorMessage);
        }
    }

    async handlePasswordChange(event) {
        event.preventDefault();

        const oldPassword = document.getElementById('old_password').value;
        const newPassword = document.getElementById('new_password').value;
        const confirmPassword = document.getElementById('confirm_password').value;

        // Validation
        if (!oldPassword || !newPassword || !confirmPassword) {
            this.showError('All password fields are required.');
            return;
        }

        if (newPassword !== confirmPassword) {
            this.showError('New passwords do not match.');
            return;
        }

        if (newPassword.length < 6) {
            this.showError('New password must be at least 6 characters long.');
            return;
        }

        if (oldPassword === newPassword) {
            this.showError('New password must be different from current password.');
            return;
        }

        try {
            const userID = localStorage.getItem('user_id');
            await ApiService.changePassword(userID, {
                old_password: oldPassword,
                new_password: newPassword
            });

            this.state.passwordChangeMode = false;
            this.showSuccess('Password changed successfully!');
            this.updateView();

            // Clear form
            document.getElementById('passwordForm').reset();
        } catch (error) {
            console.error('Password change error:', error);
            let errorMessage = 'Failed to change password. Please try again.';

            if (error.status === 400) {
                errorMessage = 'Invalid current password.';
            } else if (error.status === 401) {
                errorMessage = 'Session expired. Please log in again.';
                setTimeout(() => navigateTo(`${BASE_URL}/login`), 2000);
            }

            this.showError(errorMessage);
        }
    }

    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    showError(message) {
        this.state.error = { visible: true, message };
        this.state.success = { visible: false, message: '' };
        this.updateView();
    }

    showSuccess(message) {
        this.state.success = { visible: true, message };
        this.state.error = { visible: false, message: '' };
        this.updateView();
    }

    initializeEventListeners() {
        // Profile form submission
        const profileForm = document.getElementById('profileForm');
        if (profileForm) {
            profileForm.addEventListener('submit', (e) => this.handleProfileUpdate(e));
        }

        // Password form submission
        const passwordForm = document.getElementById('passwordForm');
        if (passwordForm) {
            passwordForm.addEventListener('submit', (e) => this.handlePasswordChange(e));
        }

        // Password confirmation validation
        const confirmPasswordInput = document.getElementById('confirm_password');
        const newPasswordInput = document.getElementById('new_password');

        if (confirmPasswordInput && newPasswordInput) {
            const validatePasswords = () => {
                const newPassword = newPasswordInput.value;
                const confirmPassword = confirmPasswordInput.value;

                if (confirmPassword && newPassword !== confirmPassword) {
                    confirmPasswordInput.setCustomValidity('Passwords do not match');
                } else {
                    confirmPasswordInput.setCustomValidity('');
                }
            };

            newPasswordInput.addEventListener('input', validatePasswords);
            confirmPasswordInput.addEventListener('input', validatePasswords);
        }

        // Alert dismissal
        const alertCloseButtons = document.querySelectorAll('.alert .btn-close');
        alertCloseButtons.forEach(button => {
            button.addEventListener('click', () => {
                this.state.error = { visible: false, message: '' };
                this.state.success = { visible: false, message: '' };
            });
        });
    }

    async afterRender() {
        // Make profileView globally accessible for onclick handlers
        window.profileView = this;

        // Load profile data
        await this.loadProfile();
        await this.updateView();
    }
}
