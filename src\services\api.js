export const API_CONFIG = {
    BASE_URL: 'https://cashier.centurionbd.com/api',
    TIMEOUT: 30000,
    HEADERS: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }
};

export default class ApiService {
    static getAuthToken() {
        return localStorage.getItem('user-token');
    }

    static getHeaders() {
        const headers = { ...API_CONFIG.HEADERS };
        const token = this.getAuthToken();
        if (token) {
            headers['Authorization'] = `Bearer ${token}`;
        }
        return headers;
    }

    static handleResponse(response) {
        return response.json().then(data => {
            if (!response.ok) {
                // Handle different error status codes
                switch (response.status) {
                    case 401:
                        // Unauthorized - clear auth and redirect to login
                        localStorage.removeItem('user-token');
                        window.location.href = '/login';
                        break;
                    case 403:
                        // Forbidden
                        throw new Error('Access denied');
                    case 404:
                        // Not found
                        throw new Error('Resource not found');
                    default:
                        // Other errors
                        throw new Error(data.message || 'Something went wrong');
                }
            }
            return data;
        });
    }

    static async request(endpoint, options = {}) {
        const url = `${API_CONFIG.BASE_URL}${endpoint}`;
        const headers = this.getHeaders();

        const defaultOptions = {
            headers,
            timeout: API_CONFIG.TIMEOUT
        };

        try {
            const response = await $.ajax({
                url,
                ...defaultOptions,
                ...options
            });
            return response;
        } catch (error) {
            console.error('API Request failed:', error);
            // Pass through the original error object       
            throw error;
        }
    }

    static showErrorModal(error) {
        // Create modal HTML
        const modalHTML = `
            <div class="modal fade" id="errorModal" tabindex="-1" role="dialog" aria-labelledby="errorModalTitle" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title text-danger" id="errorModalTitle">
                                <i class="bi bi-exclamation-circle-fill me-2"></i>Error
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true"></span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <p>${error.message || 'An unexpected error occurred'}</p>
                            ${error.details ? `<pre class="text-muted">${error.details}</pre>` : ''}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if any
        const existingModal = document.getElementById('errorModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to body
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // Initialize and show modal
        const modal = new bootstrap.Modal(document.getElementById('errorModal'));
        modal.show();
    }

    // API Endpoints
    static async login(credentials) {
        return this.request('/auth/login/password', {
            method: 'POST',
            data: JSON.stringify(credentials)
        });
    }

    static async getBranchID(userID) {
        return this.request(`/merchants/cashiers/branch/${userID}`, {
            method: 'GET'
        });
    }

    static async createLockedPayment(branchID, data) {
        return this.request(`/vouchers/create/${branchID}`, {
            method: 'POST',
            data: JSON.stringify(data)
        });
    }

    static async verifyVoucherCode(userID, data) {
        return this.request(`/vouchers/verify/${userID}`, {
            method: 'POST',
            data: JSON.stringify(data)
        });
    }

    static async redeemVoucher(userID, data) {
        return this.request(`/vouchers/redeem/${userID}`, {
            method: 'POST',
            data: JSON.stringify(data)
        });
    }

    static async redeemGiftVoucher(userID, data) {
        return this.request(`/gift-vouchers/redeem/${userID}`, {
            method: 'POST',
            data: JSON.stringify(data)
        });
    }

    static async getDirectMOMOPayments(branchID) {
        return this.request(`/vouchers/direct_momo/${branchID}`, {
            method: 'GET'
        });
    }

    static async getPricing(amount, payment_type, payment_platform) {
        return this.request(`/price/calculate?amount=${amount}&payment_type=${payment_type}&payment_platform=${payment_platform}`, {
            method: 'GET'
        });
    }

    // Add this method to your ApiService class
    static async createVoucherWithAttachment(voucherData, attachmentData) {
        const formData = new FormData();
        
        // Add voucher data
        Object.keys(voucherData).forEach(key => {
            formData.append(key, voucherData[key]);
        });

        // Add attachment if present
        if (attachmentData?.file) {
            formData.append('attachment', attachmentData.file);
        }

        const response = await fetch(`${API_CONFIG.BASE_URL}/vouchers/create/v2`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('user-token')}`
                // Note: Don't set Content-Type header when using FormData
            },
            body: formData
        });

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            // Prefer errorData.error, then errorData.message, then fallback
            throw new Error(errorData.error || errorData.message || 'Failed to create voucher');
        }

        return response.json();
    }

    static async getProfile(userID) {
        return this.request(`/cashier/profile/${userID}`, {
            method: 'GET'
        });
    }

    static async updateProfile(userID, data) {
        return this.request(`/cashier/profile/${userID}`, {
            method: 'PUT',
            data: JSON.stringify(data)
        });
    }

    static async changePassword(userID, data) {
        return this.request(`/cashier/change-password/${userID}`, {
            method: 'POST',
            data: JSON.stringify(data)
        });
    }
}