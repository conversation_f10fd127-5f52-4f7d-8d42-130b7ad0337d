import ApiService from '../services/api.js'
import Alert from '../components/alert.js'

export const networkHandlers = {
    async createTransactionStepOne(e) {
        const cardElement = e.target.closest('.newTransaction');
        const phone = document.getElementById("phone").value;
        const amount = document.getElementById("amount").value;
        const payFrom = document.getElementById("pay_from").value;
        const reference  = document.getElementById('default_ref')?.value;

        const modalElement = document.getElementById('newTransactionModal');
        const extraRefs = this.collectExtraReferences(modalElement);
        const extraRefsHTML = this.generateExtraRefsHTML(extraRefs);
        const attachmentData = this.getAttachmentData(modalElement);

        try {
            if (amount < 0 || payFrom == "") {
                throw new Error('Invalid data given');
            }

            $("#newTransactionModal").modal("hide");

            const price = await ApiService.getPricing(amount, "voucher", payFrom);
            const charge = (Number(price.service_charge)).toFixed(2);
            const total = (Number(price.total_amount)).toFixed(2);
            this.confirmTransactionModal(phone, amount, charge, total, payFrom, reference, extraRefsHTML, attachmentData);
        } catch (error) {
            const errorMessage = "Please enter proper data for Amount (greater that E50), and choose a merchant and payment platform";
            console.log(error);
            Alert.showAlert(cardElement, errorMessage, 'warning');
        }
    },

    async createTransactionStepTwo(e) {
        // Show loading spinner on button
        const btn = document.getElementById('authorize_transaction');
        if (btn) {
            btn.innerHTML = `<span class="spinner-grow spinner-grow-sm" role="status" aria-hidden="true"></span> Requesting...`;
            btn.disabled = true;
        }

        const branchID = localStorage.getItem('branch_id');
        const phone = document.getElementById("c_phone").value;
        const amount = document.getElementById("c_amount").value;
        const charge = document.getElementById("c_charge").value;
        const total = document.getElementById("c_total").value;
        const payFrom = document.getElementById("c_pay_from").value;
        const reference = document.getElementById("c_ref").value;

        const modalElement = document.getElementById('newTransactionModal');
        const extraRefs = this.collectExtraReferences(modalElement);
        const attachmentData = this.getAttachmentData(modalElement);
        let transactionResult = "Failed";

        try {

            const payload = {
                branch_id: branchID,
                amount: parseFloat(amount),
                charge: parseFloat(charge),
                total_amount: parseFloat(total),
                pay_from: payFrom,
                default_reference: reference,
                extra_reference: extraRefs.map(item => item.value),
                phone: phone,
                mode: "MERCHANT_INITIATED",
                is_public_biller: false
            };
    
            const response = await ApiService.createVoucherWithAttachment(payload, attachmentData)
            console.log(response);
            if(response.message == "Voucher created successfully"){
                transactionResult = "Success";
                $("#confirmTransactionModal").modal("hide");
                //launch voucher redemption here
                localStorage.setItem('voucher_id', response.voucher_id);
                this.confirmRedeemModal(amount);
                let msg = document.getElementById("confirmSuccess");
                msg.classList.remove("d-none");

            } else {
                Swal.fire({
                    icon: 'info',
                    title: 'Alert',
                    text: response.message,
                    confirmButtonText: 'OK'
                });
            }
        } catch (error) {
            const existingModal = document.getElementById("confirmTransactionModal");
            if (existingModal) {
                const bsModal = bootstrap.Modal.getInstance(existingModal);
                bsModal.hide();
            }

            console.log(error);
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: error || error.message || 'Failed to create payment. Possibly timeout.',
                confirmButtonText: 'OK'
            });
        } finally {
            // Hide loading spinner on button
            if (btn) {
                btn.innerHTML = `Request Authorization`;
                btn.disabled = false;
            }
            this.storeLastTransaction({
                type: "Voucher",
                amount: "E"+amount,
                status: transactionResult,
                time: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
            });
            this.storeQuickStats("E"+amount, transactionResult);
            
            let msg = document.getElementById("confirmSuccess");
            msg.classList.add("d-none");
        }
    },

    async fetchBranchID() {
        const mainContent = document.querySelector('.app-content-area');

        try {
            const userID = localStorage.getItem('user_id');
            const response = await ApiService.getBranchID(userID);
            localStorage.setItem('branch_id', response.branch_id);
        } catch (error) {
            const errorMessage = error.responseJSON?.error || 
                        error.responseJSON?.message || 
                        error.message || 
                        'Failed to fetch branch information.';

            Alert.showAlert(mainContent, errorMessage, 'danger');
        }
    },

    async verifyVoucherCode(e) {
        const cardElement = e.target.closest('.newTransaction');

        try {
            const userID = localStorage.getItem('user_id');
            const code = document.getElementById("payment_code").value;
            const response = await ApiService.verifyVoucherCode(userID, {voucher_code: code});

            if(response.message == 'Voucher is valid'){
                $("#newVoucherModal").modal("hide");
                localStorage.setItem('voucher_id', response.voucher_id);
                this.confirmRedeemModal(response.amount);
            }
        } catch (error) {
            const errorMessage = error.responseJSON?.error || 
                        error.responseJSON?.message || 
                        error.message || 
                        'Failed to fetch branch information.';

            Alert.showAlert(cardElement, errorMessage, 'danger');
        }
    },

    async redeemVoucher(e) {
        const btn = document.getElementById('authorize_redeem');
        if (btn) {
            btn.innerHTML = `<span class="spinner-grow spinner-grow-sm" role="status" aria-hidden="true"></span> Requesting...`;
            btn.disabled = true;
        }

        const cardElement = e.target.closest('.newTransaction');
        let transactionResult = "Failed";
        const amount = document.getElementById("v_amount").value;
        const payable = document.getElementById("v_payable").value;
        const reference = document.getElementById("v_ref").value;
        const voucherID = localStorage.getItem('voucher_id');

        try {
            if(Number(payable) > Number(amount)) {
                Alert.showAlert(cardElement, 'Amount Payable cannot be greater than Amount Tendered.', 'danger');
                if (btn) {
                    btn.innerHTML = `Authorize`;
                    btn.disabled = false;
                }
                return
            }

            const paymentData = {
                amount_paid: Number(amount),
                amount_payable: Number(payable),
                voucher_id: Number(voucherID),
                reference: reference == "" ? "Default Reference" : reference,
            }
            const userID = localStorage.getItem('user_id');

            const response = await ApiService.redeemVoucher(userID, paymentData);
            transactionResult = "Success";
            $("#confirmRedeemModal").modal("hide");

            localStorage.setItem('voucher_id', null);
            let title = "Voucher Redeemed";
            let paymentInfo = `
            Payment voucher has been redeemed @: ${response.merchant_name}, ${response.branch_address}
            <br> Change: E${Number(response.change_given).toFixed(2)}<br>
            <b>Customer will receive confirmation SMS</b>`;
            this.successModal(title ,paymentInfo);

        } catch (error) {
            console.log(error);
            const errorMessage = error.responseJSON?.error || 
                        error.responseJSON?.message || 
                        error.message || 
                        'Failed to redeem payment voucher.';
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: errorMessage,
                confirmButtonText: 'OK'
            });
        } finally {
            if (btn) {
                btn.innerHTML = `Authorize`;
                btn.disabled = false;
            }

            this.storeLastTransaction({
                type: "Voucher Redemption",
                amount: "E"+amount,
                status: transactionResult,
                time: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
            });
            this.storeQuickStats("E"+amount, transactionResult);
            location.reload();
        }
    },

    async verifyGiftVoucherCode(e) {
        const cardElement = e.target.closest('.newTransaction');

        try {
            const pinCode = document.getElementById("gift_pin_code").value;
            
            if (!pinCode || pinCode.length !== 6) {
                throw new Error('Please enter a valid 6-digit PIN code');
            }

            $("#newGiftVoucherModal").modal("hide");
            this.confirmGiftRedeemModal();
        } catch (error) {
            const errorMessage = error.message || 'Failed to verify gift voucher PIN.';
            Alert.showAlert(cardElement, errorMessage, 'danger');
        }
    },

    async redeemGiftVoucher(e) {
        const btn = document.getElementById('authorize_gift_redeem');
        if (btn) {
            btn.innerHTML = `<span class="spinner-grow spinner-grow-sm" role="status" aria-hidden="true"></span> Requesting...`;
            btn.disabled = true;
        }

        const cardElement = e.target.closest('.newTransaction');
        let transactionResult = "Failed";
        const amountUsed = document.getElementById("gift_amount_used").value;
        const reference = document.getElementById("gift_reference").value;
        const pinCode = document.getElementById("gift_pin_code").value;

        try {
            if (!amountUsed || Number(amountUsed) <= 0) {
                Alert.showAlert(cardElement, 'Please enter a valid amount to use.', 'danger');
                if (btn) {
                    btn.innerHTML = `Authorize`;
                    btn.disabled = false;
                }
                return;
            }

            const giftVoucherData = {
                pinCode: pinCode,
                amountUsed: Number(amountUsed),
                reference: reference || "Gift voucher redemption"
            };
            
            const userID = localStorage.getItem('user_id');

            const response = await ApiService.redeemGiftVoucher(userID, giftVoucherData);
            transactionResult = "Success";
            $("#confirmGiftRedeemModal").modal("hide");

            let title = "Gift Voucher Redeemed";
            let paymentInfo = `
            Gift voucher has been redeemed successfully!
            <br> Amount Used: E${Number(response.amountUsed).toFixed(2)}
            <br> Remaining Balance: E${Number(response.remaining).toFixed(2)}
            <br> New Status: ${response.newStatus}
            <br> Voucher ID: ${response.voucherId}`;
            
            this.successModal(title, paymentInfo);

        } catch (error) {
            console.log(error);
            const errorMessage = error.responseJSON?.error || 
                        error.responseJSON?.message || 
                        error.message || 
                        'Failed to redeem gift voucher.';
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: errorMessage,
                confirmButtonText: 'OK'
            });
        } finally {
            if (btn) {
                btn.innerHTML = `Authorize`;
                btn.disabled = false;
            }

            this.storeLastTransaction({
                type: "Gift Voucher Redemption",
                amount: "E"+amountUsed,
                status: transactionResult,
                time: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
            });
            this.storeQuickStats("E"+amountUsed, transactionResult);
            location.reload();
        }
    }
}