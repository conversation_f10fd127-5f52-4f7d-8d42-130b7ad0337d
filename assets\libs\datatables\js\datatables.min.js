/*
 * This combined file was created by the DataTables downloader builder:
 *   https://datatables.net/download
 *
 * To rebuild or modify this file with the latest versions of the included
 * software please visit:
 *   https://datatables.net/download/#bs5/jq-3.7.0/dt-2.0.8/af-2.7.0/b-3.0.2/b-colvis-3.0.2/b-html5-3.0.2/b-print-3.0.2/cr-2.0.3/fh-4.0.1/r-3.0.2/rr-1.5.0/sc-2.4.3/sb-1.7.1/sp-2.3.1
 *
 * Included libraries:
 *   jQuery 3 3.7.0, DataTables 2.0.8, AutoFill 2.7.0, Buttons 3.0.2, Column visibility 3.0.2, HTML5 export 3.0.2, Print view 3.0.2, ColReorder 2.0.3, FixedHeader 4.0.1, Responsive 3.0.2, RowReorder 1.5.0, Scroller 2.4.3, SearchBuilder 1.7.1, SearchPanes 2.3.1
 */

/*! DataTables 2.0.8
 * © SpryMedia Ltd - datatables.net/license
 */
!function(n){"use strict";var a;"function"==typeof define&&define.amd?define(["jquery"],function(t){return n(t,window,document)}):"object"==typeof exports?(a=require("jquery"),"undefined"==typeof window?module.exports=function(t,e){return t=t||window,e=e||a(t),n(e,t,t.document)}:module.exports=n(a,window,window.document)):window.DataTable=n(jQuery,window,document)}(function(V,q,_){"use strict";function g(t){var e=parseInt(t,10);return!isNaN(e)&&isFinite(t)?e:null}function o(t,e,n){var a=typeof t,r="string"==a;return"number"==a||"bigint"==a||!!y(t)||(e&&r&&(t=R(t,e)),n&&r&&(t=t.replace(P,"")),!isNaN(parseFloat(t))&&isFinite(t))}function l(t,e,n){var a;return!!y(t)||("string"!=typeof t||!t.match(/<(input|select)/i))&&(y(a=t)||"string"==typeof a)&&!!o(I(t),e,n)||null}function v(t,e,n,a){var r=[],o=0,i=e.length;if(void 0!==a)for(;o<i;o++)t[e[o]][n]&&r.push(t[e[o]][n][a]);else for(;o<i;o++)t[e[o]]&&r.push(t[e[o]][n]);return r}function h(t,e){var n,a=[];void 0===e?(e=0,n=t):(n=e,e=t);for(var r=e;r<n;r++)a.push(r);return a}function b(t){for(var e=[],n=0,a=t.length;n<a;n++)t[n]&&e.push(t[n]);return e}var C,U,e,t,$=function(t,H){var W,X,B;return $.factory(t,H)?$:this instanceof $?V(t).DataTable(H):(X=void 0===(H=t),B=(W=this).length,X&&(H={}),this.api=function(){return new U(this)},this.each(function(){var n=1<B?Zt({},H,!0):H,a=0,t=this.getAttribute("id"),r=!1,e=$.defaults,o=V(this);if("table"!=this.nodeName.toLowerCase())Z(null,0,"Non-table node initialisation ("+this.nodeName+")",2);else{V(this).trigger("options.dt",n),nt(e),at(e.column),z(e,e,!0),z(e.column,e.column,!0),z(e,V.extend(n,o.data()),!0);for(var i=$.settings,a=0,l=i.length;a<l;a++){var s=i[a];if(s.nTable==this||s.nTHead&&s.nTHead.parentNode==this||s.nTFoot&&s.nTFoot.parentNode==this){var E=(void 0!==n.bRetrieve?n:e).bRetrieve,k=(void 0!==n.bDestroy?n:e).bDestroy;if(X||E)return s.oInstance;if(k){new $.Api(s).destroy();break}return void Z(s,0,"Cannot reinitialise DataTable",3)}if(s.sTableId==this.id){i.splice(a,1);break}}null!==t&&""!==t||(t="DataTables_Table_"+$.ext._unique++,this.id=t);var u=V.extend(!0,{},$.models.oSettings,{sDestroyWidth:o[0].style.width,sInstance:t,sTableId:t,colgroup:V("<colgroup>").prependTo(this),fastData:function(t,e,n){return G(u,t,e,n)}}),t=(u.nTable=this,u.oInit=n,i.push(u),u.api=new U(u),u.oInstance=1===W.length?W:o.dataTable(),nt(n),n.aLengthMenu&&!n.iDisplayLength&&(n.iDisplayLength=Array.isArray(n.aLengthMenu[0])?n.aLengthMenu[0][0]:V.isPlainObject(n.aLengthMenu[0])?n.aLengthMenu[0].value:n.aLengthMenu[0]),n=Zt(V.extend(!0,{},e),n),Q(u.oFeatures,n,["bPaginate","bLengthChange","bFilter","bSort","bSortMulti","bInfo","bProcessing","bAutoWidth","bSortClasses","bServerSide","bDeferRender"]),Q(u,n,["ajax","fnFormatNumber","sServerMethod","aaSorting","aaSortingFixed","aLengthMenu","sPaginationType","iStateDuration","bSortCellsTop","iTabIndex","sDom","fnStateLoadCallback","fnStateSaveCallback","renderer","searchDelay","rowId","caption","layout",["iCookieDuration","iStateDuration"],["oSearch","oPreviousSearch"],["aoSearchCols","aoPreSearchCols"],["iDisplayLength","_iDisplayLength"]]),Q(u.oScroll,n,[["sScrollX","sX"],["sScrollXInner","sXInner"],["sScrollY","sY"],["bScrollCollapse","bCollapse"]]),Q(u.oLanguage,n,"fnInfoCallback"),K(u,"aoDrawCallback",n.fnDrawCallback),K(u,"aoStateSaveParams",n.fnStateSaveParams),K(u,"aoStateLoadParams",n.fnStateLoadParams),K(u,"aoStateLoaded",n.fnStateLoaded),K(u,"aoRowCallback",n.fnRowCallback),K(u,"aoRowCreatedCallback",n.fnCreatedRow),K(u,"aoHeaderCallback",n.fnHeaderCallback),K(u,"aoFooterCallback",n.fnFooterCallback),K(u,"aoInitComplete",n.fnInitComplete),K(u,"aoPreDrawCallback",n.fnPreDrawCallback),u.rowIdFn=J(n.rowId),u),c=($.__browser||(P={},$.__browser=P,j=V("<div/>").css({position:"fixed",top:0,left:-1*q.pageXOffset,height:1,width:1,overflow:"hidden"}).append(V("<div/>").css({position:"absolute",top:1,left:1,width:100,overflow:"scroll"}).append(V("<div/>").css({width:"100%",height:10}))).appendTo("body"),p=j.children(),O=p.children(),P.barWidth=p[0].offsetWidth-p[0].clientWidth,P.bScrollbarLeft=1!==Math.round(O.offset().left),j.remove()),V.extend(t.oBrowser,$.__browser),t.oScroll.iBarWidth=$.__browser.barWidth,u.oClasses),d=(V.extend(c,$.ext.classes,n.oClasses),o.addClass(c.table),u.oFeatures.bPaginate||(n.iDisplayStart=0),void 0===u.iInitDisplayStart&&(u.iInitDisplayStart=n.iDisplayStart,u._iDisplayStart=n.iDisplayStart),u.oLanguage),f=(V.extend(!0,d,n.oLanguage),d.sUrl?(V.ajax({dataType:"json",url:d.sUrl,success:function(t){z(e.oLanguage,t),V.extend(!0,d,t,u.oInit.oLanguage),tt(u,null,"i18n",[u],!0),Et(u)},error:function(){Z(u,0,"i18n file loading error",21),Et(u)}}),r=!0):tt(u,null,"i18n",[u]),[]),h=this.getElementsByTagName("thead"),p=It(u,h[0]);if(n.aoColumns)f=n.aoColumns;else if(p.length)for(l=p[a=0].length;a<l;a++)f.push(null);for(a=0,l=f.length;a<l;a++)rt(u);var g,m,v,b,y,D,x,S=u,T=n.aoColumnDefs,w=f,M=p,_=function(t,e){ot(u,t,e)},C=S.aoColumns;if(w)for(g=0,m=w.length;g<m;g++)w[g]&&w[g].name&&(C[g].sName=w[g].name);if(T)for(g=T.length-1;0<=g;g--){var I=void 0!==(x=T[g]).target?x.target:void 0!==x.targets?x.targets:x.aTargets;for(Array.isArray(I)||(I=[I]),v=0,b=I.length;v<b;v++){var A=I[v];if("number"==typeof A&&0<=A){for(;C.length<=A;)rt(S);_(A,x)}else if("number"==typeof A&&A<0)_(C.length+A,x);else if("string"==typeof A)for(y=0,D=C.length;y<D;y++)"_all"===A?_(y,x):-1!==A.indexOf(":name")?C[y].sName===A.replace(":name","")&&_(y,x):M.forEach(function(t){t[y]&&(t=V(t[y].cell),A.match(/^[a-z][\w-]*$/i)&&(A="."+A),t.is(A))&&_(y,x)})}}if(w)for(g=0,m=w.length;g<m;g++)_(g,w[g]);var L,F,N,j,P=o.children("tbody").find("tr").eq(0),R=(P.length&&(L=function(t,e){return null!==t.getAttribute("data-"+e)?e:null},V(P[0]).children("th, td").each(function(t,e){var n,a=u.aoColumns[t];a||Z(u,0,"Incorrect column count",18),a.mData===t&&(n=L(e,"sort")||L(e,"order"),e=L(e,"filter")||L(e,"search"),null===n&&null===e||(a.mData={_:t+".display",sort:null!==n?t+".@data-"+n:void 0,type:null!==n?t+".@data-"+n:void 0,filter:null!==e?t+".@data-"+e:void 0},a._isArrayHost=!0,ot(u,t)))})),u.oFeatures),O=function(){if(void 0===n.aaSorting){var t=u.aaSorting;for(a=0,l=t.length;a<l;a++)t[a][1]=u.aoColumns[a].asSorting[0]}Yt(u),K(u,"aoDrawCallback",function(){(u.bSorted||"ssp"===et(u)||R.bDeferRender)&&Yt(u)});var e=o.children("caption"),e=(u.caption&&(e=0===e.length?V("<caption/>").appendTo(o):e).html(u.caption),e.length&&(e[0]._captionSide=e.css("caption-side"),u.captionNode=e[0]),0===h.length&&(h=V("<thead/>").appendTo(o)),u.nTHead=h[0],V("tr",h).addClass(c.thead.row),o.children("tbody")),e=(0===e.length&&(e=V("<tbody/>").insertAfter(h)),u.nTBody=e[0],o.children("tfoot"));if(0===e.length&&(e=V("<tfoot/>").appendTo(o)),u.nTFoot=e[0],V("tr",e).addClass(c.tfoot.row),n.aaData)for(a=0;a<n.aaData.length;a++)Y(u,n.aaData[a]);else"dom"==et(u)&&ut(u,V(u.nTBody).children("tr"));u.aiDisplay=u.aiDisplayMaster.slice(),!(u.bInitialised=!0)===r&&Et(u)};K(u,"aoDrawCallback",Gt),n.bStateSave?(R.bStateSave=!0,N=O,(F=u).oFeatures.bStateSave?void 0!==(j=F.fnStateLoadCallback.call(F.oInstance,F,function(t){Jt(F,t,N)}))&&Jt(F,j,N):N()):O()}}),W=null,this)},c=($.ext=C={buttons:{},classes:{},builder:"bs5/jq-3.7.0/dt-2.0.8/af-2.7.0/b-3.0.2/b-colvis-3.0.2/b-html5-3.0.2/b-print-3.0.2/cr-2.0.3/fh-4.0.1/r-3.0.2/rr-1.5.0/sc-2.4.3/sb-1.7.1/sp-2.3.1",errMode:"alert",feature:[],features:{},search:[],selector:{cell:[],column:[],row:[]},legacy:{ajax:null},pager:{},renderer:{pageButton:{},header:{}},order:{},type:{className:{},detect:[],render:{},search:{},order:{}},_unique:0,fnVersionCheck:$.fnVersionCheck,iApiIndex:0,sVersion:$.version},V.extend(C,{afnFiltering:C.search,aTypes:C.type.detect,ofnSearch:C.type.search,oSort:C.type.order,afnSortData:C.order,aoFeatures:C.feature,oStdClasses:C.classes,oPagination:C.pager}),V.extend($.ext.classes,{container:"dt-container",empty:{row:"dt-empty"},info:{container:"dt-info"},length:{container:"dt-length",select:"dt-input"},order:{canAsc:"dt-orderable-asc",canDesc:"dt-orderable-desc",isAsc:"dt-ordering-asc",isDesc:"dt-ordering-desc",none:"dt-orderable-none",position:"sorting_"},processing:{container:"dt-processing"},scrolling:{body:"dt-scroll-body",container:"dt-scroll",footer:{self:"dt-scroll-foot",inner:"dt-scroll-footInner"},header:{self:"dt-scroll-head",inner:"dt-scroll-headInner"}},search:{container:"dt-search",input:"dt-input"},table:"dataTable",tbody:{cell:"",row:""},thead:{cell:"",row:""},tfoot:{cell:"",row:""},paging:{active:"current",button:"dt-paging-button",container:"dt-paging",disabled:"disabled"}}),{}),d=/[\r\n\u2028]/g,L=/<([^>]*>)/g,F=Math.pow(2,28),N=/^\d{2,4}[./-]\d{1,2}[./-]\d{1,2}([T ]{1}\d{1,2}[:.]\d{2}([.:]\d{2})?)?$/,j=new RegExp("(\\"+["/",".","*","+","?","|","(",")","[","]","{","}","\\","$","^","-"].join("|\\")+")","g"),P=/['\u00A0,$£€¥%\u2009\u202F\u20BD\u20a9\u20BArfkɃΞ]/gi,y=function(t){return!t||!0===t||"-"===t},R=function(t,e){return c[e]||(c[e]=new RegExp(Pt(e),"g")),"string"==typeof t&&"."!==e?t.replace(/\./g,"").replace(c[e],"."):t},f=function(t,e,n){var a=[],r=0,o=t.length;if(void 0!==n)for(;r<o;r++)t[r]&&t[r][e]&&a.push(t[r][e][n]);else for(;r<o;r++)t[r]&&a.push(t[r][e]);return a},I=function(t){if(t.length>F)throw new Error("Exceeded max str len");var e;for(t=t.replace(L,"");(t=(e=t).replace(/<script/i,""))!==e;);return e},u=function(t){return"string"==typeof(t=Array.isArray(t)?t.join(","):t)?t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;"):t},O=function(t,e){var n;return"string"!=typeof t?t:(n=t.normalize("NFD")).length!==t.length?(!0===e?t+" ":"")+n.replace(/[\u0300-\u036f]/g,""):n},x=function(t){if(Array.from&&Set)return Array.from(new Set(t));if(function(t){if(!(t.length<2))for(var e=t.slice().sort(),n=e[0],a=1,r=e.length;a<r;a++){if(e[a]===n)return!1;n=e[a]}return!0}(t))return t.slice();var e,n,a,r=[],o=t.length,i=0;t:for(n=0;n<o;n++){for(e=t[n],a=0;a<i;a++)if(r[a]===e)continue t;r.push(e),i++}return r},E=function(t,e){if(Array.isArray(e))for(var n=0;n<e.length;n++)E(t,e[n]);else t.push(e);return t};function D(e,t){t&&t.split(" ").forEach(function(t){t&&e.classList.add(t)})}function k(e){var n,a,r={};V.each(e,function(t){(n=t.match(/^([^A-Z]+?)([A-Z])/))&&-1!=="a aa ai ao as b fn i m o s ".indexOf(n[1]+" ")&&(a=t.replace(n[0],n[2].toLowerCase()),r[a]=t,"o"===n[1])&&k(e[t])}),e._hungarianMap=r}function z(e,n,a){var r;e._hungarianMap||k(e),V.each(n,function(t){void 0===(r=e._hungarianMap[t])||!a&&void 0!==n[r]||("o"===r.charAt(0)?(n[r]||(n[r]={}),V.extend(!0,n[r],n[t]),z(e[r],n[r],a)):n[r]=n[t])})}$.util={diacritics:function(t,e){if("function"!=typeof t)return O(t,e);O=t},debounce:function(n,a){var r;return function(){var t=this,e=arguments;clearTimeout(r),r=setTimeout(function(){n.apply(t,e)},a||250)}},throttle:function(a,t){var r,o,i=void 0!==t?t:200;return function(){var t=this,e=+new Date,n=arguments;r&&e<r+i?(clearTimeout(o),o=setTimeout(function(){r=void 0,a.apply(t,n)},i)):(r=e,a.apply(t,n))}},escapeRegex:function(t){return t.replace(j,"\\$1")},set:function(a){var f;return V.isPlainObject(a)?$.util.set(a._):null===a?function(){}:"function"==typeof a?function(t,e,n){a(t,"set",e,n)}:"string"!=typeof a||-1===a.indexOf(".")&&-1===a.indexOf("[")&&-1===a.indexOf("(")?function(t,e){t[a]=e}:(f=function(t,e,n){for(var a,r,o,i,l=ft(n),n=l[l.length-1],s=0,u=l.length-1;s<u;s++){if("__proto__"===l[s]||"constructor"===l[s])throw new Error("Cannot set prototype values");if(a=l[s].match(dt),r=l[s].match(p),a){if(l[s]=l[s].replace(dt,""),t[l[s]]=[],(a=l.slice()).splice(0,s+1),i=a.join("."),Array.isArray(e))for(var c=0,d=e.length;c<d;c++)f(o={},e[c],i),t[l[s]].push(o);else t[l[s]]=e;return}r&&(l[s]=l[s].replace(p,""),t=t[l[s]](e)),null!==t[l[s]]&&void 0!==t[l[s]]||(t[l[s]]={}),t=t[l[s]]}n.match(p)?t[n.replace(p,"")](e):t[n.replace(dt,"")]=e},function(t,e){return f(t,e,a)})},get:function(r){var o,f;return V.isPlainObject(r)?(o={},V.each(r,function(t,e){e&&(o[t]=$.util.get(e))}),function(t,e,n,a){var r=o[e]||o._;return void 0!==r?r(t,e,n,a):t}):null===r?function(t){return t}:"function"==typeof r?function(t,e,n,a){return r(t,e,n,a)}:"string"!=typeof r||-1===r.indexOf(".")&&-1===r.indexOf("[")&&-1===r.indexOf("(")?function(t){return t[r]}:(f=function(t,e,n){var a,r,o;if(""!==n)for(var i=ft(n),l=0,s=i.length;l<s;l++){if(d=i[l].match(dt),a=i[l].match(p),d){if(i[l]=i[l].replace(dt,""),""!==i[l]&&(t=t[i[l]]),r=[],i.splice(0,l+1),o=i.join("."),Array.isArray(t))for(var u=0,c=t.length;u<c;u++)r.push(f(t[u],e,o));var d=d[0].substring(1,d[0].length-1);t=""===d?r:r.join(d);break}if(a)i[l]=i[l].replace(p,""),t=t[i[l]]();else{if(null===t||null===t[i[l]])return null;if(void 0===t||void 0===t[i[l]])return;t=t[i[l]]}}return t},function(t,e){return f(t,e,r)})},stripHtml:function(t){var e=typeof t;if("function"!=e)return"string"==e?I(t):t;I=t},escapeHtml:function(t){var e=typeof t;if("function"!=e)return"string"==e||Array.isArray(t)?u(t):t;u=t},unique:x};var r=function(t,e,n){void 0!==t[e]&&(t[n]=t[e])};function nt(t){r(t,"ordering","bSort"),r(t,"orderMulti","bSortMulti"),r(t,"orderClasses","bSortClasses"),r(t,"orderCellsTop","bSortCellsTop"),r(t,"order","aaSorting"),r(t,"orderFixed","aaSortingFixed"),r(t,"paging","bPaginate"),r(t,"pagingType","sPaginationType"),r(t,"pageLength","iDisplayLength"),r(t,"searching","bFilter"),"boolean"==typeof t.sScrollX&&(t.sScrollX=t.sScrollX?"100%":""),"boolean"==typeof t.scrollX&&(t.scrollX=t.scrollX?"100%":"");var e=t.aoSearchCols;if(e)for(var n=0,a=e.length;n<a;n++)e[n]&&z($.models.oSearch,e[n]);t.serverSide&&!t.searchDelay&&(t.searchDelay=400)}function at(t){r(t,"orderable","bSortable"),r(t,"orderData","aDataSort"),r(t,"orderSequence","asSorting"),r(t,"orderDataType","sortDataType");var e=t.aDataSort;"number"!=typeof e||Array.isArray(e)||(t.aDataSort=[e])}function rt(t){var e=$.defaults.column,n=t.aoColumns.length,e=V.extend({},$.models.oColumn,e,{aDataSort:e.aDataSort||[n],mData:e.mData||n,idx:n,searchFixed:{},colEl:V("<col>").attr("data-dt-column",n)}),e=(t.aoColumns.push(e),t.aoPreSearchCols);e[n]=V.extend({},$.models.oSearch,e[n])}function ot(t,e,n){function a(t){return"string"==typeof t&&-1!==t.indexOf("@")}var r=t.aoColumns[e],o=(null!=n&&(at(n),z($.defaults.column,n,!0),void 0===n.mDataProp||n.mData||(n.mData=n.mDataProp),n.sType&&(r._sManualType=n.sType),n.className&&!n.sClass&&(n.sClass=n.className),e=r.sClass,V.extend(r,n),Q(r,n,"sWidth","sWidthOrig"),e!==r.sClass&&(r.sClass=e+" "+r.sClass),void 0!==n.iDataSort&&(r.aDataSort=[n.iDataSort]),Q(r,n,"aDataSort")),r.mData),i=J(o);r.mRender&&Array.isArray(r.mRender)&&(n=(e=r.mRender.slice()).shift(),r.mRender=$.render[n].apply(q,e)),r._render=r.mRender?J(r.mRender):null;r._bAttrSrc=V.isPlainObject(o)&&(a(o.sort)||a(o.type)||a(o.filter)),r._setter=null,r.fnGetData=function(t,e,n){var a=i(t,e,void 0,n);return r._render&&e?r._render(a,e,t,n):a},r.fnSetData=function(t,e,n){return m(o)(t,e,n)},"number"==typeof o||r._isArrayHost||(t._rowReadObject=!0),t.oFeatures.bSort||(r.bSortable=!1)}function M(t){var e=t;if(e.oFeatures.bAutoWidth){var n,a,r=e.nTable,o=e.aoColumns,i=e.oScroll,l=i.sY,s=i.sX,i=i.sXInner,u=X(e,"bVisible"),c=r.getAttribute("width"),d=r.parentNode,f=r.style.width,f=(f&&-1!==f.indexOf("%")&&(c=f),tt(e,null,"column-calc",{visible:u},!1),V(r.cloneNode()).css("visibility","hidden").removeAttr("id")),h=(f.append("<tbody>"),V("<tr/>").appendTo(f.find("tbody")));for(f.append(V(e.nTHead).clone()).append(V(e.nTFoot).clone()),f.find("tfoot th, tfoot td").css("width",""),f.find("thead th, thead td").each(function(){var t=lt(e,this,!0,!1);t?(this.style.width=t,s&&V(this).append(V("<div/>").css({width:t,margin:0,padding:0,border:0,height:1}))):this.style.width=""}),n=0;n<u.length;n++){p=u[n],a=o[p];var p=function(t,e){var n=t.aoColumns[e];if(!n.maxLenString){for(var a,r="",o=-1,i=0,l=t.aiDisplayMaster.length;i<l;i++){var s=t.aiDisplayMaster[i],s=vt(t,s)[e],s=s&&"object"==typeof s&&s.nodeType?s.innerHTML:s+"";s=s.replace(/id=".*?"/g,"").replace(/name=".*?"/g,""),(a=I(s).replace(/&nbsp;/g," ")).length>o&&(r=s,o=a.length)}n.maxLenString=r}return n.maxLenString}(e,p),g=C.type.className[a.sType],m=p+a.sContentPadding,p=-1===p.indexOf("<")?_.createTextNode(m):m;V("<td/>").addClass(g).addClass(a.sClass).append(p).appendTo(h)}V("[name]",f).removeAttr("name");var v=V("<div/>").css(s||l?{position:"absolute",top:0,left:0,height:1,right:0,overflow:"hidden"}:{}).append(f).appendTo(d),b=(s&&i?f.width(i):s?(f.css("width","auto"),f.removeAttr("width"),f.width()<d.clientWidth&&c&&f.width(d.clientWidth)):l?f.width(d.clientWidth):c&&f.width(c),0),y=f.find("tbody tr").eq(0).children();for(n=0;n<u.length;n++){var D=y[n].getBoundingClientRect().width;b+=D,o[u[n]].sWidth=A(D)}r.style.width=A(b),v.remove(),c&&(r.style.width=A(c)),!c&&!s||e._reszEvt||(V(q).on("resize.DT-"+e.sInstance,$.util.throttle(function(){e.bDestroying||M(e)})),e._reszEvt=!0)}for(var x=t,S=x.aoColumns,T=0;T<S.length;T++){var w=lt(x,[T],!1,!1);S[T].colEl.css("width",w)}i=t.oScroll;""===i.sY&&""===i.sX||Xt(t),tt(t,null,"column-sizing",[t])}function H(t,e){t=X(t,"bVisible");return"number"==typeof t[e]?t[e]:null}function T(t,e){t=X(t,"bVisible").indexOf(e);return-1!==t?t:null}function W(t){var e=t.aoHeader,n=t.aoColumns,a=0;if(e.length)for(var r=0,o=e[0].length;r<o;r++)n[r].bVisible&&"none"!==V(e[0][r].cell).css("display")&&a++;return a}function X(t,n){var a=[];return t.aoColumns.map(function(t,e){t[n]&&a.push(e)}),a}function B(t){for(var e,n,a,r,o,i,l,s=t.aoColumns,u=t.aoData,c=$.ext.type.detect,d=0,f=s.length;d<f;d++){if(l=[],!(o=s[d]).sType&&o._sManualType)o.sType=o._sManualType;else if(!o.sType){for(e=0,n=c.length;e<n;e++){for(a=0,r=u.length;a<r;a++)if(u[a]){if(void 0===l[a]&&(l[a]=G(t,a,d,"type")),!(i=c[e](l[a],t))&&e!==c.length-2)break;if("html"===i&&!y(l[a]))break}if(i){o.sType=i;break}}o.sType||(o.sType="string")}var h=C.type.className[o.sType],h=(h&&(it(t.aoHeader,d,h),it(t.aoFooter,d,h)),C.type.render[o.sType]);if(h&&!o._render){o._render=$.util.get(h),p=b=v=m=g=void 0;for(var p,g=t,m=d,v=g.aoData,b=0;b<v.length;b++)v[b].nTr&&(p=G(g,b,m,"display"),v[b].displayData[m]=p,ct(v[b].anCells[m],p))}}}function it(t,e,n){t.forEach(function(t){t[e]&&t[e].unique&&D(t[e].cell,n)})}function lt(t,e,n,a){Array.isArray(e)||(e=st(e));for(var r,o=0,i=t.aoColumns,l=0,s=e.length;l<s;l++){var u=i[e[l]],c=n?u.sWidthOrig:u.sWidth;if(a||!1!==u.bVisible){if(null==c)return null;"number"==typeof c?(r="px",o+=c):(u=c.match(/([\d\.]+)([^\d]*)/))&&(o+=+u[1],r=3===u.length?u[2]:"px")}}return o+r}function st(t){t=V(t).closest("[data-dt-column]").attr("data-dt-column");return t?t.split(",").map(function(t){return+t}):[]}function Y(t,e,n,a){for(var r=t.aoData.length,o=V.extend(!0,{},$.models.oRow,{src:n?"dom":"data",idx:r}),i=(o._aData=e,t.aoData.push(o),t.aoColumns),l=0,s=i.length;l<s;l++)i[l].sType=null;t.aiDisplayMaster.push(r);e=t.rowIdFn(e);return void 0!==e&&(t.aIds[e]=o),!n&&t.oFeatures.bDeferRender||bt(t,r,n,a),r}function ut(n,t){var a;return(t=t instanceof V?t:V(t)).map(function(t,e){return a=mt(n,e),Y(n,a.data,e,a.cells)})}function G(t,e,n,a){"search"===a?a="filter":"order"===a&&(a="sort");var r=t.aoData[e];if(r){var o=t.iDraw,i=t.aoColumns[n],r=r._aData,l=i.sDefaultContent,s=i.fnGetData(r,a,{settings:t,row:e,col:n});if(void 0===(s="display"!==a&&s&&"object"==typeof s&&s.nodeName?s.innerHTML:s))return t.iDrawError!=o&&null===l&&(Z(t,0,"Requested unknown parameter "+("function"==typeof i.mData?"{function}":"'"+i.mData+"'")+" for row "+e+", column "+n,4),t.iDrawError=o),l;if(s!==r&&null!==s||null===l||void 0===a){if("function"==typeof s)return s.call(r)}else s=l;return null===s&&"display"===a?"":s="filter"===a&&(e=$.ext.type.search)[i.sType]?e[i.sType](s):s}}function ct(t,e){e&&"object"==typeof e&&e.nodeName?V(t).empty().append(e):t.innerHTML=e}var dt=/\[.*?\]$/,p=/\(\)$/;function ft(t){return(t.match(/(\\.|[^.])+/g)||[""]).map(function(t){return t.replace(/\\\./g,".")})}var J=$.util.get,m=$.util.set;function ht(t){return f(t.aoData,"_aData")}function pt(t){t.aoData.length=0,t.aiDisplayMaster.length=0,t.aiDisplay.length=0,t.aIds={}}function gt(t,e,n,a){var r,o,i=t.aoData[e];if(i._aSortData=null,i._aFilterData=null,i.displayData=null,"dom"!==n&&(n&&"auto"!==n||"dom"!==i.src)){var l=i.anCells,s=vt(t,e);if(l)if(void 0!==a)ct(l[a],s[a]);else for(r=0,o=l.length;r<o;r++)ct(l[r],s[r])}else i._aData=mt(t,i,a,void 0===a?void 0:i._aData).data;var u=t.aoColumns;if(void 0!==a)u[a].sType=null,u[a].maxLenString=null;else{for(r=0,o=u.length;r<o;r++)u[r].sType=null,u[r].maxLenString=null;yt(t,i)}}function mt(t,e,n,a){function r(t,e){var n;"string"==typeof t&&-1!==(n=t.indexOf("@"))&&(n=t.substring(n+1),m(t)(a,e.getAttribute(n)))}function o(t){void 0!==n&&n!==d||(l=f[d],s=t.innerHTML.trim(),l&&l._bAttrSrc?(m(l.mData._)(a,s),r(l.mData.sort,t),r(l.mData.type,t),r(l.mData.filter,t)):h?(l._setter||(l._setter=m(l.mData)),l._setter(a,s)):a[d]=s),d++}var i,l,s,u=[],c=e.firstChild,d=0,f=t.aoColumns,h=t._rowReadObject;a=void 0!==a?a:h?{}:[];if(c)for(;c;)"TD"!=(i=c.nodeName.toUpperCase())&&"TH"!=i||(o(c),u.push(c)),c=c.nextSibling;else for(var p=0,g=(u=e.anCells).length;p<g;p++)o(u[p]);var e=e.firstChild?e:e.nTr;return e&&(e=e.getAttribute("id"))&&m(t.rowId)(a,e),{data:a,cells:u}}function vt(t,e){var n=t.aoData[e],a=t.aoColumns;if(!n.displayData){n.displayData=[];for(var r=0,o=a.length;r<o;r++)n.displayData.push(G(t,e,r,"display"))}return n.displayData}function bt(t,e,n,a){var r,o,i,l,s,u,c=t.aoData[e],d=c._aData,f=[],h=t.oClasses.tbody.row;if(null===c.nTr){for(r=n||_.createElement("tr"),c.nTr=r,c.anCells=f,D(r,h),r._DT_RowIndex=e,yt(t,c),l=0,s=t.aoColumns.length;l<s;l++){i=t.aoColumns[l],(o=(u=!n||!a[l])?_.createElement(i.sCellType):a[l])||Z(t,0,"Incorrect column count",18),o._DT_CellIndex={row:e,column:l},f.push(o);var p=vt(t,e);!u&&(!i.mRender&&i.mData===l||V.isPlainObject(i.mData)&&i.mData._===l+".display")||ct(o,p[l]),i.bVisible&&u?r.appendChild(o):i.bVisible||u||o.parentNode.removeChild(o),i.fnCreatedCell&&i.fnCreatedCell.call(t.oInstance,o,G(t,e,l),d,e,l)}tt(t,"aoRowCreatedCallback","row-created",[r,d,e,f])}else D(c.nTr,h)}function yt(t,e){var n=e.nTr,a=e._aData;n&&((t=t.rowIdFn(a))&&(n.id=t),a.DT_RowClass&&(t=a.DT_RowClass.split(" "),e.__rowc=e.__rowc?x(e.__rowc.concat(t)):t,V(n).removeClass(e.__rowc.join(" ")).addClass(a.DT_RowClass)),a.DT_RowAttr&&V(n).attr(a.DT_RowAttr),a.DT_RowData)&&V(n).data(a.DT_RowData)}function Dt(t,e){var n,a=t.oClasses,r=t.aoColumns,o="header"===e?t.nTHead:t.nTFoot,i="header"===e?"sTitle":e;if(o){if(("header"===e||f(t.aoColumns,i).join(""))&&1===(n=(n=V("tr",o)).length?n:V("<tr/>").appendTo(o)).length)for(var l=V("td, th",n).length,s=r.length;l<s;l++)V("<th/>").html(r[l][i]||"").appendTo(n);var u=It(t,o,!0);"header"===e?t.aoHeader=u:t.aoFooter=u,V(o).children("tr").attr("role","row"),V(o).children("tr").children("th, td").each(function(){te(t,e)(t,V(this),a)})}}function xt(t,e,n){var a,r,o,i,l,s=[],u=[],c=t.aoColumns,t=c.length;if(e){for(n=n||h(t).filter(function(t){return c[t].bVisible}),a=0;a<e.length;a++)s[a]=e[a].slice().filter(function(t,e){return n.includes(e)}),u.push([]);for(a=0;a<s.length;a++)for(r=0;r<s[a].length;r++)if(l=i=1,void 0===u[a][r]){for(o=s[a][r].cell;void 0!==s[a+i]&&s[a][r].cell==s[a+i][r].cell;)u[a+i][r]=null,i++;for(;void 0!==s[a][r+l]&&s[a][r].cell==s[a][r+l].cell;){for(var d=0;d<i;d++)u[a+d][r+l]=null;l++}var f=V("span.dt-column-title",o);u[a][r]={cell:o,colspan:l,rowspan:i,title:(f.length?f:V(o)).html()}}return u}}function St(t,e){for(var n,a,r=xt(t,e),o=0;o<e.length;o++){if(n=e[o].row)for(;a=n.firstChild;)n.removeChild(a);for(var i=0;i<r[o].length;i++){var l=r[o][i];l&&V(l.cell).appendTo(n).attr("rowspan",l.rowspan).attr("colspan",l.colspan)}}}function S(t,e){if(r="ssp"==et(s=t),void 0!==(i=s.iInitDisplayStart)&&-1!==i&&(s._iDisplayStart=!r&&i>=s.fnRecordsDisplay()?0:i,s.iInitDisplayStart=-1),-1!==tt(t,"aoPreDrawCallback","preDraw",[t]).indexOf(!1))w(t,!1);else{var l,n=[],a=0,r="ssp"==et(t),o=t.aiDisplay,i=t._iDisplayStart,s=t.fnDisplayEnd(),u=t.aoColumns,c=V(t.nTBody);if(t.bDrawing=!0,r){if(!t.bDestroying&&!e)return 0===t.iDraw&&c.empty().append(Tt(t)),(l=t).iDraw++,w(l,!0),void At(l,function(e){function n(t,e){return"function"==typeof a[t][e]?"function":a[t][e]}var a=e.aoColumns,t=e.oFeatures,r=e.oPreviousSearch,o=e.aoPreSearchCols;return{draw:e.iDraw,columns:a.map(function(e,t){return{data:n(t,"mData"),name:e.sName,searchable:e.bSearchable,orderable:e.bSortable,search:{value:o[t].search,regex:o[t].regex,fixed:Object.keys(e.searchFixed).map(function(t){return{name:t,term:e.searchFixed[t].toString()}})}}}),order:$t(e).map(function(t){return{column:t.col,dir:t.dir,name:n(t.col,"sName")}}),start:e._iDisplayStart,length:t.bPaginate?e._iDisplayLength:-1,search:{value:r.search,regex:r.regex,fixed:Object.keys(e.searchFixed).map(function(t){return{name:t,term:e.searchFixed[t].toString()}})}}}(l),function(t){var e=l,n=Lt(e,t=t),a=Ft(e,"draw",t),r=Ft(e,"recordsTotal",t),t=Ft(e,"recordsFiltered",t);if(void 0!==a){if(+a<e.iDraw)return;e.iDraw=+a}n=n||[],pt(e),e._iRecordsTotal=parseInt(r,10),e._iRecordsDisplay=parseInt(t,10);for(var o=0,i=n.length;o<i;o++)Y(e,n[o]);e.aiDisplay=e.aiDisplayMaster.slice(),S(e,!0),kt(e),w(e,!1)})}else t.iDraw++;if(0!==o.length)for(var d=r?t.aoData.length:s,f=r?0:i;f<d;f++){for(var h=o[f],p=t.aoData[h],g=(null===p.nTr&&bt(t,h),p.nTr),m=0;m<u.length;m++){var v=u[m],b=p.anCells[m];D(b,C.type.className[v.sType]),D(b,v.sClass),D(b,t.oClasses.tbody.cell)}tt(t,"aoRowCallback",null,[g,p._aData,a,f,h]),n.push(g),a++}else n[0]=Tt(t);tt(t,"aoHeaderCallback","header",[V(t.nTHead).children("tr")[0],ht(t),i,s,o]),tt(t,"aoFooterCallback","footer",[V(t.nTFoot).children("tr")[0],ht(t),i,s,o]),c[0].replaceChildren?c[0].replaceChildren.apply(c[0],n):(c.children().detach(),c.append(V(n))),V(t.nTableWrapper).toggleClass("dt-empty-footer",0===V("tr",t.nTFoot).length),tt(t,"aoDrawCallback","draw",[t],!0),t.bSorted=!1,t.bFiltered=!1,t.bDrawing=!1}}function s(t,e,n){var a=t.oFeatures,r=a.bSort,a=a.bFilter;void 0!==n&&!0!==n||(r&&zt(t),a?Nt(t,t.oPreviousSearch):t.aiDisplay=t.aiDisplayMaster.slice()),!0!==e&&(t._iDisplayStart=0),t._drawHold=e,S(t),t._drawHold=!1}function Tt(t){var e=t.oLanguage,n=e.sZeroRecords,a=et(t);return t.iDraw<1&&"ssp"===a||t.iDraw<=1&&"ajax"===a?n=e.sLoadingRecords:e.sEmptyTable&&0===t.fnRecordsTotal()&&(n=e.sEmptyTable),V("<tr/>").append(V("<td />",{colSpan:W(t),class:t.oClasses.empty.row}).html(n))[0]}function wt(t,e,n){for(var i={},a=(V.each(e,function(t,e){if(null!==e){var t=t.replace(/([A-Z])/g," $1").split(" "),n=(i[t[0]]||(i[t[0]]={}),1===t.length?"full":t[1].toLowerCase()),a=i[t[0]],r=function(e,n){V.isPlainObject(n)?Object.keys(n).map(function(t){e.push({feature:t,opts:n[t]})}):e.push(n)};if(a[n]&&a[n].contents||(a[n]={contents:[]}),Array.isArray(e))for(var o=0;o<e.length;o++)r(a[n].contents,e[o]);else r(a[n].contents,e);Array.isArray(a[n].contents)||(a[n].contents=[a[n].contents])}}),Object.keys(i).map(function(t){return 0!==t.indexOf(n)?null:{name:t,val:i[t]}}).filter(function(t){return null!==t})),r=(a.sort(function(t,e){t=+t.name.replace(/[^0-9]/g,"");return+e.name.replace(/[^0-9]/g,"")-t}),"bottom"===n&&a.reverse(),[]),o=0,l=a.length;o<l;o++)a[o].val.full&&(r.push({full:a[o].val.full}),_t(t,r[r.length-1]),delete a[o].val.full),Object.keys(a[o].val).length&&(r.push(a[o].val),_t(t,r[r.length-1]));return r}function _t(o,i){function l(t,e){return C.features[t]||Z(o,0,"Unknown feature: "+t),C.features[t].apply(this,[o,e])}V.each(i,function(t){for(var e,n=i[t].contents,a=0,r=n.length;a<r;a++)n[a]&&("string"==typeof n[a]?n[a]=l(n[a],null):V.isPlainObject(n[a])?n[a]=l(n[a].feature,n[a].opts):"function"==typeof n[a].node?n[a]=n[a].node(o):"function"==typeof n[a]&&(e=n[a](o),n[a]="function"==typeof e.node?e.node():e))})}function Ct(e){var a,t=e.oClasses,n=V(e.nTable),r=V("<div/>").attr({id:e.sTableId+"_wrapper",class:t.container}).insertBefore(n);if(e.nTableWrapper=r[0],e.sDom)for(var o,i,l,s,u,c,d=e,t=e.sDom,f=r,h=t.match(/(".*?")|('.*?')|./g),p=0;p<h.length;p++)o=null,"<"==(i=h[p])?(l=V("<div/>"),"'"!=(s=h[p+1])[0]&&'"'!=s[0]||(s=s.replace(/['"]/g,""),u="",-1!=s.indexOf(".")?(c=s.split("."),u=c[0],c=c[1]):"#"==s[0]?u=s:c=s,l.attr("id",u.substring(1)).addClass(c),p++),f.append(l),f=l):">"==i?f=f.parent():"t"==i?o=Wt(d):$.ext.feature.forEach(function(t){i==t.cFeature&&(o=t.fnInit(d))}),o&&f.append(o);else{var n=wt(e,e.layout,"top"),t=wt(e,e.layout,"bottom"),g=te(e,"layout");n.forEach(function(t){g(e,r,t)}),g(e,r,{full:{table:!0,contents:[Wt(e)]}}),t.forEach(function(t){g(e,r,t)})}var n=e,t=n.nTable,m=""!==n.oScroll.sX||""!==n.oScroll.sY;n.oFeatures.bProcessing&&(a=V("<div/>",{id:n.sTableId+"_processing",class:n.oClasses.processing.container,role:"status"}).html(n.oLanguage.sProcessing).append("<div><div></div><div></div><div></div><div></div></div>"),m?a.prependTo(V("div.dt-scroll",n.nTableWrapper)):a.insertBefore(t),V(t).on("processing.dt.DT",function(t,e,n){a.css("display",n?"block":"none")}))}function It(t,e,n){for(var a,r,o,i,l,s,u=t.aoColumns,c=V(e).children("tr"),d=e&&"thead"===e.nodeName.toLowerCase(),f=[],h=0,p=c.length;h<p;h++)f.push([]);for(h=0,p=c.length;h<p;h++)for(r=(a=c[h]).firstChild;r;){if("TD"==r.nodeName.toUpperCase()||"TH"==r.nodeName.toUpperCase()){var g,m,v,b,y,D=[];for(b=(b=+r.getAttribute("colspan"))&&0!=b&&1!=b?b:1,y=(y=+r.getAttribute("rowspan"))&&0!=y&&1!=y?y:1,l=function(t,e,n){for(var a=t[e];a[n];)n++;return n}(f,h,0),s=1==b,n&&(s&&(ot(t,l,V(r).data()),g=u[l],m=r.getAttribute("width")||null,(v=r.style.width.match(/width:\s*(\d+[pxem%]+)/))&&(m=v[1]),g.sWidthOrig=g.sWidth||m,d?(null===g.sTitle||g.autoTitle||(r.innerHTML=g.sTitle),!g.sTitle&&s&&(g.sTitle=I(r.innerHTML),g.autoTitle=!0)):g.footer&&(r.innerHTML=g.footer),g.ariaTitle||(g.ariaTitle=V(r).attr("aria-label")||g.sTitle),g.className)&&V(r).addClass(g.className),0===V("span.dt-column-title",r).length&&V("<span>").addClass("dt-column-title").append(r.childNodes).appendTo(r),d)&&0===V("span.dt-column-order",r).length&&V("<span>").addClass("dt-column-order").appendTo(r),i=0;i<b;i++){for(o=0;o<y;o++)f[h+o][l+i]={cell:r,unique:s},f[h+o].row=a;D.push(l+i)}r.setAttribute("data-dt-column",x(D).join(","))}r=r.nextSibling}return f}function At(n,t,a){function e(t){var e=n.jqXHR?n.jqXHR.status:null;(null===t||"number"==typeof e&&204==e)&&Lt(n,t={},[]),(e=t.error||t.sError)&&Z(n,0,e),n.json=t,tt(n,null,"xhr",[n,t,n.jqXHR],!0),a(t)}var r,o=n.ajax,i=n.oInstance,l=(V.isPlainObject(o)&&o.data&&(l="function"==typeof(r=o.data)?r(t,n):r,t="function"==typeof r&&l?l:V.extend(!0,t,l),delete o.data),{url:"string"==typeof o?o:"",data:t,success:e,dataType:"json",cache:!1,type:n.sServerMethod,error:function(t,e){-1===tt(n,null,"xhr",[n,null,n.jqXHR],!0).indexOf(!0)&&("parsererror"==e?Z(n,0,"Invalid JSON response",1):4===t.readyState&&Z(n,0,"Ajax error",7)),w(n,!1)}});V.isPlainObject(o)&&V.extend(l,o),n.oAjaxData=t,tt(n,null,"preXhr",[n,t,l],!0),"function"==typeof o?n.jqXHR=o.call(i,t,e,n):""===o.url?(i={},$.util.set(o.dataSrc)(i,[]),e(i)):(n.jqXHR=V.ajax(l),r&&(o.data=r))}function Lt(t,e,n){var a="data";if(V.isPlainObject(t.ajax)&&void 0!==t.ajax.dataSrc&&("string"==typeof(t=t.ajax.dataSrc)||"function"==typeof t?a=t:void 0!==t.data&&(a=t.data)),!n)return"data"===a?e.aaData||e[a]:""!==a?J(a)(e):e;m(a)(e,n)}function Ft(t,e,n){var t=V.isPlainObject(t.ajax)?t.ajax.dataSrc:null;return t&&t[e]?J(t[e])(n):(t="","draw"===e?t="sEcho":"recordsTotal"===e?t="iTotalRecords":"recordsFiltered"===e&&(t="iTotalDisplayRecords"),void 0!==n[t]?n[t]:n[e])}function Nt(n,t){var e=n.aoPreSearchCols;if(B(n),"ssp"!=et(n)){for(var a,r,o,i,l,s=n,u=s.aoColumns,c=s.aoData,d=0;d<c.length;d++)if(c[d]&&!(l=c[d])._aFilterData){for(o=[],a=0,r=u.length;a<r;a++)u[a].bSearchable?"string"!=typeof(i=null===(i=G(s,d,a,"filter"))?"":i)&&i.toString&&(i=i.toString()):i="",i.indexOf&&-1!==i.indexOf("&")&&(Rt.innerHTML=i,i=Ot?Rt.textContent:Rt.innerText),i.replace&&(i=i.replace(/[\r\n\u2028]/g,"")),o.push(i);l._aFilterData=o,l._sFilterRow=o.join("  "),0}n.aiDisplay=n.aiDisplayMaster.slice(),jt(n.aiDisplay,n,t.search,t),V.each(n.searchFixed,function(t,e){jt(n.aiDisplay,n,e,{})});for(var f=0;f<e.length;f++){var h=e[f];jt(n.aiDisplay,n,h.search,h,f),V.each(n.aoColumns[f].searchFixed,function(t,e){jt(n.aiDisplay,n,e,{},f)})}for(var p,g,m=n,v=$.ext.search,b=m.aiDisplay,y=0,D=v.length;y<D;y++){for(var x=[],S=0,T=b.length;S<T;S++)g=b[S],p=m.aoData[g],v[y](m,p._aFilterData,g,p._aData,S)&&x.push(g);b.length=0,b.push.apply(b,x)}}n.bFiltered=!0,tt(n,null,"search",[n])}function jt(t,e,n,a,r){if(""!==n){for(var o=0,i=[],l="function"==typeof n?n:null,s=n instanceof RegExp?n:l?null:function(t,e){var a=[],e=V.extend({},{boundary:!1,caseInsensitive:!0,exact:!1,regex:!1,smart:!0},e);"string"!=typeof t&&(t=t.toString());if(t=O(t),e.exact)return new RegExp("^"+Pt(t)+"$",e.caseInsensitive?"i":"");{var n,r,o;t=e.regex?t:Pt(t),e.smart&&(n=(t.match(/!?["\u201C][^"\u201D]+["\u201D]|[^ ]+/g)||[""]).map(function(t){var e,n=!1;return"!"===t.charAt(0)&&(n=!0,t=t.substring(1)),'"'===t.charAt(0)?t=(e=t.match(/^"(.*)"$/))?e[1]:t:"“"===t.charAt(0)&&(t=(e=t.match(/^\u201C(.*)\u201D$/))?e[1]:t),n&&(1<t.length&&a.push("(?!"+t+")"),t=""),t.replace(/"/g,"")}),r=a.length?a.join(""):"",o=e.boundary?"\\b":"",t="^(?=.*?"+o+n.join(")(?=.*?"+o)+")("+r+".)*$")}return new RegExp(t,e.caseInsensitive?"i":"")}(n,a),o=0;o<t.length;o++){var u=e.aoData[t[o]],c=void 0===r?u._sFilterRow:u._aFilterData[r];(l&&l(c,u._aData,t[o],r)||s&&s.test(c))&&i.push(t[o])}for(t.length=i.length,o=0;o<i.length;o++)t[o]=i[o]}}var Pt=$.util.escapeRegex,Rt=V("<div>")[0],Ot=void 0!==Rt.textContent;function Et(n){var a,t,e,r,o,i,l=n.iInitDisplayStart;n.bInitialised?(Dt(n,"header"),Dt(n,"footer"),St(n,n.aoHeader),St(n,n.aoFooter),Ct(n),e=(t=n).nTHead,i=e.querySelectorAll("tr"),r=t.bSortCellsTop,o=':not([data-dt-order="disable"]):not([data-dt-order="icon-only"])',!0===r?e=i[0]:!1===r&&(e=i[i.length-1]),Vt(t,e,e===t.nTHead?"tr"+o+" th"+o+", tr"+o+" td"+o:"th"+o+", td"+o),Ut(t,r=[],t.aaSorting),t.aaSorting=r,Bt(n),w(n,!0),tt(n,null,"preInit",[n],!0),s(n),"ssp"!=(i=et(n))&&("ajax"==i?At(n,{},function(t){var e=Lt(n,t);for(a=0;a<e.length;a++)Y(n,e[a]);n.iInitDisplayStart=l,s(n),w(n,!1),kt(n)}):(kt(n),w(n,!1)))):setTimeout(function(){Et(n)},200)}function kt(t){var e;t._bInitComplete||(e=[t,t.json],t._bInitComplete=!0,M(t),tt(t,null,"plugin-init",e,!0),tt(t,"aoInitComplete","init",e,!0))}function Mt(t,e){e=parseInt(e,10);t._iDisplayLength=e,Kt(t),tt(t,null,"length",[t,e])}function Ht(t,e,n){var a=t._iDisplayStart,r=t._iDisplayLength,o=t.fnRecordsDisplay();if(0===o||-1===r)a=0;else if("number"==typeof e)o<(a=e*r)&&(a=0);else if("first"==e)a=0;else if("previous"==e)(a=0<=r?a-r:0)<0&&(a=0);else if("next"==e)a+r<o&&(a+=r);else if("last"==e)a=Math.floor((o-1)/r)*r;else{if("ellipsis"===e)return;Z(t,0,"Unknown paging action: "+e,5)}o=t._iDisplayStart!==a;t._iDisplayStart=a,tt(t,null,o?"page":"page-nc",[t]),o&&n&&S(t)}function w(t,e){tt(t,null,"processing",[t,e])}function Wt(t){var e,n,a,r,o,i,l,s,u,c,d,f,h,p=V(t.nTable),g=t.oScroll;return""===g.sX&&""===g.sY?t.nTable:(e=g.sX,n=g.sY,a=t.oClasses.scrolling,o=(r=t.captionNode)?r._captionSide:null,u=V(p[0].cloneNode(!1)),i=V(p[0].cloneNode(!1)),c=function(t){return t?A(t):null},(l=p.children("tfoot")).length||(l=null),u=V(s="<div/>",{class:a.container}).append(V(s,{class:a.header.self}).css({overflow:"hidden",position:"relative",border:0,width:e?c(e):"100%"}).append(V(s,{class:a.header.inner}).css({"box-sizing":"content-box",width:g.sXInner||"100%"}).append(u.removeAttr("id").css("margin-left",0).append("top"===o?r:null).append(p.children("thead"))))).append(V(s,{class:a.body}).css({position:"relative",overflow:"auto",width:c(e)}).append(p)),l&&u.append(V(s,{class:a.footer.self}).css({overflow:"hidden",border:0,width:e?c(e):"100%"}).append(V(s,{class:a.footer.inner}).append(i.removeAttr("id").css("margin-left",0).append("bottom"===o?r:null).append(p.children("tfoot"))))),c=u.children(),d=c[0],f=c[1],h=l?c[2]:null,V(f).on("scroll.DT",function(){var t=this.scrollLeft;d.scrollLeft=t,l&&(h.scrollLeft=t)}),V("th, td",d).on("focus",function(){var t=d.scrollLeft;f.scrollLeft=t,l&&(f.scrollLeft=t)}),V(f).css("max-height",n),g.bCollapse||V(f).css("height",n),t.nScrollHead=d,t.nScrollBody=f,t.nScrollFoot=h,t.aoDrawCallback.push(Xt),u[0])}function Xt(e){var t=e.oScroll.iBarWidth,n=V(e.nScrollHead).children("div"),a=n.children("table"),r=e.nScrollBody,o=V(r),i=V(e.nScrollFoot).children("div"),l=i.children("table"),s=V(e.nTHead),u=V(e.nTable),c=e.nTFoot&&V("th, td",e.nTFoot).length?V(e.nTFoot):null,d=e.oBrowser,f=r.scrollHeight>r.clientHeight;if(e.scrollBarVis!==f&&void 0!==e.scrollBarVis)e.scrollBarVis=f,M(e);else{if(e.scrollBarVis=f,u.children("thead, tfoot").remove(),(f=s.clone().prependTo(u)).find("th, td").removeAttr("tabindex"),f.find("[id]").removeAttr("id"),c&&(m=c.clone().prependTo(u)).find("[id]").removeAttr("id"),e.aiDisplay.length)for(var h=u.children("tbody").eq(0).children("tr").eq(0).children("th, td").map(function(t){return{idx:H(e,t),width:V(this).outerWidth()}}),p=0;p<h.length;p++){var g=e.aoColumns[h[p].idx].colEl[0];g.style.width.replace("px","")!==h[p].width&&(g.style.width=h[p].width+"px")}a.find("colgroup").remove(),a.append(e.colgroup.clone()),c&&(l.find("colgroup").remove(),l.append(e.colgroup.clone())),V("th, td",f).each(function(){V(this.childNodes).wrapAll('<div class="dt-scroll-sizing">')}),c&&V("th, td",m).each(function(){V(this.childNodes).wrapAll('<div class="dt-scroll-sizing">')});var s=Math.floor(u.height())>r.clientHeight||"scroll"==o.css("overflow-y"),f="padding"+(d.bScrollbarLeft?"Left":"Right"),m=u.outerWidth();a.css("width",A(m)),n.css("width",A(m)).css(f,s?t+"px":"0px"),c&&(l.css("width",A(m)),i.css("width",A(m)).css(f,s?t+"px":"0px")),u.children("colgroup").prependTo(u),o.trigger("scroll"),!e.bSorted&&!e.bFiltered||e._drawHold||(r.scrollTop=0)}}function A(t){return null===t?"0px":"number"==typeof t?t<0?"0px":t+"px":t.match(/\d$/)?t+"px":t}function Bt(t){var e=t.aoColumns;for(t.colgroup.empty(),a=0;a<e.length;a++)e[a].bVisible&&t.colgroup.append(e[a].colEl)}function Vt(o,t,e,i,l){Qt(t,e,function(t){var e=!1,n=void 0===i?st(t.target):[i];if(n.length){for(var a=0,r=n.length;a<r;a++)if(!1!==function(t,e,n,a){function r(t,e){var n=t._idx;return(n=void 0===n?s.indexOf(t[1]):n)+1<s.length?n+1:e?null:0}var o,i=t.aoColumns[e],l=t.aaSorting,s=i.asSorting;if(!i.bSortable)return!1;"number"==typeof l[0]&&(l=t.aaSorting=[l]);(a||n)&&t.oFeatures.bSortMulti?-1!==(i=f(l,"0").indexOf(e))?null===(o=null===(o=r(l[i],!0))&&1===l.length?0:o)?l.splice(i,1):(l[i][1]=s[o],l[i]._idx=o):(a?l.push([e,s[0],0]):l.push([e,l[0][1],0]),l[l.length-1]._idx=0):l.length&&l[0][0]==e?(o=r(l[0]),l.length=1,l[0][1]=s[o],l[0]._idx=o):(l.length=0,l.push([e,s[0]]),l[0]._idx=0)}(o,n[a],a,t.shiftKey)&&(e=!0),1===o.aaSorting.length&&""===o.aaSorting[0][1])break;e&&(w(o,!0),setTimeout(function(){zt(o),qt(o,o.aiDisplay),w(o,!1),s(o,!1,!1),l&&l()},0))}})}function qt(t,e){if(!(e.length<2)){for(var n=t.aiDisplayMaster,a={},r={},o=0;o<n.length;o++)a[n[o]]=o;for(o=0;o<e.length;o++)r[e[o]]=a[e[o]];e.sort(function(t,e){return r[t]-r[e]})}}function Ut(n,a,t){function e(t){var e;V.isPlainObject(t)?void 0!==t.idx?a.push([t.idx,t.dir]):t.name&&-1!==(e=f(n.aoColumns,"sName").indexOf(t.name))&&a.push([e,t.dir]):a.push(t)}if(V.isPlainObject(t))e(t);else if(t.length&&"number"==typeof t[0])e(t);else if(t.length)for(var r=0;r<t.length;r++)e(t[r])}function $t(t){var e,n,a,r,o,i,l,s=[],u=$.ext.type.order,c=t.aoColumns,d=t.aaSortingFixed,f=V.isPlainObject(d),h=[];if(t.oFeatures.bSort)for(Array.isArray(d)&&Ut(t,h,d),f&&d.pre&&Ut(t,h,d.pre),Ut(t,h,t.aaSorting),f&&d.post&&Ut(t,h,d.post),e=0;e<h.length;e++)if(c[l=h[e][0]])for(n=0,a=(r=c[l].aDataSort).length;n<a;n++)i=c[o=r[n]].sType||"string",void 0===h[e]._idx&&(h[e]._idx=c[o].asSorting.indexOf(h[e][1])),h[e][1]&&s.push({src:l,col:o,dir:h[e][1],index:h[e]._idx,type:i,formatter:u[i+"-pre"],sorter:u[i+"-"+h[e][1]]});return s}function zt(t,e,n){var a,r,o,i,l,c,d=[],s=$.ext.type.order,f=t.aoData,u=t.aiDisplayMaster;for(B(t),void 0!==e?(l=t.aoColumns[e],c=[{src:e,col:e,dir:n,index:0,type:l.sType,formatter:s[l.sType+"-pre"],sorter:s[l.sType+"-"+n]}],u=u.slice()):c=$t(t),a=0,r=c.length;a<r;a++){i=c[a],S=x=D=g=p=h=y=b=v=m=void 0;var h,p,g,m=t,v=i.col,b=m.aoColumns[v],y=$.ext.order[b.sSortDataType];y&&(h=y.call(m.oInstance,m,v,T(m,v)));for(var D=$.ext.type.order[b.sType+"-pre"],x=m.aoData,S=0;S<x.length;S++)x[S]&&((p=x[S])._aSortData||(p._aSortData=[]),p._aSortData[v]&&!y||(g=y?h[S]:G(m,S,v,"sort"),p._aSortData[v]=D?D(g,m):g))}if("ssp"!=et(t)&&0!==c.length){for(a=0,o=u.length;a<o;a++)d[a]=a;c.length&&"desc"===c[0].dir&&d.reverse(),u.sort(function(t,e){for(var n,a,r,o,i=c.length,l=f[t]._aSortData,s=f[e]._aSortData,u=0;u<i;u++)if(n=l[(o=c[u]).col],a=s[o.col],o.sorter){if(0!==(r=o.sorter(n,a)))return r}else if(0!==(r=n<a?-1:a<n?1:0))return"asc"===o.dir?r:-r;return(n=d[t])<(a=d[e])?-1:a<n?1:0})}else 0===c.length&&u.sort(function(t,e){return t<e?-1:e<t?1:0});return void 0===e&&(t.bSorted=!0,tt(t,null,"order",[t,c])),u}function Yt(t){var e,n,a,r=t.aLastSort,o=t.oClasses.order.position,i=$t(t),l=t.oFeatures;if(l.bSort&&l.bSortClasses){for(e=0,n=r.length;e<n;e++)a=r[e].src,V(f(t.aoData,"anCells",a)).removeClass(o+(e<2?e+1:3));for(e=0,n=i.length;e<n;e++)a=i[e].src,V(f(t.aoData,"anCells",a)).addClass(o+(e<2?e+1:3))}t.aLastSort=i}function Gt(n){var t;n._bLoadingState||(t={time:+new Date,start:n._iDisplayStart,length:n._iDisplayLength,order:V.extend(!0,[],n.aaSorting),search:V.extend({},n.oPreviousSearch),columns:n.aoColumns.map(function(t,e){return{visible:t.bVisible,search:V.extend({},n.aoPreSearchCols[e])}})},n.oSavedState=t,tt(n,"aoStateSaveParams","stateSaveParams",[n,t]),n.oFeatures.bStateSave&&!n.bDestroying&&n.fnStateSaveCallback.call(n.oInstance,n,t))}function Jt(n,t,e){var a,r,o=n.aoColumns,i=(n._bLoadingState=!0,n._bInitComplete?new $.Api(n):null);if(t&&t.time){var l=n.iStateDuration;if(0<l&&t.time<+new Date-1e3*l)n._bLoadingState=!1;else if(-1!==tt(n,"aoStateLoadParams","stateLoadParams",[n,t]).indexOf(!1))n._bLoadingState=!1;else if(t.columns&&o.length!==t.columns.length)n._bLoadingState=!1;else{if(n.oLoadedState=V.extend(!0,{},t),tt(n,null,"stateLoadInit",[n,t],!0),void 0!==t.length&&(i?i.page.len(t.length):n._iDisplayLength=t.length),void 0!==t.start&&(null===i?(n._iDisplayStart=t.start,n.iInitDisplayStart=t.start):Ht(n,t.start/n._iDisplayLength)),void 0!==t.order&&(n.aaSorting=[],V.each(t.order,function(t,e){n.aaSorting.push(e[0]>=o.length?[0,e[1]]:e)})),void 0!==t.search&&V.extend(n.oPreviousSearch,t.search),t.columns){for(a=0,r=t.columns.length;a<r;a++){var s=t.columns[a];void 0!==s.visible&&(i?i.column(a).visible(s.visible,!1):o[a].bVisible=s.visible),void 0!==s.search&&V.extend(n.aoPreSearchCols[a],s.search)}i&&i.columns.adjust()}n._bLoadingState=!1,tt(n,"aoStateLoaded","stateLoaded",[n,t])}}else n._bLoadingState=!1;e()}function Z(t,e,n,a){if(n="DataTables warning: "+(t?"table id="+t.sTableId+" - ":"")+n,a&&(n+=". For more information about this error, please see https://datatables.net/tn/"+a),e)q.console&&console.log&&console.log(n);else{e=$.ext,e=e.sErrMode||e.errMode;if(t&&tt(t,null,"dt-error",[t,a,n],!0),"alert"==e)alert(n);else{if("throw"==e)throw new Error(n);"function"==typeof e&&e(t,a,n)}}}function Q(n,a,t,e){Array.isArray(t)?V.each(t,function(t,e){Array.isArray(e)?Q(n,a,e[0],e[1]):Q(n,a,e)}):(void 0===e&&(e=t),void 0!==a[t]&&(n[e]=a[t]))}function Zt(t,e,n){var a,r;for(r in e)Object.prototype.hasOwnProperty.call(e,r)&&(a=e[r],V.isPlainObject(a)?(V.isPlainObject(t[r])||(t[r]={}),V.extend(!0,t[r],a)):n&&"data"!==r&&"aaData"!==r&&Array.isArray(a)?t[r]=a.slice():t[r]=a);return t}function Qt(t,e,n){V(t).on("click.DT",e,function(t){n(t)}).on("keypress.DT",e,function(t){13===t.which&&(t.preventDefault(),n(t))}).on("selectstart.DT",e,function(){return!1})}function K(t,e,n){n&&t[e].push(n)}function tt(e,t,n,a,r){var o=[];return t&&(o=e[t].slice().reverse().map(function(t){return t.apply(e.oInstance,a)})),null!==n&&(t=V.Event(n+".dt"),n=V(e.nTable),t.dt=e.api,n[r?"trigger":"triggerHandler"](t,a),r&&0===n.parents("body").length&&V("body").trigger(t,a),o.push(t.result)),o}function Kt(t){var e=t._iDisplayStart,n=t.fnDisplayEnd(),a=t._iDisplayLength;n<=e&&(e=n-a),e-=e%a,t._iDisplayStart=e=-1===a||e<0?0:e}function te(t,e){var t=t.renderer,n=$.ext.renderer[e];return V.isPlainObject(t)&&t[e]?n[t[e]]||n._:"string"==typeof t&&n[t]||n._}function et(t){return t.oFeatures.bServerSide?"ssp":t.ajax?"ajax":"dom"}function ee(t,e,n){var a=t.fnFormatNumber,r=t._iDisplayStart+1,o=t._iDisplayLength,i=t.fnRecordsDisplay(),l=t.fnRecordsTotal(),s=-1===o;return e.replace(/_START_/g,a.call(t,r)).replace(/_END_/g,a.call(t,t.fnDisplayEnd())).replace(/_MAX_/g,a.call(t,l)).replace(/_TOTAL_/g,a.call(t,i)).replace(/_PAGE_/g,a.call(t,s?1:Math.ceil(r/o))).replace(/_PAGES_/g,a.call(t,s?1:Math.ceil(i/o))).replace(/_ENTRIES_/g,t.api.i18n("entries","",n)).replace(/_ENTRIES-MAX_/g,t.api.i18n("entries","",l)).replace(/_ENTRIES-TOTAL_/g,t.api.i18n("entries","",i))}var ne=[],n=Array.prototype;U=function(t,e){if(!(this instanceof U))return new U(t,e);function n(t){t=t,e=$.settings,a=f(e,"nTable");var n,e,a,r=t?t.nTable&&t.oFeatures?[t]:t.nodeName&&"table"===t.nodeName.toLowerCase()?-1!==(r=a.indexOf(t))?[e[r]]:null:t&&"function"==typeof t.settings?t.settings().toArray():("string"==typeof t?n=V(t).get():t instanceof V&&(n=t.get()),n?e.filter(function(t,e){return n.includes(a[e])}):void 0):[];r&&o.push.apply(o,r)}var o=[];if(Array.isArray(t))for(var a=0,r=t.length;a<r;a++)n(t[a]);else n(t);this.context=1<o.length?x(o):o,e&&this.push.apply(this,e),this.selector={rows:null,cols:null,opts:null},U.extend(this,this,ne)},$.Api=U,V.extend(U.prototype,{any:function(){return 0!==this.count()},context:[],count:function(){return this.flatten().length},each:function(t){for(var e=0,n=this.length;e<n;e++)t.call(this,this[e],e,this);return this},eq:function(t){var e=this.context;return e.length>t?new U(e[t],this[t]):null},filter:function(t){t=n.filter.call(this,t,this);return new U(this.context,t)},flatten:function(){var t=[];return new U(this.context,t.concat.apply(t,this.toArray()))},get:function(t){return this[t]},join:n.join,includes:function(t){return-1!==this.indexOf(t)},indexOf:n.indexOf,iterator:function(t,e,n,a){var r,o,i,l,s,u,c,d,f=[],h=this.context,p=this.selector;for("string"==typeof t&&(a=n,n=e,e=t,t=!1),o=0,i=h.length;o<i;o++){var g=new U(h[o]);if("table"===e)void 0!==(r=n.call(g,h[o],o))&&f.push(r);else if("columns"===e||"rows"===e)void 0!==(r=n.call(g,h[o],this[o],o))&&f.push(r);else if("every"===e||"column"===e||"column-rows"===e||"row"===e||"cell"===e)for(c=this[o],"column-rows"===e&&(u=he(h[o],p.opts)),l=0,s=c.length;l<s;l++)d=c[l],void 0!==(r="cell"===e?n.call(g,h[o],d.row,d.column,o,l):n.call(g,h[o],d,o,l,u))&&f.push(r)}return f.length||a?((t=(a=new U(h,t?f.concat.apply([],f):f)).selector).rows=p.rows,t.cols=p.cols,t.opts=p.opts,a):this},lastIndexOf:n.lastIndexOf,length:0,map:function(t){t=n.map.call(this,t,this);return new U(this.context,t)},pluck:function(t){var e=$.util.get(t);return this.map(function(t){return e(t)})},pop:n.pop,push:n.push,reduce:n.reduce,reduceRight:n.reduceRight,reverse:n.reverse,selector:null,shift:n.shift,slice:function(){return new U(this.context,this)},sort:n.sort,splice:n.splice,toArray:function(){return n.slice.call(this)},to$:function(){return V(this)},toJQuery:function(){return V(this)},unique:function(){return new U(this.context,x(this.toArray()))},unshift:n.unshift}),q.__apiStruct=ne,U.extend=function(t,e,n){if(n.length&&e&&(e instanceof U||e.__dt_wrapper))for(var a,r=0,o=n.length;r<o;r++)"__proto__"!==(a=n[r]).name&&(e[a.name]="function"===a.type?function(e,n,a){return function(){var t=n.apply(e||this,arguments);return U.extend(t,t,a.methodExt),t}}(t,a.val,a):"object"===a.type?{}:a.val,e[a.name].__dt_wrapper=!0,U.extend(t,e[a.name],a.propExt))},U.register=e=function(t,e){if(Array.isArray(t))for(var n=0,a=t.length;n<a;n++)U.register(t[n],e);else for(var r=t.split("."),o=ne,i=0,l=r.length;i<l;i++){var s,u,c=function(t,e){for(var n=0,a=t.length;n<a;n++)if(t[n].name===e)return t[n];return null}(o,u=(s=-1!==r[i].indexOf("()"))?r[i].replace("()",""):r[i]);c||o.push(c={name:u,val:{},methodExt:[],propExt:[],type:"object"}),i===l-1?(c.val=e,c.type="function"==typeof e?"function":V.isPlainObject(e)?"object":"other"):o=s?c.methodExt:c.propExt}},U.registerPlural=t=function(t,e,n){U.register(t,n),U.register(e,function(){var t=n.apply(this,arguments);return t===this?this:t instanceof U?t.length?Array.isArray(t[0])?new U(t.context,t[0]):t[0]:void 0:t})};function ae(t,e){var n,a;return Array.isArray(t)?(n=[],t.forEach(function(t){t=ae(t,e);n.push.apply(n,t)}),n.filter(function(t){return t})):"number"==typeof t?[e[t]]:(a=e.map(function(t){return t.nTable}),V(a).filter(t).map(function(){var t=a.indexOf(this);return e[t]}).toArray())}function re(r,o,t){var e,n;t&&(e=new U(r)).one("draw",function(){t(e.ajax.json())}),"ssp"==et(r)?s(r,o):(w(r,!0),(n=r.jqXHR)&&4!==n.readyState&&n.abort(),At(r,{},function(t){pt(r);for(var e=Lt(r,t),n=0,a=e.length;n<a;n++)Y(r,e[n]);s(r,o),kt(r),w(r,!1)}))}function oe(t,e,n,a,r){for(var o,i,l,s,u=[],c=typeof e,d=0,f=(e=e&&"string"!=c&&"function"!=c&&void 0!==e.length?e:[e]).length;d<f;d++)for(l=0,s=(i=e[d]&&e[d].split&&!e[d].match(/[[(:]/)?e[d].split(","):[e[d]]).length;l<s;l++)(o=(o=n("string"==typeof i[l]?i[l].trim():i[l])).filter(function(t){return null!=t}))&&o.length&&(u=u.concat(o));var h=C.selector[t];if(h.length)for(d=0,f=h.length;d<f;d++)u=h[d](a,r,u);return x(u)}function ie(t){return(t=t||{}).filter&&void 0===t.search&&(t.search=t.filter),V.extend({search:"none",order:"current",page:"all"},t)}function le(t){var e=new U(t.context[0]);return t.length&&e.push(t[0]),e.selector=t.selector,e.length&&1<e[0].length&&e[0].splice(1),e}e("tables()",function(t){return null!=t?new U(ae(t,this.context)):this}),e("table()",function(t){var t=this.tables(t),e=t.context;return e.length?new U(e[0]):t}),[["nodes","node","nTable"],["body","body","nTBody"],["header","header","nTHead"],["footer","footer","nTFoot"]].forEach(function(e){t("tables()."+e[0]+"()","table()."+e[1]+"()",function(){return this.iterator("table",function(t){return t[e[2]]},1)})}),[["header","aoHeader"],["footer","aoFooter"]].forEach(function(n){e("table()."+n[0]+".structure()",function(t){var t=this.columns(t).indexes().flatten(),e=this.context[0];return xt(e,e[n[1]],t)})}),t("tables().containers()","table().container()",function(){return this.iterator("table",function(t){return t.nTableWrapper},1)}),e("tables().every()",function(n){var a=this;return this.iterator("table",function(t,e){n.call(a.table(e),e)})}),e("caption()",function(r,o){var t,e=this.context;return void 0===r?(t=e[0].captionNode)&&e.length?t.innerHTML:null:this.iterator("table",function(t){var e=V(t.nTable),n=V(t.captionNode),a=V(t.nTableWrapper);n.length||(n=V("<caption/>").html(r),t.captionNode=n[0],o)||(e.prepend(n),o=n.css("caption-side")),n.html(r),o&&(n.css("caption-side",o),n[0]._captionSide=o),(a.find("div.dataTables_scroll").length?(t="top"===o?"Head":"Foot",a.find("div.dataTables_scroll"+t+" table")):e).prepend(n)},1)}),e("caption.node()",function(){var t=this.context;return t.length?t[0].captionNode:null}),e("draw()",function(e){return this.iterator("table",function(t){"page"===e?S(t):s(t,!1===(e="string"==typeof e?"full-hold"!==e:e))})}),e("page()",function(e){return void 0===e?this.page.info().page:this.iterator("table",function(t){Ht(t,e)})}),e("page.info()",function(){var t,e,n,a,r;if(0!==this.context.length)return e=(t=this.context[0])._iDisplayStart,n=t.oFeatures.bPaginate?t._iDisplayLength:-1,a=t.fnRecordsDisplay(),{page:(r=-1===n)?0:Math.floor(e/n),pages:r?1:Math.ceil(a/n),start:e,end:t.fnDisplayEnd(),length:n,recordsTotal:t.fnRecordsTotal(),recordsDisplay:a,serverSide:"ssp"===et(t)}}),e("page.len()",function(e){return void 0===e?0!==this.context.length?this.context[0]._iDisplayLength:void 0:this.iterator("table",function(t){Mt(t,e)})}),e("ajax.json()",function(){var t=this.context;if(0<t.length)return t[0].json}),e("ajax.params()",function(){var t=this.context;if(0<t.length)return t[0].oAjaxData}),e("ajax.reload()",function(e,n){return this.iterator("table",function(t){re(t,!1===n,e)})}),e("ajax.url()",function(e){var t=this.context;return void 0===e?0===t.length?void 0:(t=t[0],V.isPlainObject(t.ajax)?t.ajax.url:t.ajax):this.iterator("table",function(t){V.isPlainObject(t.ajax)?t.ajax.url=e:t.ajax=e})}),e("ajax.url().load()",function(e,n){return this.iterator("table",function(t){re(t,!1===n,e)})});function se(o,i,t,e){function l(t,e){var n;if(Array.isArray(t)||t instanceof V)for(var a=0,r=t.length;a<r;a++)l(t[a],e);else t.nodeName&&"tr"===t.nodeName.toLowerCase()?(t.setAttribute("data-dt-row",i.idx),s.push(t)):(n=V("<tr><td></td></tr>").attr("data-dt-row",i.idx).addClass(e),V("td",n).addClass(e).html(t)[0].colSpan=W(o),s.push(n[0]))}var s=[];l(t,e),i._details&&i._details.detach(),i._details=V(s),i._detailsShow&&i._details.insertAfter(i.nTr)}function ue(t,e){var n=t.context;if(n.length&&t.length){var a=n[0].aoData[t[0]];if(a._details){(a._detailsShow=e)?(a._details.insertAfter(a.nTr),V(a.nTr).addClass("dt-hasChild")):(a._details.detach(),V(a.nTr).removeClass("dt-hasChild")),tt(n[0],null,"childRow",[e,t.row(t[0])]);var i=n[0],r=new U(i),a=".dt.DT_details",e="draw"+a,t="column-sizing"+a,a="destroy"+a,l=i.aoData;if(r.off(e+" "+t+" "+a),f(l,"_details").length>0){r.on(e,function(t,e){if(i!==e)return;r.rows({page:"current"}).eq(0).each(function(t){var e=l[t];if(e._detailsShow)e._details.insertAfter(e.nTr)})});r.on(t,function(t,e){if(i!==e)return;var n,a=W(e);for(var r=0,o=l.length;r<o;r++){n=l[r];if(n&&n._details)n._details.each(function(){var t=V(this).children("td");if(t.length==1)t.attr("colspan",a)})}});r.on(a,function(t,e){if(i!==e)return;for(var n=0,a=l.length;n<a;n++)if(l[n]&&l[n]._details)me(r,n)})}ge(n)}}}function ce(t,e,n,a,r,o){for(var i=[],l=0,s=r.length;l<s;l++)i.push(G(t,r[l],e,o));return i}function de(t,e,n){var a=t.aoHeader;return a[void 0!==n?n:t.bSortCellsTop?0:a.length-1][e].cell}function fe(e,n){return function(t){return y(t)||"string"!=typeof t||(t=t.replace(d," "),e&&(t=I(t)),n&&(t=O(t,!1))),t}}var he=function(t,e){var n,a=[],r=t.aiDisplay,o=t.aiDisplayMaster,i=e.search,l=e.order,e=e.page;if("ssp"==et(t))return"removed"===i?[]:h(0,o.length);if("current"==e)for(u=t._iDisplayStart,c=t.fnDisplayEnd();u<c;u++)a.push(r[u]);else if("current"==l||"applied"==l){if("none"==i)a=o.slice();else if("applied"==i)a=r.slice();else if("removed"==i){for(var s={},u=0,c=r.length;u<c;u++)s[r[u]]=null;o.forEach(function(t){Object.prototype.hasOwnProperty.call(s,t)||a.push(t)})}}else if("index"==l||"original"==l)for(u=0,c=t.aoData.length;u<c;u++)t.aoData[u]&&("none"==i||-1===(n=r.indexOf(u))&&"removed"==i||0<=n&&"applied"==i)&&a.push(u);else if("number"==typeof l){var d=zt(t,l,"asc");if("none"===i)a=d;else for(u=0;u<d.length;u++)(-1===(n=r.indexOf(d[u]))&&"removed"==i||0<=n&&"applied"==i)&&a.push(d[u])}return a},pe=(e("rows()",function(n,a){void 0===n?n="":V.isPlainObject(n)&&(a=n,n=""),a=ie(a);var t=this.iterator("table",function(t){return e=oe("row",e=n,function(n){var t=g(n),a=r.aoData;if(null!==t&&!o)return[t];if(i=i||he(r,o),null!==t&&-1!==i.indexOf(t))return[t];if(null==n||""===n)return i;if("function"==typeof n)return i.map(function(t){var e=a[t];return n(t,e._aData,e.nTr)?t:null});if(n.nodeName)return t=n._DT_RowIndex,e=n._DT_CellIndex,void 0!==t?a[t]&&a[t].nTr===n?[t]:[]:e?a[e.row]&&a[e.row].nTr===n.parentNode?[e.row]:[]:(t=V(n).closest("*[data-dt-row]")).length?[t.data("dt-row")]:[];if("string"==typeof n&&"#"===n.charAt(0)){var e=r.aIds[n.replace(/^#/,"")];if(void 0!==e)return[e.idx]}t=b(v(r.aoData,i,"nTr"));return V(t).filter(n).map(function(){return this._DT_RowIndex}).toArray()},r=t,o=a),"current"!==o.order&&"applied"!==o.order||qt(r,e),e;var r,e,o,i},1);return t.selector.rows=n,t.selector.opts=a,t}),e("rows().nodes()",function(){return this.iterator("row",function(t,e){return t.aoData[e].nTr||void 0},1)}),e("rows().data()",function(){return this.iterator(!0,"rows",function(t,e){return v(t.aoData,e,"_aData")},1)}),t("rows().cache()","row().cache()",function(n){return this.iterator("row",function(t,e){t=t.aoData[e];return"search"===n?t._aFilterData:t._aSortData},1)}),t("rows().invalidate()","row().invalidate()",function(n){return this.iterator("row",function(t,e){gt(t,e,n)})}),t("rows().indexes()","row().index()",function(){return this.iterator("row",function(t,e){return e},1)}),t("rows().ids()","row().id()",function(t){for(var e=[],n=this.context,a=0,r=n.length;a<r;a++)for(var o=0,i=this[a].length;o<i;o++){var l=n[a].rowIdFn(n[a].aoData[this[a][o]]._aData);e.push((!0===t?"#":"")+l)}return new U(n,e)}),t("rows().remove()","row().remove()",function(){return this.iterator("row",function(t,e){var n=t.aoData,a=n[e],r=t.aiDisplayMaster.indexOf(e),r=(-1!==r&&t.aiDisplayMaster.splice(r,1),0<t._iRecordsDisplay&&t._iRecordsDisplay--,Kt(t),t.rowIdFn(a._aData));void 0!==r&&delete t.aIds[r],n[e]=null}),this}),e("rows.add()",function(o){var t=this.iterator("table",function(t){for(var e,n=[],a=0,r=o.length;a<r;a++)(e=o[a]).nodeName&&"TR"===e.nodeName.toUpperCase()?n.push(ut(t,e)[0]):n.push(Y(t,e));return n},1),e=this.rows(-1);return e.pop(),e.push.apply(e,t),e}),e("row()",function(t,e){return le(this.rows(t,e))}),e("row().data()",function(t){var e,n=this.context;return void 0===t?n.length&&this.length&&this[0].length?n[0].aoData[this[0]]._aData:void 0:((e=n[0].aoData[this[0]])._aData=t,Array.isArray(t)&&e.nTr&&e.nTr.id&&m(n[0].rowId)(t,e.nTr.id),gt(n[0],this[0],"data"),this)}),e("row().node()",function(){var t=this.context;if(t.length&&this.length&&this[0].length){t=t[0].aoData[this[0]];if(t&&t.nTr)return t.nTr}return null}),e("row.add()",function(e){e instanceof V&&e.length&&(e=e[0]);var t=this.iterator("table",function(t){return e.nodeName&&"TR"===e.nodeName.toUpperCase()?ut(t,e)[0]:Y(t,e)});return this.row(t[0])}),V(_).on("plugin-init.dt",function(t,e){var a=new U(e);a.on("stateSaveParams.DT",function(t,e,n){for(var a=e.rowIdFn,r=e.aiDisplayMaster,o=[],i=0;i<r.length;i++){var l=r[i],l=e.aoData[l];l._detailsShow&&o.push("#"+a(l._aData))}n.childRows=o}),a.on("stateLoaded.DT",function(t,e,n){pe(a,n)}),pe(a,a.state.loaded())}),function(t,e){e&&e.childRows&&t.rows(e.childRows.map(function(t){return t.replace(/([^:\\]*(?:\\.[^:\\]*)*):/g,"$1\\:")})).every(function(){tt(t.settings()[0],null,"requestChild",[this])})}),ge=$.util.throttle(function(t){Gt(t[0])},500),me=function(t,e){var n=t.context;n.length&&(e=n[0].aoData[void 0!==e?e:t[0]])&&e._details&&(e._details.remove(),e._detailsShow=void 0,e._details=void 0,V(e.nTr).removeClass("dt-hasChild"),ge(n))},ve="row().child",be=ve+"()",ye=(e(be,function(t,e){var n=this.context;return void 0===t?n.length&&this.length&&n[0].aoData[this[0]]?n[0].aoData[this[0]]._details:void 0:(!0===t?this.child.show():!1===t?me(this):n.length&&this.length&&se(n[0],n[0].aoData[this[0]],t,e),this)}),e([ve+".show()",be+".show()"],function(){return ue(this,!0),this}),e([ve+".hide()",be+".hide()"],function(){return ue(this,!1),this}),e([ve+".remove()",be+".remove()"],function(){return me(this),this}),e(ve+".isShown()",function(){var t=this.context;return t.length&&this.length&&t[0].aoData[this[0]]&&t[0].aoData[this[0]]._detailsShow||!1}),/^([^:]+)?:(name|title|visIdx|visible)$/),be=(e("columns()",function(n,a){void 0===n?n="":V.isPlainObject(n)&&(a=n,n=""),a=ie(a);var t=this.iterator("table",function(t){return e=n,l=a,s=(i=t).aoColumns,u=f(s,"sName"),c=f(s,"sTitle"),t=$.util.get("[].[].cell")(i.aoHeader),d=x(E([],t)),oe("column",e,function(n){var a,t=g(n);if(""===n)return h(s.length);if(null!==t)return[0<=t?t:s.length+t];if("function"==typeof n)return a=he(i,l),s.map(function(t,e){return n(e,ce(i,e,0,0,a),de(i,e))?e:null});var e,r,o="string"==typeof n?n.match(ye):"";if(o)switch(o[2]){case"visIdx":case"visible":return o[1]?(e=parseInt(o[1],10))<0?[(r=s.map(function(t,e){return t.bVisible?e:null}))[r.length+e]]:[H(i,e)]:s.map(function(t,e){return t.bVisible?e:null});case"name":return u.map(function(t,e){return t===o[1]?e:null});case"title":return c.map(function(t,e){return t===o[1]?e:null});default:return[]}return n.nodeName&&n._DT_CellIndex?[n._DT_CellIndex.column]:(t=V(d).filter(n).map(function(){return st(this)}).toArray()).length||!n.nodeName?t:(t=V(n).closest("*[data-dt-column]")).length?[t.data("dt-column")]:[]},i,l);var i,e,l,s,u,c,d},1);return t.selector.cols=n,t.selector.opts=a,t}),t("columns().header()","column().header()",function(n){return this.iterator("column",function(t,e){return de(t,e,n)},1)}),t("columns().footer()","column().footer()",function(n){return this.iterator("column",function(t,e){return t.aoFooter.length?t.aoFooter[void 0!==n?n:0][e].cell:null},1)}),t("columns().data()","column().data()",function(){return this.iterator("column-rows",ce,1)}),t("columns().render()","column().render()",function(o){return this.iterator("column-rows",function(t,e,n,a,r){return ce(t,e,0,0,r,o)},1)}),t("columns().dataSrc()","column().dataSrc()",function(){return this.iterator("column",function(t,e){return t.aoColumns[e].mData},1)}),t("columns().cache()","column().cache()",function(o){return this.iterator("column-rows",function(t,e,n,a,r){return v(t.aoData,r,"search"===o?"_aFilterData":"_aSortData",e)},1)}),t("columns().init()","column().init()",function(){return this.iterator("column",function(t,e){return t.aoColumns[e]},1)}),t("columns().nodes()","column().nodes()",function(){return this.iterator("column-rows",function(t,e,n,a,r){return v(t.aoData,r,"anCells",e)},1)}),t("columns().titles()","column().title()",function(n,a){return this.iterator("column",function(t,e){"number"==typeof n&&(a=n,n=void 0);e=V("span.dt-column-title",this.column(e).header(a));return void 0!==n?(e.html(n),this):e.html()},1)}),t("columns().types()","column().type()",function(){return this.iterator("column",function(t,e){e=t.aoColumns[e].sType;return e||B(t),e},1)}),t("columns().visible()","column().visible()",function(n,a){var e=this,r=[],t=this.iterator("column",function(t,e){if(void 0===n)return t.aoColumns[e].bVisible;!function(t,e,n){var a,r,o=t.aoColumns,i=o[e],l=t.aoData;if(void 0===n)return i.bVisible;if(i.bVisible===n)return!1;if(n)for(var s=f(o,"bVisible").indexOf(!0,e+1),u=0,c=l.length;u<c;u++)l[u]&&(r=l[u].nTr,a=l[u].anCells,r)&&r.insertBefore(a[e],a[s]||null);else V(f(t.aoData,"anCells",e)).detach();return i.bVisible=n,Bt(t),!0}(t,e,n)||r.push(e)});return void 0!==n&&this.iterator("table",function(t){St(t,t.aoHeader),St(t,t.aoFooter),t.aiDisplay.length||V(t.nTBody).find("td[colspan]").attr("colspan",W(t)),Gt(t),e.iterator("column",function(t,e){r.includes(e)&&tt(t,null,"column-visibility",[t,e,n,a])}),r.length&&(void 0===a||a)&&e.columns.adjust()}),t}),t("columns().widths()","column().width()",function(){var t=this.columns(":visible").count(),t=V("<tr>").html("<td>"+Array(t).join("</td><td>")+"</td>"),n=(V(this.table().body()).append(t),t.children().map(function(){return V(this).outerWidth()}));return t.remove(),this.iterator("column",function(t,e){t=T(t,e);return null!==t?n[t]:0},1)}),t("columns().indexes()","column().index()",function(n){return this.iterator("column",function(t,e){return"visible"===n?T(t,e):e},1)}),e("columns.adjust()",function(){return this.iterator("table",function(t){M(t)},1)}),e("column.index()",function(t,e){var n;if(0!==this.context.length)return n=this.context[0],"fromVisible"===t||"toData"===t?H(n,e):"fromData"===t||"toVisible"===t?T(n,e):void 0}),e("column()",function(t,e){return le(this.columns(t,e))}),e("cells()",function(g,t,m){var a,r,o,i,l,s,e;return V.isPlainObject(g)&&(void 0===g.row?(m=g,g=null):(m=t,t=null)),V.isPlainObject(t)&&(m=t,t=null),null==t?this.iterator("table",function(t){return a=t,t=g,e=ie(m),d=a.aoData,f=he(a,e),n=b(v(d,f,"anCells")),h=V(E([],n)),p=a.aoColumns.length,oe("cell",t,function(t){var e,n="function"==typeof t;if(null==t||n){for(o=[],i=0,l=f.length;i<l;i++)for(r=f[i],s=0;s<p;s++)u={row:r,column:s},(!n||(c=d[r],t(u,G(a,r,s),c.anCells?c.anCells[s]:null)))&&o.push(u);return o}return V.isPlainObject(t)?void 0!==t.column&&void 0!==t.row&&-1!==f.indexOf(t.row)?[t]:[]:(e=h.filter(t).map(function(t,e){return{row:e._DT_CellIndex.row,column:e._DT_CellIndex.column}}).toArray()).length||!t.nodeName?e:(c=V(t).closest("*[data-dt-row]")).length?[{row:c.data("dt-row"),column:c.data("dt-column")}]:[]},a,e);var a,e,r,o,i,l,s,u,c,d,f,n,h,p}):(e=m?{page:m.page,order:m.order,search:m.search}:{},a=this.columns(t,e),r=this.rows(g,e),e=this.iterator("table",function(t,e){var n=[];for(o=0,i=r[e].length;o<i;o++)for(l=0,s=a[e].length;l<s;l++)n.push({row:r[e][o],column:a[e][l]});return n},1),e=m&&m.selected?this.cells(e,m):e,V.extend(e.selector,{cols:t,rows:g,opts:m}),e)}),t("cells().nodes()","cell().node()",function(){return this.iterator("cell",function(t,e,n){t=t.aoData[e];return t&&t.anCells?t.anCells[n]:void 0},1)}),e("cells().data()",function(){return this.iterator("cell",function(t,e,n){return G(t,e,n)},1)}),t("cells().cache()","cell().cache()",function(a){return a="search"===a?"_aFilterData":"_aSortData",this.iterator("cell",function(t,e,n){return t.aoData[e][a][n]},1)}),t("cells().render()","cell().render()",function(a){return this.iterator("cell",function(t,e,n){return G(t,e,n,a)},1)}),t("cells().indexes()","cell().index()",function(){return this.iterator("cell",function(t,e,n){return{row:e,column:n,columnVisible:T(t,n)}},1)}),t("cells().invalidate()","cell().invalidate()",function(a){return this.iterator("cell",function(t,e,n){gt(t,e,a,n)})}),e("cell()",function(t,e,n){return le(this.cells(t,e,n))}),e("cell().data()",function(t){var e,n,a,r,o,i=this.context,l=this[0];return void 0===t?i.length&&l.length?G(i[0],l[0].row,l[0].column):void 0:(e=i[0],n=l[0].row,a=l[0].column,r=e.aoColumns[a],o=e.aoData[n]._aData,r.fnSetData(o,t,{settings:e,row:n,col:a}),gt(i[0],l[0].row,"data",l[0].column),this)}),e("order()",function(e,t){var n=this.context,a=Array.prototype.slice.call(arguments);return void 0===e?0!==n.length?n[0].aaSorting:void 0:("number"==typeof e?e=[[e,t]]:1<a.length&&(e=a),this.iterator("table",function(t){t.aaSorting=Array.isArray(e)?e.slice():e}))}),e("order.listener()",function(e,n,a){return this.iterator("table",function(t){Vt(t,e,{},n,a)})}),e("order.fixed()",function(e){var t;return e?this.iterator("table",function(t){t.aaSortingFixed=V.extend(!0,{},e)}):(t=(t=this.context).length?t[0].aaSortingFixed:void 0,Array.isArray(t)?{pre:t}:t)}),e(["columns().order()","column().order()"],function(n){var a=this;return n?this.iterator("table",function(t,e){t.aaSorting=a[e].map(function(t){return[t,n]})}):this.iterator("column",function(t,e){for(var n=$t(t),a=0,r=n.length;a<r;a++)if(n[a].col===e)return n[a].dir;return null},1)}),t("columns().orderable()","column().orderable()",function(n){return this.iterator("column",function(t,e){t=t.aoColumns[e];return n?t.asSorting:t.bSortable},1)}),e("processing()",function(e){return this.iterator("table",function(t){w(t,e)})}),e("search()",function(e,n,a,r){var t=this.context;return void 0===e?0!==t.length?t[0].oPreviousSearch.search:void 0:this.iterator("table",function(t){t.oFeatures.bFilter&&Nt(t,"object"==typeof n?V.extend(t.oPreviousSearch,n,{search:e}):V.extend(t.oPreviousSearch,{search:e,regex:null!==n&&n,smart:null===a||a,caseInsensitive:null===r||r}))})}),e("search.fixed()",function(e,n){var t=this.iterator(!0,"table",function(t){t=t.searchFixed;return e?void 0===n?t[e]:(null===n?delete t[e]:t[e]=n,this):Object.keys(t)});return void 0!==e&&void 0===n?t[0]:t}),t("columns().search()","column().search()",function(a,r,o,i){return this.iterator("column",function(t,e){var n=t.aoPreSearchCols;if(void 0===a)return n[e].search;t.oFeatures.bFilter&&("object"==typeof r?V.extend(n[e],r,{search:a}):V.extend(n[e],{search:a,regex:null!==r&&r,smart:null===o||o,caseInsensitive:null===i||i}),Nt(t,t.oPreviousSearch))})}),e(["columns().search.fixed()","column().search.fixed()"],function(n,a){var t=this.iterator(!0,"column",function(t,e){t=t.aoColumns[e].searchFixed;return n?void 0===a?t[n]:(null===a?delete t[n]:t[n]=a,this):Object.keys(t)});return void 0!==n&&void 0===a?t[0]:t}),e("state()",function(t,e){var n;return t?(n=V.extend(!0,{},t),this.iterator("table",function(t){!1!==e&&(n.time=+new Date+100),Jt(t,n,function(){})})):this.context.length?this.context[0].oSavedState:null}),e("state.clear()",function(){return this.iterator("table",function(t){t.fnStateSaveCallback.call(t.oInstance,t,{})})}),e("state.loaded()",function(){return this.context.length?this.context[0].oLoadedState:null}),e("state.save()",function(){return this.iterator("table",function(t){Gt(t)})}),$.use=function(t,e){"lib"===e||t.fn?V=t:"win"==e||t.document?_=(q=t).document:"datetime"!==e&&"DateTime"!==t.type||($.DateTime=t)},$.factory=function(t,e){var n=!1;return t&&t.document&&(_=(q=t).document),e&&e.fn&&e.fn.jquery&&(V=e,n=!0),n},$.versionCheck=function(t,e){for(var n,a,r=(e||$.version).split("."),o=t.split("."),i=0,l=o.length;i<l;i++)if((n=parseInt(r[i],10)||0)!==(a=parseInt(o[i],10)||0))return a<n;return!0},$.isDataTable=function(t){var r=V(t).get(0),o=!1;return t instanceof $.Api||(V.each($.settings,function(t,e){var n=e.nScrollHead?V("table",e.nScrollHead)[0]:null,a=e.nScrollFoot?V("table",e.nScrollFoot)[0]:null;e.nTable!==r&&n!==r&&a!==r||(o=!0)}),o)},$.tables=function(e){var t=!1,n=(V.isPlainObject(e)&&(t=e.api,e=e.visible),$.settings.filter(function(t){return!(e&&!V(t.nTable).is(":visible"))}).map(function(t){return t.nTable}));return t?new U(n):n},$.camelToHungarian=z,e("$()",function(t,e){e=this.rows(e).nodes(),e=V(e);return V([].concat(e.filter(t).toArray(),e.find(t).toArray()))}),V.each(["on","one","off"],function(t,n){e(n+"()",function(){var t=Array.prototype.slice.call(arguments),e=(t[0]=t[0].split(/\s/).map(function(t){return t.match(/\.dt\b/)?t:t+".dt"}).join(" "),V(this.tables().nodes()));return e[n].apply(e,t),this})}),e("clear()",function(){return this.iterator("table",function(t){pt(t)})}),e("error()",function(e){return this.iterator("table",function(t){Z(t,0,e)})}),e("settings()",function(){return new U(this.context,this.context)}),e("init()",function(){var t=this.context;return t.length?t[0].oInit:null}),e("data()",function(){return this.iterator("table",function(t){return f(t.aoData,"_aData")}).flatten()}),e("trigger()",function(e,n,a){return this.iterator("table",function(t){return tt(t,null,e,n,a)}).flatten()}),e("ready()",function(t){var e=this.context;return t?this.tables().every(function(){this.context[0]._bInitComplete?t.call(this):this.on("init",function(){t.call(this)})}):e.length?e[0]._bInitComplete||!1:null}),e("destroy()",function(c){return c=c||!1,this.iterator("table",function(t){var e=t.oClasses,n=t.nTable,a=t.nTBody,r=t.nTHead,o=t.nTFoot,i=V(n),a=V(a),l=V(t.nTableWrapper),s=t.aoData.map(function(t){return t?t.nTr:null}),u=e.order,o=(t.bDestroying=!0,tt(t,"aoDestroyCallback","destroy",[t],!0),c||new U(t).columns().visible(!0),l.off(".DT").find(":not(tbody *)").off(".DT"),V(q).off(".DT-"+t.sInstance),n!=r.parentNode&&(i.children("thead").detach(),i.append(r)),o&&n!=o.parentNode&&(i.children("tfoot").detach(),i.append(o)),t.colgroup.remove(),t.aaSorting=[],t.aaSortingFixed=[],Yt(t),V("th, td",r).removeClass(u.canAsc+" "+u.canDesc+" "+u.isAsc+" "+u.isDesc).css("width",""),a.children().detach(),a.append(s),t.nTableWrapper.parentNode),r=t.nTableWrapper.nextSibling,u=c?"remove":"detach",a=(i[u](),l[u](),!c&&o&&(o.insertBefore(n,r),i.css("width",t.sDestroyWidth).removeClass(e.table)),$.settings.indexOf(t));-1!==a&&$.settings.splice(a,1)})}),V.each(["column","row","cell"],function(t,s){e(s+"s().every()",function(a){var r,o=this.selector.opts,i=this,l=0;return this.iterator("every",function(t,e,n){r=i[s](e,o),"cell"===s?a.call(r,r[0][0].row,r[0][0].column,n,l):a.call(r,e,n,l),l++})})}),e("i18n()",function(t,e,n){var a=this.context[0],t=J(t)(a.oLanguage);return"string"==typeof(t=V.isPlainObject(t=void 0===t?e:t)?void 0!==n&&void 0!==t[n]?t[n]:t._:t)?t.replace("%d",n):t}),$.version="2.0.8",$.settings=[],$.models={},$.models.oSearch={caseInsensitive:!0,search:"",regex:!1,smart:!0,return:!1},$.models.oRow={nTr:null,anCells:null,_aData:[],_aSortData:null,_aFilterData:null,_sFilterRow:null,src:null,idx:-1,displayData:null},$.models.oColumn={idx:null,aDataSort:null,asSorting:null,bSearchable:null,bSortable:null,bVisible:null,_sManualType:null,_bAttrSrc:!1,fnCreatedCell:null,fnGetData:null,fnSetData:null,mData:null,mRender:null,sClass:null,sContentPadding:null,sDefaultContent:null,sName:null,sSortDataType:"std",sSortingClass:null,sTitle:null,sType:null,sWidth:null,sWidthOrig:null,maxLenString:null,searchFixed:null},$.defaults={aaData:null,aaSorting:[[0,"asc"]],aaSortingFixed:[],ajax:null,aLengthMenu:[10,25,50,100],aoColumns:null,aoColumnDefs:null,aoSearchCols:[],bAutoWidth:!0,bDeferRender:!0,bDestroy:!1,bFilter:!0,bInfo:!0,bLengthChange:!0,bPaginate:!0,bProcessing:!1,bRetrieve:!1,bScrollCollapse:!1,bServerSide:!1,bSort:!0,bSortMulti:!0,bSortCellsTop:null,bSortClasses:!0,bStateSave:!1,fnCreatedRow:null,fnDrawCallback:null,fnFooterCallback:null,fnFormatNumber:function(t){return t.toString().replace(/\B(?=(\d{3})+(?!\d))/g,this.oLanguage.sThousands)},fnHeaderCallback:null,fnInfoCallback:null,fnInitComplete:null,fnPreDrawCallback:null,fnRowCallback:null,fnStateLoadCallback:function(t){try{return JSON.parse((-1===t.iStateDuration?sessionStorage:localStorage).getItem("DataTables_"+t.sInstance+"_"+location.pathname))}catch(t){return{}}},fnStateLoadParams:null,fnStateLoaded:null,fnStateSaveCallback:function(t,e){try{(-1===t.iStateDuration?sessionStorage:localStorage).setItem("DataTables_"+t.sInstance+"_"+location.pathname,JSON.stringify(e))}catch(t){}},fnStateSaveParams:null,iStateDuration:7200,iDisplayLength:10,iDisplayStart:0,iTabIndex:0,oClasses:{},oLanguage:{oAria:{orderable:": Activate to sort",orderableReverse:": Activate to invert sorting",orderableRemove:": Activate to remove sorting",paginate:{first:"First",last:"Last",next:"Next",previous:"Previous"}},oPaginate:{sFirst:"«",sLast:"»",sNext:"›",sPrevious:"‹"},entries:{_:"entries",1:"entry"},sEmptyTable:"No data available in table",sInfo:"Showing _START_ to _END_ of _TOTAL_ _ENTRIES-TOTAL_",sInfoEmpty:"Showing 0 to 0 of 0 _ENTRIES-TOTAL_",sInfoFiltered:"(filtered from _MAX_ total _ENTRIES-MAX_)",sInfoPostFix:"",sDecimal:"",sThousands:",",sLengthMenu:"_MENU_ _ENTRIES_ per page",sLoadingRecords:"Loading...",sProcessing:"",sSearch:"Search:",sSearchPlaceholder:"",sUrl:"",sZeroRecords:"No matching records found"},oSearch:V.extend({},$.models.oSearch),layout:{topStart:"pageLength",topEnd:"search",bottomStart:"info",bottomEnd:"paging"},sDom:null,searchDelay:null,sPaginationType:"full_numbers",sScrollX:"",sScrollXInner:"",sScrollY:"",sServerMethod:"GET",renderer:null,rowId:"DT_RowId",caption:null},k($.defaults),$.defaults.column={aDataSort:null,iDataSort:-1,ariaTitle:"",asSorting:["asc","desc",""],bSearchable:!0,bSortable:!0,bVisible:!0,fnCreatedCell:null,mData:null,mRender:null,sCellType:"td",sClass:"",sContentPadding:"",sDefaultContent:null,sName:"",sSortDataType:"std",sTitle:null,sType:null,sWidth:null},k($.defaults.column),$.models.oSettings={oFeatures:{bAutoWidth:null,bDeferRender:null,bFilter:null,bInfo:!0,bLengthChange:!0,bPaginate:null,bProcessing:null,bServerSide:null,bSort:null,bSortMulti:null,bSortClasses:null,bStateSave:null},oScroll:{bCollapse:null,iBarWidth:0,sX:null,sXInner:null,sY:null},oLanguage:{fnInfoCallback:null},oBrowser:{bScrollbarLeft:!1,barWidth:0},ajax:null,aanFeatures:[],aoData:[],aiDisplay:[],aiDisplayMaster:[],aIds:{},aoColumns:[],aoHeader:[],aoFooter:[],oPreviousSearch:{},searchFixed:{},aoPreSearchCols:[],aaSorting:null,aaSortingFixed:[],sDestroyWidth:0,aoRowCallback:[],aoHeaderCallback:[],aoFooterCallback:[],aoDrawCallback:[],aoRowCreatedCallback:[],aoPreDrawCallback:[],aoInitComplete:[],aoStateSaveParams:[],aoStateLoadParams:[],aoStateLoaded:[],sTableId:"",nTable:null,nTHead:null,nTFoot:null,nTBody:null,nTableWrapper:null,bInitialised:!1,aoOpenRows:[],sDom:null,searchDelay:null,sPaginationType:"two_button",pagingControls:0,iStateDuration:0,aoStateSave:[],aoStateLoad:[],oSavedState:null,oLoadedState:null,bAjaxDataGet:!0,jqXHR:null,json:void 0,oAjaxData:void 0,sServerMethod:null,fnFormatNumber:null,aLengthMenu:null,iDraw:0,bDrawing:!1,iDrawError:-1,_iDisplayLength:10,_iDisplayStart:0,_iRecordsTotal:0,_iRecordsDisplay:0,oClasses:{},bFiltered:!1,bSorted:!1,bSortCellsTop:null,oInit:null,aoDestroyCallback:[],fnRecordsTotal:function(){return"ssp"==et(this)?+this._iRecordsTotal:this.aiDisplayMaster.length},fnRecordsDisplay:function(){return"ssp"==et(this)?+this._iRecordsDisplay:this.aiDisplay.length},fnDisplayEnd:function(){var t=this._iDisplayLength,e=this._iDisplayStart,n=e+t,a=this.aiDisplay.length,r=this.oFeatures,o=r.bPaginate;return r.bServerSide?!1===o||-1===t?e+a:Math.min(e+t,this._iRecordsDisplay):!o||a<n||-1===t?a:n},oInstance:null,sInstance:null,iTabIndex:0,nScrollHead:null,nScrollFoot:null,aLastSort:[],oPlugins:{},rowIdFn:null,rowId:null,caption:"",captionNode:null,colgroup:null},$.ext.pager);V.extend(be,{simple:function(){return["previous","next"]},full:function(){return["first","previous","next","last"]},numbers:function(){return["numbers"]},simple_numbers:function(){return["previous","numbers","next"]},full_numbers:function(){return["first","previous","numbers","next","last"]},first_last:function(){return["first","last"]},first_last_numbers:function(){return["first","numbers","last"]},_numbers:Ne,numbers_length:7}),V.extend(!0,$.ext.renderer,{pagingButton:{_:function(t,e,n,a,r){var t=t.oClasses.paging,o=[t.button];return a&&o.push(t.active),r&&o.push(t.disabled),{display:a="ellipsis"===e?V('<span class="ellipsis"></span>').html(n)[0]:V("<button>",{class:o.join(" "),role:"link",type:"button"}).html(n),clicker:a}}},pagingContainer:{_:function(t,e){return e}}});function De(t){return t.replace(/[\W]/g,"_")}function xe(t,e,n,a,r){return q.moment?t[e](r):q.luxon?t[n](r):a?t[a](r):t}var Se=!1;function Te(t,e,n){var a;if(q.moment){if(!(a=q.moment.utc(t,e,n,!0)).isValid())return null}else if(q.luxon){if(!(a=e&&"string"==typeof t?q.luxon.DateTime.fromFormat(t,e):q.luxon.DateTime.fromISO(t)).isValid)return null;a.setLocale(n)}else e?(Se||alert("DataTables warning: Formatted date without Moment.js or Luxon - https://datatables.net/tn/17"),Se=!0):a=new Date(t);return a}function we(s){return function(a,r,o,i){0===arguments.length?(o="en",a=r=null):1===arguments.length?(o="en",r=a,a=null):2===arguments.length&&(o=r,r=a,a=null);var l="datetime"+(r?"-"+De(r):"");return $.ext.type.order[l]||$.type(l,{detect:function(t){return t===l&&l},order:{pre:function(t){return t.valueOf()}},className:"dt-right"}),function(t,e){var n;return null==t&&(t="--now"===i?(n=new Date,new Date(Date.UTC(n.getFullYear(),n.getMonth(),n.getDate(),n.getHours(),n.getMinutes(),n.getSeconds()))):""),"type"===e?l:""===t?"sort"!==e?"":Te("0000-01-01 00:00:00",null,o):!(null===r||a!==r||"sort"===e||"type"===e||t instanceof Date)||null===(n=Te(t,a,o))?t:"sort"===e?n:(t=null===r?xe(n,"toDate","toJSDate","")[s]():xe(n,"format","toFormat","toISOString",r),"display"===e?u(t):t)}}}var _e=",",Ce=".";if(void 0!==q.Intl)try{for(var Ie=(new Intl.NumberFormat).formatToParts(100000.1),a=0;a<Ie.length;a++)"group"===Ie[a].type?_e=Ie[a].value:"decimal"===Ie[a].type&&(Ce=Ie[a].value)}catch(t){}$.datetime=function(n,a){var r="datetime-detect-"+De(n);a=a||"en",$.ext.type.order[r]||$.type(r,{detect:function(t){var e=Te(t,n,a);return!(""!==t&&!e)&&r},order:{pre:function(t){return Te(t,n,a)||0}},className:"dt-right"})},$.render={date:we("toLocaleDateString"),datetime:we("toLocaleString"),time:we("toLocaleTimeString"),number:function(r,o,i,l,s){return null==r&&(r=_e),null==o&&(o=Ce),{display:function(t){if("number"!=typeof t&&"string"!=typeof t)return t;if(""===t||null===t)return t;var e=t<0?"-":"",n=parseFloat(t),a=Math.abs(n);if(1e11<=a||a<1e-4&&0!==a)return(a=n.toExponential(i).split(/e\+?/))[0]+" x 10<sup>"+a[1]+"</sup>";if(isNaN(n))return u(t);n=n.toFixed(i),t=Math.abs(n);a=parseInt(t,10),n=i?o+(t-a).toFixed(i).substring(2):"";return(e=0===a&&0===parseFloat(n)?"":e)+(l||"")+a.toString().replace(/\B(?=(\d{3})+(?!\d))/g,r)+n+(s||"")}}},text:function(){return{display:u,filter:u}}};var i=$.ext.type,Ae=($.type=function(a,t,e){if(!t)return{className:i.className[a],detect:i.detect.find(function(t){return t.name===a}),order:{pre:i.order[a+"-pre"],asc:i.order[a+"-asc"],desc:i.order[a+"-desc"]},render:i.render[a],search:i.search[a]};function n(t,e){i[t][a]=e}function r(n){function t(t,e){return!0===(t=n(t,e))?a:t}Object.defineProperty(t,"name",{value:a});var e=i.detect.findIndex(function(t){return t.name===a});-1===e?i.detect.unshift(t):i.detect.splice(e,1,t)}function o(t){i.order[a+"-pre"]=t.pre,i.order[a+"-asc"]=t.asc,i.order[a+"-desc"]=t.desc}void 0===e&&(e=t,t=null),"className"===t?n("className",e):"detect"===t?r(e):"order"===t?o(e):"render"===t?n("render",e):"search"===t?n("search",e):t||(e.className&&n("className",e.className),void 0!==e.detect&&r(e.detect),e.order&&o(e.order),void 0!==e.render&&n("render",e.render),void 0!==e.search&&n("search",e.search))},$.types=function(){return i.detect.map(function(t){return t.name})},$.type("string",{detect:function(){return"string"},order:{pre:function(t){return y(t)?"":"string"==typeof t?t.toLowerCase():t.toString?t.toString():""}},search:fe(!1,!0)}),$.type("html",{detect:function(t){return y(t)||"string"==typeof t&&-1!==t.indexOf("<")?"html":null},order:{pre:function(t){return y(t)?"":t.replace?I(t).trim().toLowerCase():t+""}},search:fe(!0,!0)}),$.type("date",{className:"dt-type-date",detect:function(t){var e;return(!t||t instanceof Date||N.test(t))&&(null!==(e=Date.parse(t))&&!isNaN(e)||y(t))?"date":null},order:{pre:function(t){t=Date.parse(t);return isNaN(t)?-1/0:t}}}),$.type("html-num-fmt",{className:"dt-type-numeric",detect:function(t,e){e=e.oLanguage.sDecimal;return l(t,e,!0)?"html-num-fmt":null},order:{pre:function(t,e){e=e.oLanguage.sDecimal;return Ae(t,e,L,P)}},search:fe(!0,!0)}),$.type("html-num",{className:"dt-type-numeric",detect:function(t,e){e=e.oLanguage.sDecimal;return l(t,e)?"html-num":null},order:{pre:function(t,e){e=e.oLanguage.sDecimal;return Ae(t,e,L)}},search:fe(!0,!0)}),$.type("num-fmt",{className:"dt-type-numeric",detect:function(t,e){e=e.oLanguage.sDecimal;return o(t,e,!0)?"num-fmt":null},order:{pre:function(t,e){e=e.oLanguage.sDecimal;return Ae(t,e,P)}}}),$.type("num",{className:"dt-type-numeric",detect:function(t,e){e=e.oLanguage.sDecimal;return o(t,e)?"num":null},order:{pre:function(t,e){e=e.oLanguage.sDecimal;return Ae(t,e)}}}),function(t,e,n,a){var r;return 0===t||t&&"-"!==t?"number"==(r=typeof t)||"bigint"==r?t:+(t=(t=e?R(t,e):t).replace&&(n&&(t=t.replace(n,"")),a)?t.replace(a,""):t):-1/0});V.extend(!0,$.ext.renderer,{footer:{_:function(t,e,n){e.addClass(n.tfoot.cell)}},header:{_:function(d,f,h){f.addClass(h.thead.cell),d.oFeatures.bSort||f.addClass(h.order.none);var t=d.bSortCellsTop,e=f.closest("thead").find("tr"),n=f.parent().index();"disable"===f.attr("data-dt-order")||"disable"===f.parent().attr("data-dt-order")||!0===t&&0!==n||!1===t&&n!==e.length-1||V(d.nTable).on("order.dt.DT",function(t,e,n){var a,r,o,i,l,s,u,c;d===e&&(a=h.order,c=e.api.columns(f),r=d.aoColumns[c.flatten()[0]],o=c.orderable().includes(!0),i="",u=c.indexes(),l=c.orderable(!0).flatten(),s=","+n.map(function(t){return t.col}).join(",")+",",f.removeClass(a.isAsc+" "+a.isDesc).toggleClass(a.none,!o).toggleClass(a.canAsc,o&&l.includes("asc")).toggleClass(a.canDesc,o&&l.includes("desc")),-1!==(l=s.indexOf(","+u.toArray().join(",")+","))&&(s=c.order(),f.addClass(s.includes("asc")?a.isAsc:""+s.includes("desc")?a.isDesc:"")),0===l?(u=n[0],c=r.asSorting,f.attr("aria-sort","asc"===u.dir?"ascending":"descending"),i=c[u.index+1]?"Reverse":"Remove"):f.removeAttr("aria-sort"),f.attr("aria-label",o?r.ariaTitle+e.api.i18n("oAria.orderable"+i):r.ariaTitle),o)&&(f.find(".dt-column-title").attr("role","button"),f.attr("tabindex",0))})}},layout:{_:function(t,e,n){var a=V("<div/>").addClass("dt-layout-row").appendTo(e);V.each(n,function(t,e){t=e.table?"":"dt-"+t+" ";e.table&&a.addClass("dt-layout-table"),V("<div/>").attr({id:e.id||null,class:"dt-layout-cell "+t+(e.className||"")}).append(e.contents).appendTo(a)})}}}),$.feature={},$.feature.register=function(t,e,n){$.ext.features[t]=e,n&&C.feature.push({cFeature:n,fnInit:e})},$.feature.register("info",function(t,s){var e,n,u;return t.oFeatures.bInfo?(e=t.oLanguage,n=t.sTableId,u=V("<div/>",{class:t.oClasses.info.container}),s=V.extend({callback:e.fnInfoCallback,empty:e.sInfoEmpty,postfix:e.sInfoPostFix,search:e.sInfoFiltered,text:e.sInfo},s),t.aoDrawCallback.push(function(t){var e=s,n=u,a=t._iDisplayStart+1,r=t.fnDisplayEnd(),o=t.fnRecordsTotal(),i=t.fnRecordsDisplay(),l=i?e.text:e.empty;i!==o&&(l+=" "+e.search),l+=e.postfix,l=ee(t,l),e.callback&&(l=e.callback.call(t.oInstance,t,a,r,o,i,l)),n.html(l),tt(t,null,"info",[t,n[0],l])}),t._infoEl||(u.attr({"aria-live":"polite",id:n+"_info",role:"status"}),V(t.nTable).attr("aria-describedby",n+"_info"),t._infoEl=u),u):null},"i");var Le=0;function Fe(t,e,n,a){var r=t.oLanguage.oPaginate,o={display:"",active:!1,disabled:!1};switch(e){case"ellipsis":o.display="&#x2026;",o.disabled=!0;break;case"first":o.display=r.sFirst,0===n&&(o.disabled=!0);break;case"previous":o.display=r.sPrevious,0===n&&(o.disabled=!0);break;case"next":o.display=r.sNext,0!==a&&n!==a-1||(o.disabled=!0);break;case"last":o.display=r.sLast,0!==a&&n!==a-1||(o.disabled=!0);break;default:"number"==typeof e&&(o.display=t.fnFormatNumber(e+1),n===e)&&(o.active=!0)}return o}function Ne(t,e,n,a){var r=[],o=Math.floor(n/2),i=a?2:1,l=a?1:0;return e<=n?r=h(0,e):1===n?r=[t]:3===n?t<=1?r=[0,1,"ellipsis"]:e-2<=t?(r=h(e-2,e)).unshift("ellipsis"):r=["ellipsis",t,"ellipsis"]:t<=o?((r=h(0,n-i)).push("ellipsis"),a&&r.push(e-1)):e-1-o<=t?((r=h(e-(n-i),e)).unshift("ellipsis"),a&&r.unshift(0)):((r=h(t-o+i,t+o-l)).push("ellipsis"),r.unshift("ellipsis"),a&&(r.push(e-1),r.unshift(0))),r}$.feature.register("search",function(n,t){var e,a,r,o,i,l,s,u,c,d;return n.oFeatures.bFilter?(e=n.oClasses.search,a=n.sTableId,c=n.oLanguage,r=n.oPreviousSearch,o='<input type="search" class="'+e.input+'"/>',-1===(t=V.extend({placeholder:c.sSearchPlaceholder,text:c.sSearch},t)).text.indexOf("_INPUT_")&&(t.text+="_INPUT_"),t.text=ee(n,t.text),c=t.text.match(/_INPUT_$/),s=t.text.match(/^_INPUT_/),i=t.text.replace(/_INPUT_/,""),l="<label>"+t.text+"</label>",s?l="_INPUT_<label>"+i+"</label>":c&&(l="<label>"+i+"</label>_INPUT_"),(s=V("<div>").addClass(e.container).append(l.replace(/_INPUT_/,o))).find("label").attr("for","dt-search-"+Le),s.find("input").attr("id","dt-search-"+Le),Le++,u=function(t){var e=this.value;r.return&&"Enter"!==t.key||e!=r.search&&(r.search=e,Nt(n,r),n._iDisplayStart=0,S(n))},c=null!==n.searchDelay?n.searchDelay:0,d=V("input",s).val(r.search).attr("placeholder",t.placeholder).on("keyup.DT search.DT input.DT paste.DT cut.DT",c?$.util.debounce(u,c):u).on("mouseup.DT",function(t){setTimeout(function(){u.call(d[0],t)},10)}).on("keypress.DT",function(t){if(13==t.keyCode)return!1}).attr("aria-controls",a),V(n.nTable).on("search.dt.DT",function(t,e){n===e&&d[0]!==_.activeElement&&d.val("function"!=typeof r.search?r.search:"")}),s):null},"f"),$.feature.register("paging",function(t,e){if(!t.oFeatures.bPaginate)return null;(e=V.extend({buttons:$.ext.pager.numbers_length,type:t.sPaginationType,boundaryNumbers:!0},e)).numbers&&(e.buttons=e.numbers);function n(){!function t(e,n,a){if(!e._bInitComplete)return;var r=$.ext.pager[a.type],o=e.oLanguage.oAria.paginate||{},i=e._iDisplayStart,l=e._iDisplayLength,s=e.fnRecordsDisplay(),u=-1===l,c=u?0:Math.ceil(i/l),d=u?1:Math.ceil(s/l),f=r().map(function(t){return"numbers"===t?Ne(c,d,a.buttons,a.boundaryNumbers):t}).flat();var h=[];for(var p=0;p<f.length;p++){var g=f[p],m=Fe(e,g,c,d),v=te(e,"pagingButton")(e,g,m.display,m.active,m.disabled);V(v.clicker).attr({"aria-controls":e.sTableId,"aria-disabled":m.disabled?"true":null,"aria-current":m.active?"page":null,"aria-label":o[g],"data-dt-idx":g,tabIndex:m.disabled?-1:e.iTabIndex}),"number"!=typeof g&&V(v.clicker).addClass(g),Qt(v.clicker,{action:g},function(t){t.preventDefault(),Ht(e,t.data.action,!0)}),h.push(v.display)}i=te(e,"pagingContainer")(e,h);u=n.find(_.activeElement).data("dt-idx");n.empty().append(i);void 0!==u&&n.find("[data-dt-idx="+u+"]").trigger("focus");h.length&&1<a.numbers&&V(n).height()>=2*V(h[0]).outerHeight()-10&&t(e,n,V.extend({},a,{numbers:a.numbers-2}))}(t,a,e)}var a=V("<div/>").addClass(t.oClasses.paging.container+" paging_"+e.type);return t.aoDrawCallback.push(n),V(t.nTable).on("column-sizing.dt.DT",n),a},"p");var je=0;return $.feature.register("pageLength",function(a,t){var e=a.oFeatures;if(!e.bPaginate||!e.bLengthChange)return null;t=V.extend({menu:a.aLengthMenu,text:a.oLanguage.sLengthMenu},t);var e=a.oClasses.length,n=a.sTableId,r=t.menu,o=[],i=[];if(Array.isArray(r[0]))o=r[0],i=r[1];else for(p=0;p<r.length;p++)V.isPlainObject(r[p])?(o.push(r[p].value),i.push(r[p].label)):(o.push(r[p]),i.push(r[p]));for(var l=t.text.match(/_MENU_$/),s=t.text.match(/^_MENU_/),u=t.text.replace(/_MENU_/,""),t="<label>"+t.text+"</label>",c=(s?t="_MENU_<label>"+u+"</label>":l&&(t="<label>"+u+"</label>_MENU_"),V("<div/>").addClass(e.container).append(t.replace("_MENU_","<span></span>"))),d=[],f=(c.find("label")[0].childNodes.forEach(function(t){t.nodeType===Node.TEXT_NODE&&d.push({el:t,text:t.textContent})}),function(e){d.forEach(function(t){t.el.textContent=ee(a,t.text,e)})}),h=V("<select/>",{name:n+"_length","aria-controls":n,class:e.select}),p=0;p<o.length;p++)h[0][p]=new Option("number"==typeof i[p]?a.fnFormatNumber(i[p]):i[p],o[p]);return c.find("label").attr("for","dt-length-"+je),h.attr("id","dt-length-"+je),je++,c.find("span").replaceWith(h),V("select",c).val(a._iDisplayLength).on("change.DT",function(){Mt(a,V(this).val()),S(a)}),V(a.nTable).on("length.dt.DT",function(t,e,n){a===e&&(V("select",c).val(n),f(n))}),f(a._iDisplayLength),c},"l"),((V.fn.dataTable=$).$=V).fn.dataTableSettings=$.settings,V.fn.dataTableExt=$.ext,V.fn.DataTable=function(t){return V(this).dataTable(t).api()},V.each($,function(t,e){V.fn.DataTable[t]=e}),$});

/*! DataTables Bootstrap 5 integration
 * © SpryMedia Ltd - datatables.net/license
 */
!function(n){var o,a;"function"==typeof define&&define.amd?define(["jquery","datatables.net"],function(e){return n(e,window,document)}):"object"==typeof exports?(o=require("jquery"),a=function(e,t){t.fn.dataTable||require("datatables.net")(e,t)},"undefined"==typeof window?module.exports=function(e,t){return e=e||window,t=t||o(e),a(e,t),n(t,0,e.document)}:(a(window,o),module.exports=n(o,window,window.document))):n(jQuery,window,document)}(function(d,e,t){"use strict";var n=d.fn.dataTable;return d.extend(!0,n.defaults,{renderer:"bootstrap"}),d.extend(!0,n.ext.classes,{container:"dt-container dt-bootstrap5",search:{input:"form-control form-control-sm"},length:{select:"form-select form-select-sm"},processing:{container:"dt-processing card"}}),n.ext.renderer.pagingButton.bootstrap=function(e,t,n,o,a){var r=["dt-paging-button","page-item"],o=(o&&r.push("active"),a&&r.push("disabled"),d("<li>").addClass(r.join(" ")));return{display:o,clicker:d("<a>",{href:a?null:"#",class:"page-link"}).html(n).appendTo(o)}},n.ext.renderer.pagingContainer.bootstrap=function(e,t){return d("<ul/>").addClass("pagination").append(t)},n.ext.renderer.layout.bootstrap=function(e,t,n){var o=d("<div/>",{class:n.full?"row mt-2 justify-content-md-center":"row mt-2 justify-content-between"}).appendTo(t);d.each(n,function(e,t){e=t.table?"col-12":"start"===e?"col-md-auto me-auto":"end"===e?"col-md-auto ms-auto":"col-md";d("<div/>",{id:t.id||null,class:e+" "+(t.className||"")}).append(t.contents).appendTo(o)})},n});

/*! AutoFill 2.7.0
 * © SpryMedia Ltd - datatables.net/license
 */
!function(o){var i,n;"function"==typeof define&&define.amd?define(["jquery","datatables.net"],function(t){return o(t,window,document)}):"object"==typeof exports?(i=require("jquery"),n=function(t,e){e.fn.dataTable||require("datatables.net")(t,e)},"undefined"==typeof window?module.exports=function(t,e){return t=t||window,e=e||i(t),n(t,e),o(e,t,t.document)}:(n(window,i),module.exports=o(i,window,window.document))):o(jQuery,window,document)}(function(f,r,m){"use strict";function d(t,e){if(!p.versionCheck||!p.versionCheck("1.11"))throw"Warning: AutoFill requires DataTables 1.11 or greater";this.c=f.extend(!0,{},p.defaults.autoFill,d.defaults,e),this.s={dt:new p.Api(t),namespace:".autoFill"+o++,scroll:{},scrollInterval:null,handle:{height:0,width:0},enabled:!1},this.dom={closeButton:f('<div class="dtaf-popover-close">&times;</div>'),handle:f('<div class="dt-autofill-handle"/>'),select:{top:f('<div class="dt-autofill-select top"/>'),right:f('<div class="dt-autofill-select right"/>'),bottom:f('<div class="dt-autofill-select bottom"/>'),left:f('<div class="dt-autofill-select left"/>')},background:f('<div class="dt-autofill-background"/>'),list:f('<div class="dt-autofill-list">'+this.s.dt.i18n("autoFill.info","")+"</div>").attr("aria-modal",!0).attr("role","dialog").append('<div class="dt-autofill-list-items"></div>'),dtScroll:null,offsetParent:null},this._constructor()}var p=f.fn.dataTable,o=0,t=(f.extend(d.prototype,{enabled:function(){return this.s.enabled},enable:function(t){var e=this;if(!1===t)return this.disable();this.s.enabled=!0,this._focusListener(),this.dom.handle.on("mousedown touchstart",function(t){return e._mousedown(t),!1}),f(r).on("resize",function(){0<f("div.dt-autofill-handle").length&&void 0!==e.dom.attachedTo&&e._attach(e.dom.attachedTo)});function o(){e.s.handle={height:!1,width:!1},f(e.dom.handle).css({height:"",width:""}),void 0!==e.dom.attachedTo&&e._attach(e.dom.attachedTo)}return f(r).on("orientationchange",function(){setTimeout(function(){o(),setTimeout(o,150)},50)}),this},disable:function(){return this.s.enabled=!1,this._focusListenerRemove(),this},_constructor:function(){var t=this,e=this.s.dt,o=f("div.dataTables_scrollBody",this.s.dt.table().container());e.settings()[0].autoFill=this,o.length&&"static"===(this.dom.dtScroll=o).css("position")&&o.css("position","relative"),!1!==this.c.enable&&this.enable(),e.on("destroy.autoFill",function(){t._focusListenerRemove()})},_attach:function(t){var e=this.s.dt,o=e.cell(t).index(),i=this.dom.handle,n=this.s.handle;o&&-1!==e.columns(this.c.columns).indexes().indexOf(o.column)?(this.dom.offsetParent||(this.dom.offsetParent=f(e.table().node()).offsetParent()),n.height&&n.width||(i.appendTo("body"),n.height=i.outerHeight(),n.width=i.outerWidth()),o=this._getPosition(t,this.dom.offsetParent),this.dom.attachedTo=t,i.css({top:o.top+t.offsetHeight-n.height,left:o.left+t.offsetWidth-n.width}).appendTo(this.dom.offsetParent)):this._detach()},_actionSelector:function(o){var t,i,n=this,l=this.s.dt,s=d.actions,a=[];f.each(s,function(t,e){e.available(l,o)&&a.push(t)}),1===a.length&&!1===this.c.alwaysAsk?(t=s[a[0]].execute(l,o),this._update(t,o)):(1<a.length||this.c.alwaysAsk)&&(i=this.dom.list.children("div.dt-autofill-list-items").empty(),a.push("cancel"),f.each(a,function(t,e){i.append(f("<button/>").html(s[e].option(l,o)).append(f('<span class="dt-autofill-button"/>').html(l.i18n("autoFill.button","&gt;"))).on("click",function(t){"button"===t.target.nodeName.toLowerCase()&&(t=s[e].execute(l,o,f(this).closest("button")),n._update(t,o),n.dom.background.remove(),n.dom.list.remove())}))}),this.dom.background.appendTo("body"),this.dom.background.one("click",function(){n.dom.background.remove(),n.dom.list.remove()}),this.dom.list.appendTo("body"),this.c.closeButton&&(this.dom.list.prepend(this.dom.closeButton).addClass(d.classes.closeable),this.dom.closeButton.on("click",function(){return n.dom.background.click()})),this.dom.list.css("margin-top",this.dom.list.outerHeight()/2*-1))},_detach:function(){this.dom.attachedTo=null,this.dom.handle.detach()},_drawSelection:function(t,e){var o,i=this.s.dt,n=this.s.start,l=f(this.dom.start),t={row:this.c.vertical?i.rows({page:"current"}).nodes().indexOf(t.parentNode):n.row,column:this.c.horizontal?f(t).index():n.column},s=i.column.index("toData",t.column),a=i.row(":eq("+t.row+")",{page:"current"}),a=f(i.cell(a.index(),s).node());i.cell(a).any()&&-1!==i.columns(this.c.columns).indexes().indexOf(s)&&-1!==t.row&&(this.s.end=t,i=n.row<t.row?l:a,s=n.row<t.row?a:l,o=n.column<t.column?l:a,n=n.column<t.column?a:l,i=this._getPosition(i.get(0)).top,o=this._getPosition(o.get(0)).left,t=this._getPosition(s.get(0)).top+s.outerHeight()-i,a=this._getPosition(n.get(0)).left+n.outerWidth()-o,(l=this.dom.select).top.css({top:i,left:o,width:a}),l.left.css({top:i,left:o,height:t}),l.bottom.css({top:i+t,left:o,width:a}),l.right.css({top:i,left:o+a,height:t}))},_editor:function(t){var e=this.s.dt,o=this.c.editor;if(o){for(var i={},n=[],l=o.fields(),s=0,a=t.length;s<a;s++)for(var d=0,r=t[s].length;d<r;d++){var c=t[s][d],u=e.settings()[0].aoColumns[c.index.column],h=u.editField;if(void 0===h)for(var f=u.mData,m=0,p=l.length;m<p;m++){var v=o.field(l[m]);if(v.dataSrc()===f){h=v.name();break}}if(!h)throw"Could not automatically determine field data. Please see https://datatables.net/tn/11";i[h]||(i[h]={});u=e.row(c.index.row).id();i[h][u]=c.set,n.push(c.index)}o.bubble(n,!1).multiSet(i).submit(null,function(){o.close()})}},_emitEvent:function(o,i){this.s.dt.iterator("table",function(t,e){f(t.nTable).triggerHandler(o+".dt",i)})},_focusListener:function(){var i=this,e=this.s.dt,t=this.s.namespace,o=null!==this.c.focus?this.c.focus:e.init().keys||e.settings()[0].keytable?"focus":"hover";"focus"===o?e.on("key-focus.autoFill",function(t,e,o){i._attach(o.node())}).on("key-blur.autoFill",function(t,e,o){i._detach()}):"click"===o?(f(e.table().body()).on("click"+t,"td, th",function(t){i._attach(this)}),f(m.body).on("click"+t,function(t){f(t.target).parents().filter(e.table().body()).length||i._detach()})):f(e.table().body()).on("mouseenter"+t+" touchstart"+t,"td, th",function(t){i._attach(this)}).on("mouseleave"+t+"touchend"+t,function(t){f(t.relatedTarget).hasClass("dt-autofill-handle")||i._detach()})},_focusListenerRemove:function(){var t=this.s.dt;t.off(".autoFill"),f(t.table().body()).off(this.s.namespace),f(m.body).off(this.s.namespace)},_getPosition:function(t,e){var o=t,i=0,n=0;e=e||f(f(this.s.dt.table().node())[0].offsetParent);do{var l=o.offsetTop,s=o.offsetLeft,a=f(o.offsetParent)}while((i+=l+ +parseInt(a.css("border-top-width")||0),n+=s+ +parseInt(a.css("border-left-width")||0),"body"!==o.nodeName.toLowerCase())&&(o=a.get(0),a.get(0)!==e.get(0)));return{top:i,left:n}},_mousedown:function(t){var e=this,o=this.s.dt,i=(this.dom.start=this.dom.attachedTo,this.s.start={row:o.rows({page:"current"}).nodes().indexOf(f(this.dom.start).parent()[0]),column:f(this.dom.start).index()},f(m.body).on("mousemove.autoFill touchmove.autoFill",function(t){e._mousemove(t),"touchmove"===t.type&&f(m.body).one("touchend.autoFill",function(){e._detach()})}).on("mouseup.autoFill touchend.autoFill",function(t){e._mouseup(t)}),this.dom.select),o=f(o.table().node()).offsetParent(),i=(i.top.appendTo(o),i.left.appendTo(o),i.right.appendTo(o),i.bottom.appendTo(o),this._drawSelection(this.dom.start,t),this.dom.handle.css("display","none"),this.dom.dtScroll);this.s.scroll={windowHeight:f(r).height(),windowWidth:f(r).width(),dtTop:i?i.offset().top:null,dtLeft:i?i.offset().left:null,dtHeight:i?i.outerHeight():null,dtWidth:i?i.outerWidth():null}},_mousemove:function(t){var e=t.touches&&t.touches.length?m.elementFromPoint(t.touches[0].clientX,t.touches[0].clientY):t.target,o=e.nodeName.toLowerCase();"td"!==o&&"th"!==o||(this._drawSelection(e,t),this._shiftScroll(t))},_mouseup:function(t){f(m.body).off(".autoFill");var e=this,n=this.s.dt,o=this.dom.select,o=(o.top.remove(),o.left.remove(),o.right.remove(),o.bottom.remove(),this.dom.handle.css("display","block"),this.s.start),i=this.s.end;if(o.row!==i.row||o.column!==i.column){var l,s=n.cell(":eq("+o.row+")",o.column+":visible",{page:"current"});if(f("div.DTE",s.node()).length)(l=n.editor()).on("submitSuccess.dtaf close.dtaf",function(){l.off(".dtaf"),setTimeout(function(){e._mouseup(t)},100)}).on("submitComplete.dtaf preSubmitCancelled.dtaf close.dtaf",function(){l.off(".dtaf")}),l.submit();else{for(var a=this._range(o.row,i.row),d=this._range(o.column,i.column),r=[],c=n.settings()[0].aoColumns,u=n.columns(this.c.columns).indexes(),h=0;h<a.length;h++)r.push(f.map(d,function(t){var e=n.row(":eq("+a[h]+")",{page:"current"}),e=n.cell(e.index(),t+":visible"),t=e.data(),o=e.index(),i=c[o.column].editField;if(void 0!==i&&(t=p.util.get(i)(n.row(o.row).data())),-1!==u.indexOf(o.column))return{cell:e,data:t,label:e.data(),index:o}}));this._actionSelector(r),clearInterval(this.s.scrollInterval),this.s.scrollInterval=null}}},_range:function(t,e){var o,i=[];if(t<=e)for(o=t;o<=e;o++)i.push(o);else for(o=t;e<=o;o--)i.push(o);return i},_shiftScroll:function(t){var e,o,i,n,l=this,s=this.s.scroll,a=!1,d=t.type.includes("touch")?t.touches[0].clientX:t.pageX-r.scrollX,t=t.type.includes("touch")?t.touches[0].clientY:t.pageY-r.scrollY;t<65?e=-5:t>s.windowHeight-65&&(e=5),d<65?o=-5:d>s.windowWidth-65&&(o=5),null!==s.dtTop&&t<s.dtTop+65?i=-5:null!==s.dtTop&&t>s.dtTop+s.dtHeight-65&&(i=5),null!==s.dtLeft&&d<s.dtLeft+65?n=-5:null!==s.dtLeft&&d>s.dtLeft+s.dtWidth-65&&(n=5),e||o||i||n?(s.windowVert=e,s.windowHoriz=o,s.dtVert=i,s.dtHoriz=n,a=!0):this.s.scrollInterval&&(clearInterval(this.s.scrollInterval),this.s.scrollInterval=null),!this.s.scrollInterval&&a&&(this.s.scrollInterval=setInterval(function(){var t;r.scrollTo(r.scrollX+(s.windowHoriz||0),r.scrollY+(s.windowVert||0)),(s.dtVert||s.dtHoriz)&&(t=l.dom.dtScroll[0],s.dtVert&&(t.scrollTop+=s.dtVert),s.dtHoriz)&&(t.scrollLeft+=s.dtHoriz)},20))},_update:function(t,e){if(!1!==t){var o,t=this.s.dt,i=t.columns(this.c.columns).indexes();if(this._emitEvent("preAutoFill",[t,e]),this._editor(e),null!==this.c.update?this.c.update:!this.c.editor){for(var n=0,l=e.length;n<l;n++)for(var s=0,a=e[n].length;s<a;s++)o=e[n][s],-1!==i.indexOf(o.index.column)&&o.cell.data(o.set);t.draw(!1)}this._emitEvent("autoFill",[t,e])}}}),d.actions={increment:{available:function(t,e){e=e[0][0].label;return!isNaN(e-parseFloat(e))},option:function(t,e){return t.i18n("autoFill.increment",'Increment / decrement each cell by: <input type="number" value="1">')},execute:function(t,e,o){for(var i=+e[0][0].data,n=+f("input",o).val(),l=0,s=e.length;l<s;l++)for(var a=0,d=e[l].length;a<d;a++)e[l][a].set=i,i+=n}},fill:{available:function(t,e){return!0},option:function(t,e){return t.i18n("autoFill.fill","Fill all cells with <i>%d</i>",e[0][0].label)},execute:function(t,e,o){for(var i=e[0][0].data,n=0,l=e.length;n<l;n++)for(var s=0,a=e[n].length;s<a;s++)e[n][s].set=i}},fillHorizontal:{available:function(t,e){return 1<e.length&&1<e[0].length},option:function(t,e){return t.i18n("autoFill.fillHorizontal","Fill cells horizontally")},execute:function(t,e,o){for(var i=0,n=e.length;i<n;i++)for(var l=0,s=e[i].length;l<s;l++)e[i][l].set=e[i][0].data}},fillVertical:{available:function(t,e){return 1<e.length&&1<e[0].length},option:function(t,e){return t.i18n("autoFill.fillVertical","Fill cells vertically")},execute:function(t,e,o){for(var i=0,n=e.length;i<n;i++)for(var l=0,s=e[i].length;l<s;l++)e[i][l].set=e[0][l].data}},cancel:{available:function(){return!1},option:function(t){return t.i18n("autoFill.cancel","Cancel")},execute:function(){return!1}}},d.version="2.7.0",d.defaults={alwaysAsk:!1,closeButton:!0,focus:null,columns:"",enable:!0,update:null,editor:null,vertical:!0,horizontal:!0},d.classes={btn:"btn",closeable:"dtaf-popover-closeable"},f.fn.dataTable.Api);return t.register("autoFill()",function(){return this}),t.register("autoFill().enabled()",function(){var t=this.context[0];return!!t.autoFill&&t.autoFill.enabled()}),t.register("autoFill().enable()",function(e){return this.iterator("table",function(t){t.autoFill&&t.autoFill.enable(e)})}),t.register("autoFill().disable()",function(){return this.iterator("table",function(t){t.autoFill&&t.autoFill.disable()})}),f(m).on("preInit.dt.autofill",function(t,e,o){var i,n;"dt"===t.namespace&&(t=e.oInit.autoFill,i=p.defaults.autoFill,t||i)&&(n=f.extend({},t,i),!1!==t)&&new d(e,n)}),p.AutoFill=d,f.fn.DataTable.AutoFill=d,p});

/*! Bootstrap integration for DataTables' AutoFill
 * ©2015 SpryMedia Ltd - datatables.net/license
 */
!function(n){var o,a;"function"==typeof define&&define.amd?define(["jquery","datatables.net-bs5","datatables.net-autofill"],function(e){return n(e,window,document)}):"object"==typeof exports?(o=require("jquery"),a=function(e,t){t.fn.dataTable||require("datatables.net-bs5")(e,t),t.fn.dataTable.AutoFill||require("datatables.net-autofill")(e,t)},"undefined"==typeof window?module.exports=function(e,t){return e=e||window,t=t||o(e),a(e,t),n(t,0,e.document)}:(a(window,o),module.exports=n(o,window,window.document))):n(jQuery,window,document)}(function(e,t,n){"use strict";e=e.fn.dataTable;return e.AutoFill.classes.btn="btn btn-primary",e});

/*! Buttons for DataTables 3.0.2
 * © SpryMedia Ltd - datatables.net/license
 */
!function(e){var o,i;"function"==typeof define&&define.amd?define(["jquery","datatables.net"],function(t){return e(t,window,document)}):"object"==typeof exports?(o=require("jquery"),i=function(t,n){n.fn.dataTable||require("datatables.net")(t,n)},"undefined"==typeof window?module.exports=function(t,n){return t=t||window,n=n||o(t),i(t,n),e(n,t,t.document)}:(i(window,o),module.exports=e(o,window,window.document))):e(jQuery,window,document)}(function(x,g,m){"use strict";var e=x.fn.dataTable,o=0,C=0,w=e.ext.buttons,i=null;function v(t,n,e){x.fn.animate?t.stop().fadeIn(n,e):(t.css("display","block"),e&&e.call(t))}function y(t,n,e){x.fn.animate?t.stop().fadeOut(n,e):(t.css("display","none"),e&&e.call(t))}function _(n,t){if(!e.versionCheck("2"))throw"Warning: Buttons requires DataTables 2 or newer";if(!(this instanceof _))return function(t){return new _(t,n).container()};!0===(t=void 0===t?{}:t)&&(t={}),Array.isArray(t)&&(t={buttons:t}),this.c=x.extend(!0,{},_.defaults,t),t.buttons&&(this.c.buttons=t.buttons),this.s={dt:new e.Api(n),buttons:[],listenKeys:"",namespace:"dtb"+o++},this.dom={container:x("<"+this.c.dom.container.tag+"/>").addClass(this.c.dom.container.className)},this._constructor()}x.extend(_.prototype,{action:function(t,n){t=this._nodeToButton(t);return void 0===n?t.conf.action:(t.conf.action=n,this)},active:function(t,n){var t=this._nodeToButton(t),e=this.c.dom.button.active,o=x(t.node);return t.inCollection&&this.c.dom.collection.button&&void 0!==this.c.dom.collection.button.active&&(e=this.c.dom.collection.button.active),void 0===n?o.hasClass(e):(o.toggleClass(e,void 0===n||n),this)},add:function(t,n,e){var o=this.s.buttons;if("string"==typeof n){for(var i=n.split("-"),s=this.s,r=0,a=i.length-1;r<a;r++)s=s.buttons[+i[r]];o=s.buttons,n=+i[i.length-1]}return this._expandButton(o,t,void 0!==t?t.split:void 0,(void 0===t||void 0===t.split||0===t.split.length)&&void 0!==s,!1,n),void 0!==e&&!0!==e||this._draw(),this},collectionRebuild:function(t,n){var e=this._nodeToButton(t);if(void 0!==n){for(var o=e.buttons.length-1;0<=o;o--)this.remove(e.buttons[o].node);for(e.conf.prefixButtons&&n.unshift.apply(n,e.conf.prefixButtons),e.conf.postfixButtons&&n.push.apply(n,e.conf.postfixButtons),o=0;o<n.length;o++){var i=n[o];this._expandButton(e.buttons,i,void 0!==i&&void 0!==i.config&&void 0!==i.config.split,!0,void 0!==i.parentConf&&void 0!==i.parentConf.split,null,i.parentConf)}}this._draw(e.collection,e.buttons)},container:function(){return this.dom.container},disable:function(t){t=this._nodeToButton(t);return x(t.node).addClass(this.c.dom.button.disabled).prop("disabled",!0),this},destroy:function(){x("body").off("keyup."+this.s.namespace);for(var t=this.s.buttons.slice(),n=0,e=t.length;n<e;n++)this.remove(t[n].node);this.dom.container.remove();var o=this.s.dt.settings()[0];for(n=0,e=o.length;n<e;n++)if(o.inst===this){o.splice(n,1);break}return this},enable:function(t,n){return!1===n?this.disable(t):(n=this._nodeToButton(t),x(n.node).removeClass(this.c.dom.button.disabled).prop("disabled",!1),this)},index:function(t,n,e){n||(n="",e=this.s.buttons);for(var o=0,i=e.length;o<i;o++){var s=e[o].buttons;if(e[o].node===t)return n+o;if(s&&s.length){s=this.index(t,o+"-",s);if(null!==s)return s}}return null},name:function(){return this.c.name},node:function(t){return t?(t=this._nodeToButton(t),x(t.node)):this.dom.container},processing:function(t,n){var e=this.s.dt,o=this._nodeToButton(t);return void 0===n?x(o.node).hasClass("processing"):(x(o.node).toggleClass("processing",n),x(e.table().node()).triggerHandler("buttons-processing.dt",[n,e.button(t),e,x(t),o.conf]),this)},remove:function(t){var n=this._nodeToButton(t),e=this._nodeToHost(t),o=this.s.dt;if(n.buttons.length)for(var i=n.buttons.length-1;0<=i;i--)this.remove(n.buttons[i].node);n.conf.destroying=!0,n.conf.destroy&&n.conf.destroy.call(o.button(t),o,x(t),n.conf),this._removeKey(n.conf),x(n.node).remove();o=x.inArray(n,e);return e.splice(o,1),this},text:function(t,n){function e(t){return"function"==typeof t?t(i,s,o.conf):t}var o=this._nodeToButton(t),t=o.textNode,i=this.s.dt,s=x(o.node);return void 0===n?e(o.conf.text):(o.conf.text=n,t.html(e(n)),this)},_constructor:function(){var e=this,t=this.s.dt,o=t.settings()[0],n=this.c.buttons;o._buttons||(o._buttons=[]),o._buttons.push({inst:this,name:this.c.name});for(var i=0,s=n.length;i<s;i++)this.add(n[i]);t.on("destroy",function(t,n){n===o&&e.destroy()}),x("body").on("keyup."+this.s.namespace,function(t){var n;m.activeElement&&m.activeElement!==m.body||(n=String.fromCharCode(t.keyCode).toLowerCase(),-1!==e.s.listenKeys.toLowerCase().indexOf(n)&&e._keypress(n,t))})},_addKey:function(t){t.key&&(this.s.listenKeys+=(x.isPlainObject(t.key)?t.key:t).key)},_draw:function(t,n){t||(t=this.dom.container,n=this.s.buttons),t.children().detach();for(var e=0,o=n.length;e<o;e++)t.append(n[e].inserter),t.append(" "),n[e].buttons&&n[e].buttons.length&&this._draw(n[e].collection,n[e].buttons)},_expandButton:function(t,n,e,o,i,s,r){for(var a,l=this.s.dt,c=this.c.dom.collection,u=Array.isArray(n)?n:[n],d=0,f=(u=void 0===n?Array.isArray(e)?e:[e]:u).length;d<f;d++){var p=this._resolveExtends(u[d]);if(p)if(a=!(!p.config||!p.config.split),Array.isArray(p))this._expandButton(t,p,void 0!==h&&void 0!==h.conf?h.conf.split:void 0,o,void 0!==r&&void 0!==r.split,s,r);else{var h=this._buildButton(p,o,void 0!==p.split||void 0!==p.config&&void 0!==p.config.split,i);if(h){if(null!=s?(t.splice(s,0,h),s++):t.push(h),h.conf.buttons&&(h.collection=x("<"+c.container.content.tag+"/>"),h.conf._collection=h.collection,x(h.node).append(c.action.dropHtml),this._expandButton(h.buttons,h.conf.buttons,h.conf.split,!a,a,s,h.conf)),h.conf.split){h.collection=x("<"+c.container.tag+"/>"),h.conf._collection=h.collection;for(var b=0;b<h.conf.split.length;b++){var g=h.conf.split[b];"object"==typeof g&&(g.parent=r,void 0===g.collectionLayout&&(g.collectionLayout=h.conf.collectionLayout),void 0===g.dropup&&(g.dropup=h.conf.dropup),void 0===g.fade)&&(g.fade=h.conf.fade)}this._expandButton(h.buttons,h.conf.buttons,h.conf.split,!a,a,s,h.conf)}h.conf.parent=r,p.init&&p.init.call(l.button(h.node),l,x(h.node),p)}}}},_buildButton:function(n,t,e,o){function i(t){return"function"==typeof t?t(f,c,n):t}var s,r,a,l,c,u=this,d=this.c.dom,f=this.s.dt,p=x.extend(!0,{},d.button);if(t&&e&&d.collection.split?x.extend(!0,p,d.collection.split.action):o||t?x.extend(!0,p,d.collection.button):e&&x.extend(!0,p,d.split.button),n.spacer)return d=x("<"+p.spacer.tag+"/>").addClass("dt-button-spacer "+n.style+" "+p.spacer.className).html(i(n.text)),{conf:n,node:d,inserter:d,buttons:[],inCollection:t,isSplit:e,collection:null,textNode:d};if(n.available&&!n.available(f,n)&&!n.html)return!1;n.html?c=x(n.html):(r=function(t,n,e,o,i){o.action.call(n.button(e),t,n,e,o,i),x(n.table().node()).triggerHandler("buttons-action.dt",[n.button(e),n,e,o])},a=function(t,n,e,o){o.async?(u.processing(e[0],!0),setTimeout(function(){r(t,n,e,o,function(){u.processing(e[0],!1)})},o.async)):r(t,n,e,o,function(){})},d=n.tag||p.tag,l=void 0===n.clickBlurs||n.clickBlurs,c=x("<"+d+"/>").addClass(p.className).attr("tabindex",this.s.dt.settings()[0].iTabIndex).attr("aria-controls",this.s.dt.table().node().id).on("click.dtb",function(t){t.preventDefault(),!c.hasClass(p.disabled)&&n.action&&a(t,f,c,n),l&&c.trigger("blur")}).on("keypress.dtb",function(t){13===t.keyCode&&(t.preventDefault(),!c.hasClass(p.disabled))&&n.action&&a(t,f,c,n)}),"a"===d.toLowerCase()&&c.attr("href","#"),"button"===d.toLowerCase()&&c.attr("type","button"),s=p.liner.tag?(d=x("<"+p.liner.tag+"/>").html(i(n.text)).addClass(p.liner.className),"a"===p.liner.tag.toLowerCase()&&d.attr("href","#"),c.append(d),d):(c.html(i(n.text)),c),!1===n.enabled&&c.addClass(p.disabled),n.className&&c.addClass(n.className),n.titleAttr&&c.attr("title",i(n.titleAttr)),n.attr&&c.attr(n.attr),n.namespace||(n.namespace=".dt-button-"+C++),void 0!==n.config&&n.config.split&&(n.split=n.config.split));var h,b,g,m,v,y,d=this.c.dom.buttonContainer,d=d&&d.tag?x("<"+d.tag+"/>").addClass(d.className).append(c):c;return this._addKey(n),this.c.buttonCreated&&(d=this.c.buttonCreated(n,d)),e&&(b=(h=t?x.extend(!0,this.c.dom.split,this.c.dom.collection.split):this.c.dom.split).wrapper,g=x("<"+b.tag+"/>").addClass(b.className).append(c),m=x.extend(n,{align:h.dropdown.align,attr:{"aria-haspopup":"dialog","aria-expanded":!1},className:h.dropdown.className,closeButton:!1,splitAlignClass:h.dropdown.splitAlignClass,text:h.dropdown.text}),this._addKey(m),v=function(t,n,e,o){w.split.action.call(n.button(g),t,n,e,o),x(n.table().node()).triggerHandler("buttons-action.dt",[n.button(e),n,e,o]),e.attr("aria-expanded",!0)},y=x('<button class="'+h.dropdown.className+' dt-button"></button>').html(h.dropdown.dropHtml).on("click.dtb",function(t){t.preventDefault(),t.stopPropagation(),y.hasClass(p.disabled)||v(t,f,y,m),l&&y.trigger("blur")}).on("keypress.dtb",function(t){13===t.keyCode&&(t.preventDefault(),y.hasClass(p.disabled)||v(t,f,y,m))}),0===n.split.length&&y.addClass("dtb-hide-drop"),g.append(y).attr(m.attr)),{conf:n,node:(e?g:c).get(0),inserter:e?g:d,buttons:[],inCollection:t,isSplit:e,inSplit:o,collection:null,textNode:s}},_nodeToButton:function(t,n){for(var e=0,o=(n=n||this.s.buttons).length;e<o;e++){if(n[e].node===t)return n[e];if(n[e].buttons.length){var i=this._nodeToButton(t,n[e].buttons);if(i)return i}}},_nodeToHost:function(t,n){for(var e=0,o=(n=n||this.s.buttons).length;e<o;e++){if(n[e].node===t)return n;if(n[e].buttons.length){var i=this._nodeToHost(t,n[e].buttons);if(i)return i}}},_keypress:function(s,r){var a;r._buttonsHandled||(a=function(t){for(var n,e,o=0,i=t.length;o<i;o++)n=t[o].conf,e=t[o].node,!n.key||n.key!==s&&(!x.isPlainObject(n.key)||n.key.key!==s||n.key.shiftKey&&!r.shiftKey||n.key.altKey&&!r.altKey||n.key.ctrlKey&&!r.ctrlKey||n.key.metaKey&&!r.metaKey)||(r._buttonsHandled=!0,x(e).click()),t[o].buttons.length&&a(t[o].buttons)})(this.s.buttons)},_removeKey:function(t){var n;t.key&&(t=(x.isPlainObject(t.key)?t.key:t).key,n=this.s.listenKeys.split(""),t=x.inArray(t,n),n.splice(t,1),this.s.listenKeys=n.join(""))},_resolveExtends:function(e){function t(t){for(var n=0;!x.isPlainObject(t)&&!Array.isArray(t);){if(void 0===t)return;if("function"==typeof t){if(!(t=t.call(i,s,e)))return!1}else if("string"==typeof t){if(!w[t])return{html:t};t=w[t]}if(30<++n)throw"Buttons: Too many iterations"}return Array.isArray(t)?t:x.extend({},t)}var n,o,i=this,s=this.s.dt;for(e=t(e);e&&e.extend;){if(!w[e.extend])throw"Cannot extend unknown button type: "+e.extend;var r=t(w[e.extend]);if(Array.isArray(r))return r;if(!r)return!1;var a=r.className;void 0!==e.config&&void 0!==r.config&&(e.config=x.extend({},r.config,e.config)),e=x.extend({},r,e),a&&e.className!==a&&(e.className=a+" "+e.className),e.extend=r.extend}var l=e.postfixButtons;if(l)for(e.buttons||(e.buttons=[]),n=0,o=l.length;n<o;n++)e.buttons.push(l[n]);var c=e.prefixButtons;if(c)for(e.buttons||(e.buttons=[]),n=0,o=c.length;n<o;n++)e.buttons.splice(n,0,c[n]);return e},_popover:function(o,t,n){function i(){f=!0,y(x(h),p.fade,function(){x(this).detach()}),x(u.buttons('[aria-haspopup="dialog"][aria-expanded="true"]').nodes()).attr("aria-expanded","false"),x("div.dt-button-background").off("click.dtb-collection"),_.background(!1,p.backgroundClassName,p.fade,b),x(g).off("resize.resize.dtb-collection"),x("body").off(".dtb-collection"),u.off("buttons-action.b-internal"),u.off("destroy")}var e,s,r,a,l,c,u=t,d=this.c,f=!1,p=x.extend({align:"button-left",autoClose:!1,background:!0,backgroundClassName:"dt-button-background",closeButton:!0,containerClassName:d.dom.collection.container.className,contentClassName:d.dom.collection.container.content.className,collectionLayout:"",collectionTitle:"",dropup:!1,fade:400,popoverTitle:"",rightAlignClassName:"dt-button-right",tag:d.dom.collection.container.tag},n),h=p.tag+"."+p.containerClassName.replace(/ /g,"."),b=t.node();!1===o?i():((d=x(u.buttons('[aria-haspopup="dialog"][aria-expanded="true"]').nodes())).length&&(b.closest(h).length&&(b=d.eq(0)),i()),n=x(".dt-button",o).length,d="",3===n?d="dtb-b3":2===n?d="dtb-b2":1===n&&(d="dtb-b1"),e=x("<"+p.tag+"/>").addClass(p.containerClassName).addClass(p.collectionLayout).addClass(p.splitAlignClass).addClass(d).css("display","none").attr({"aria-modal":!0,role:"dialog"}),o=x(o).addClass(p.contentClassName).attr("role","menu").appendTo(e),b.attr("aria-expanded","true"),b.parents("body")[0]!==m.body&&(b=m.body.lastChild),p.popoverTitle?e.prepend('<div class="dt-button-collection-title">'+p.popoverTitle+"</div>"):p.collectionTitle&&e.prepend('<div class="dt-button-collection-title">'+p.collectionTitle+"</div>"),p.closeButton&&e.prepend('<div class="dtb-popover-close">&times;</div>').addClass("dtb-collection-closeable"),v(e.insertAfter(b),p.fade),n=x(t.table().container()),d=e.css("position"),"container"!==p.span&&"dt-container"!==p.align||(b=b.parent(),e.css("width",n.width())),"absolute"===d?(t=x(b[0].offsetParent),n=b.position(),d=b.offset(),a=t.offset(),s=t.position(),r=g.getComputedStyle(t[0]),a.height=t.outerHeight(),a.width=t.width()+parseFloat(r.paddingLeft),a.right=a.left+a.width,a.bottom=a.top+a.height,t=n.top+b.outerHeight(),a=n.left,e.css({top:t,left:a}),r=g.getComputedStyle(e[0]),(l=e.offset()).height=e.outerHeight(),l.width=e.outerWidth(),l.right=l.left+l.width,l.bottom=l.top+l.height,l.marginTop=parseFloat(r.marginTop),l.marginBottom=parseFloat(r.marginBottom),p.dropup&&(t=n.top-l.height-l.marginTop-l.marginBottom),"button-right"!==p.align&&!e.hasClass(p.rightAlignClassName)||(a=n.left-l.width+b.outerWidth()),"dt-container"!==p.align&&"container"!==p.align||a<n.left&&(a=-n.left),s.left+a+l.width>x(g).width()&&(a=x(g).width()-l.width-s.left),d.left+a<0&&(a=-d.left),s.top+t+l.height>x(g).height()+x(g).scrollTop()&&(t=n.top-l.height-l.marginTop-l.marginBottom),s.top+t<x(g).scrollTop()&&(t=n.top+b.outerHeight()),e.css({top:t,left:a})):((c=function(){var t=x(g).height()/2,n=e.height()/2;e.css("marginTop",-1*(n=t<n?t:n))})(),x(g).on("resize.dtb-collection",function(){c()})),p.background&&_.background(!0,p.backgroundClassName,p.fade,p.backgroundHost||b),x("div.dt-button-background").on("click.dtb-collection",function(){}),p.autoClose&&setTimeout(function(){u.on("buttons-action.b-internal",function(t,n,e,o){o[0]!==b[0]&&i()})},0),x(e).trigger("buttons-popover.dt"),u.on("destroy",i),setTimeout(function(){f=!1,x("body").on("click.dtb-collection",function(t){var n,e;!f&&(n=x.fn.addBack?"addBack":"andSelf",e=x(t.target).parent()[0],!x(t.target).parents()[n]().filter(o).length&&!x(e).hasClass("dt-buttons")||x(t.target).hasClass("dt-button-background"))&&i()}).on("keyup.dtb-collection",function(t){27===t.keyCode&&i()}).on("keydown.dtb-collection",function(t){var n=x("a, button",o),e=m.activeElement;9===t.keyCode&&(-1===n.index(e)?(n.first().focus(),t.preventDefault()):t.shiftKey?e===n[0]&&(n.last().focus(),t.preventDefault()):e===n.last()[0]&&(n.first().focus(),t.preventDefault()))})},0))}}),_.background=function(t,n,e,o){void 0===e&&(e=400),o=o||m.body,t?v(x("<div/>").addClass(n).css("display","none").insertAfter(o),e):y(x("div."+n),e,function(){x(this).removeClass(n).remove()})},_.instanceSelector=function(t,s){var r,a,l;return null==t?x.map(s,function(t){return t.inst}):(r=[],a=x.map(s,function(t){return t.name}),(l=function(t){var n;if(Array.isArray(t))for(var e=0,o=t.length;e<o;e++)l(t[e]);else if("string"==typeof t)-1!==t.indexOf(",")?l(t.split(",")):-1!==(n=x.inArray(t.trim(),a))&&r.push(s[n].inst);else if("number"==typeof t)r.push(s[t].inst);else if("object"==typeof t&&t.nodeName)for(var i=0;i<s.length;i++)s[i].inst.dom.container[0]===t&&r.push(s[i].inst);else"object"==typeof t&&r.push(t)})(t),r)},_.buttonSelector=function(t,n){for(var c=[],u=function(t,n,e){for(var o,i,s=0,r=n.length;s<r;s++)(o=n[s])&&(t.push({node:o.node,name:o.conf.name,idx:i=void 0!==e?e+s:s+""}),o.buttons)&&u(t,o.buttons,i+"-")},d=function(t,n){var e=[],o=(u(e,n.s.buttons),x.map(e,function(t){return t.node}));if(Array.isArray(t)||t instanceof x)for(s=0,r=t.length;s<r;s++)d(t[s],n);else if(null==t||"*"===t)for(s=0,r=e.length;s<r;s++)c.push({inst:n,node:e[s].node});else if("number"==typeof t)n.s.buttons[t]&&c.push({inst:n,node:n.s.buttons[t].node});else if("string"==typeof t)if(-1!==t.indexOf(","))for(var i=t.split(","),s=0,r=i.length;s<r;s++)d(i[s].trim(),n);else if(t.match(/^\d+(\-\d+)*$/)){var a=x.map(e,function(t){return t.idx});c.push({inst:n,node:e[x.inArray(t,a)].node})}else if(-1!==t.indexOf(":name")){var l=t.replace(":name","");for(s=0,r=e.length;s<r;s++)e[s].name===l&&c.push({inst:n,node:e[s].node})}else x(o).filter(t).each(function(){c.push({inst:n,node:this})});else"object"==typeof t&&t.nodeName&&-1!==(a=x.inArray(t,o))&&c.push({inst:n,node:o[a]})},e=0,o=t.length;e<o;e++){var i=t[e];d(n,i)}return c},_.stripData=function(t,n){return t="string"==typeof t&&(t=_.stripHtmlScript(t),t=_.stripHtmlComments(t),n&&!n.stripHtml||(t=e.util.stripHtml(t)),n&&!n.trim||(t=t.trim()),n&&!n.stripNewlines||(t=t.replace(/\n/g," ")),!n||n.decodeEntities)?i?i(t):(c.innerHTML=t,c.value):t},_.entityDecoder=function(t){i=t},_.stripHtmlComments=function(t){for(var n;(t=(n=t).replace(/(<!--.*?--!?>)|(<!--[\S\s]+?--!?>)|(<!--[\S\s]*?$)/g,""))!==n;);return t},_.stripHtmlScript=function(t){for(var n;(t=(n=t).replace(/<script\b[^<]*(?:(?!<\/script[^>]*>)<[^<]*)*<\/script[^>]*>/gi,""))!==n;);return t},_.defaults={buttons:["copy","excel","csv","pdf","print"],name:"main",tabIndex:0,dom:{container:{tag:"div",className:"dt-buttons"},collection:{action:{dropHtml:'<span class="dt-button-down-arrow">&#x25BC;</span>'},container:{className:"dt-button-collection",content:{className:"",tag:"div"},tag:"div"}},button:{tag:"button",className:"dt-button",active:"dt-button-active",disabled:"disabled",spacer:{className:"dt-button-spacer",tag:"span"},liner:{tag:"span",className:""}},split:{action:{className:"dt-button-split-drop-button dt-button",tag:"button"},dropdown:{align:"split-right",className:"dt-button-split-drop",dropHtml:'<span class="dt-button-down-arrow">&#x25BC;</span>',splitAlignClass:"dt-button-split-left",tag:"button"},wrapper:{className:"dt-button-split",tag:"div"}}}},x.extend(w,{collection:{text:function(t){return t.i18n("buttons.collection","Collection")},className:"buttons-collection",closeButton:!(_.version="3.0.2"),init:function(t,n){n.attr("aria-expanded",!1)},action:function(t,n,e,o){o._collection.parents("body").length?this.popover(!1,o):this.popover(o._collection,o),"keypress"===t.type&&x("a, button",o._collection).eq(0).focus()},attr:{"aria-haspopup":"dialog"}},split:{text:function(t){return t.i18n("buttons.split","Split")},className:"buttons-split",closeButton:!1,init:function(t,n){return n.attr("aria-expanded",!1)},action:function(t,n,e,o){this.popover(o._collection,o)},attr:{"aria-haspopup":"dialog"}},copy:function(){if(w.copyHtml5)return"copyHtml5"},csv:function(t,n){if(w.csvHtml5&&w.csvHtml5.available(t,n))return"csvHtml5"},excel:function(t,n){if(w.excelHtml5&&w.excelHtml5.available(t,n))return"excelHtml5"},pdf:function(t,n){if(w.pdfHtml5&&w.pdfHtml5.available(t,n))return"pdfHtml5"},pageLength:function(t){var n=t.settings()[0].aLengthMenu,e=[],o=[];if(Array.isArray(n[0]))e=n[0],o=n[1];else for(var i=0;i<n.length;i++){var s=n[i];x.isPlainObject(s)?(e.push(s.value),o.push(s.label)):(e.push(s),o.push(s))}return{extend:"collection",text:function(t){return t.i18n("buttons.pageLength",{"-1":"Show all rows",_:"Show %d rows"},t.page.len())},className:"buttons-page-length",autoClose:!0,buttons:x.map(e,function(s,t){return{text:o[t],className:"button-page-length",action:function(t,n){n.page.len(s).draw()},init:function(t,n,e){function o(){i.active(t.page.len()===s)}var i=this;t.on("length.dt"+e.namespace,o),o()},destroy:function(t,n,e){t.off("length.dt"+e.namespace)}}}),init:function(t,n,e){var o=this;t.on("length.dt"+e.namespace,function(){o.text(e.text)})},destroy:function(t,n,e){t.off("length.dt"+e.namespace)}}},spacer:{style:"empty",spacer:!0,text:function(t){return t.i18n("buttons.spacer","")}}}),e.Api.register("buttons()",function(n,e){void 0===e&&(e=n,n=void 0),this.selector.buttonGroup=n;var t=this.iterator(!0,"table",function(t){if(t._buttons)return _.buttonSelector(_.instanceSelector(n,t._buttons),e)},!0);return t._groupSelector=n,t}),e.Api.register("button()",function(t,n){t=this.buttons(t,n);return 1<t.length&&t.splice(1,t.length),t}),e.Api.registerPlural("buttons().active()","button().active()",function(n){return void 0===n?this.map(function(t){return t.inst.active(t.node)}):this.each(function(t){t.inst.active(t.node,n)})}),e.Api.registerPlural("buttons().action()","button().action()",function(n){return void 0===n?this.map(function(t){return t.inst.action(t.node)}):this.each(function(t){t.inst.action(t.node,n)})}),e.Api.registerPlural("buttons().collectionRebuild()","button().collectionRebuild()",function(e){return this.each(function(t){for(var n=0;n<e.length;n++)"object"==typeof e[n]&&(e[n].parentConf=t);t.inst.collectionRebuild(t.node,e)})}),e.Api.register(["buttons().enable()","button().enable()"],function(n){return this.each(function(t){t.inst.enable(t.node,n)})}),e.Api.register(["buttons().disable()","button().disable()"],function(){return this.each(function(t){t.inst.disable(t.node)})}),e.Api.register("button().index()",function(){var n=null;return this.each(function(t){t=t.inst.index(t.node);null!==t&&(n=t)}),n}),e.Api.registerPlural("buttons().nodes()","button().node()",function(){var n=x();return x(this.each(function(t){n=n.add(t.inst.node(t.node))})),n}),e.Api.registerPlural("buttons().processing()","button().processing()",function(n){return void 0===n?this.map(function(t){return t.inst.processing(t.node)}):this.each(function(t){t.inst.processing(t.node,n)})}),e.Api.registerPlural("buttons().text()","button().text()",function(n){return void 0===n?this.map(function(t){return t.inst.text(t.node)}):this.each(function(t){t.inst.text(t.node,n)})}),e.Api.registerPlural("buttons().trigger()","button().trigger()",function(){return this.each(function(t){t.inst.node(t.node).trigger("click")})}),e.Api.register("button().popover()",function(n,e){return this.map(function(t){return t.inst._popover(n,this.button(this[0].node),e)})}),e.Api.register("buttons().containers()",function(){var i=x(),s=this._groupSelector;return this.iterator(!0,"table",function(t){if(t._buttons)for(var n=_.instanceSelector(s,t._buttons),e=0,o=n.length;e<o;e++)i=i.add(n[e].container())}),i}),e.Api.register("buttons().container()",function(){return this.containers().eq(0)}),e.Api.register("button().add()",function(t,n,e){var o=this.context;return o.length&&(o=_.instanceSelector(this._groupSelector,o[0]._buttons)).length&&o[0].add(n,t,e),this.button(this._groupSelector,t)}),e.Api.register("buttons().destroy()",function(){return this.pluck("inst").unique().each(function(t){t.destroy()}),this}),e.Api.registerPlural("buttons().remove()","buttons().remove()",function(){return this.each(function(t){t.inst.remove(t.node)}),this}),e.Api.register("buttons.info()",function(t,n,e){var o=this;return!1===t?(this.off("destroy.btn-info"),y(x("#datatables_buttons_info"),400,function(){x(this).remove()}),clearTimeout(s),s=null):(s&&clearTimeout(s),x("#datatables_buttons_info").length&&x("#datatables_buttons_info").remove(),t=t?"<h2>"+t+"</h2>":"",v(x('<div id="datatables_buttons_info" class="dt-button-info"/>').html(t).append(x("<div/>")["string"==typeof n?"html":"append"](n)).css("display","none").appendTo("body")),void 0!==e&&0!==e&&(s=setTimeout(function(){o.buttons.info(!1)},e)),this.on("destroy.btn-info",function(){o.buttons.info(!1)})),this}),e.Api.register("buttons.exportData()",function(t){if(this.context.length)return u(new e.Api(this.context[0]),t)}),e.Api.register("buttons.exportInfo()",function(t){return{filename:n(t=t||{},this),title:a(t,this),messageTop:l(this,t,t.message||t.messageTop,"top"),messageBottom:l(this,t,t.messageBottom,"bottom")}});var s,n=function(t,n){var e;return null==(e="function"==typeof(e="*"===t.filename&&"*"!==t.title&&void 0!==t.title&&null!==t.title&&""!==t.title?t.title:t.filename)?e(t,n):e)?null:(e=(e=-1!==e.indexOf("*")?e.replace(/\*/g,x("head > title").text()).trim():e).replace(/[^a-zA-Z0-9_\u00A1-\uFFFF\.,\-_ !\(\)]/g,""))+(r(t.extension,t,n)||"")},r=function(t,n,e){return null==t?null:"function"==typeof t?t(n,e):t},a=function(t,n){t=r(t.title,t,n);return null===t?null:-1!==t.indexOf("*")?t.replace(/\*/g,x("head > title").text()||"Exported data"):t},l=function(t,n,e,o){e=r(e,n,t);return null===e?null:(n=x("caption",t.table().container()).eq(0),"*"===e?n.css("caption-side")!==o?null:n.length?n.text():"":e)},c=x("<textarea/>")[0],u=function(i,t){for(var s=x.extend(!0,{},{rows:null,columns:"",modifier:{search:"applied",order:"applied"},orthogonal:"display",stripHtml:!0,stripNewlines:!0,decodeEntities:!0,trim:!0,format:{header:function(t){return _.stripData(t,s)},footer:function(t){return _.stripData(t,s)},body:function(t){return _.stripData(t,s)}},customizeData:null,customizeZip:null},t),t=i.columns(s.columns).indexes().map(function(t){var n=i.column(t);return s.format.header(n.title(),t,n.header())}).toArray(),n=i.table().footer()?i.columns(s.columns).indexes().map(function(t){var n,e=i.column(t).footer(),o="";return e&&(o=((n=x(".dt-column-title",e)).length?n:x(e)).html()),s.format.footer(o,t,e)}).toArray():null,e=x.extend({},s.modifier),o=(i.select&&"function"==typeof i.select.info&&void 0===e.selected&&i.rows(s.rows,x.extend({selected:!0},e)).any()&&x.extend(e,{selected:!0}),i.rows(s.rows,e).indexes().toArray()),o=i.cells(o,s.columns,{order:e.order}),r=o.render(s.orthogonal).toArray(),a=o.nodes().toArray(),l=o.indexes().toArray(),c=i.columns(s.columns).count(),u=[],d=0,f=0,p=0<c?r.length/c:0;f<p;f++){for(var h=[c],b=0;b<c;b++)h[b]=s.format.body(r[d],l[d].row,l[d].column,a[d]),d++;u[f]=h}e={header:t,headerStructure:A(s.format.header,i.table().header.structure(s.columns)),footer:n,footerStructure:A(s.format.footer,i.table().footer.structure(s.columns)),body:u};return s.customizeData&&s.customizeData(e),e};function A(t,n){for(var e=0;e<n.length;e++)for(var o=0;o<n[e].length;o++){var i=n[e][o];i&&(i.title=t(i.title,o,i.cell))}return n}function t(t,n){t=new e.Api(t),n=n||t.init().buttons||e.defaults.buttons;return new _(t,n).container()}return x.fn.dataTable.Buttons=_,x.fn.DataTable.Buttons=_,x(m).on("init.dt plugin-init.dt",function(t,n){"dt"===t.namespace&&(t=n.oInit.buttons||e.defaults.buttons)&&!n._buttons&&new _(n,t).container()}),e.ext.feature.push({fnInit:t,cFeature:"B"}),e.feature&&e.feature.register("buttons",t),e});

/*! Bootstrap integration for DataTables' Buttons
 * © SpryMedia Ltd - datatables.net/license
 */
!function(o){var e,a;"function"==typeof define&&define.amd?define(["jquery","datatables.net-bs5","datatables.net-buttons"],function(t){return o(t,window,document)}):"object"==typeof exports?(e=require("jquery"),a=function(t,n){n.fn.dataTable||require("datatables.net-bs5")(t,n),n.fn.dataTable.Buttons||require("datatables.net-buttons")(t,n)},"undefined"==typeof window?module.exports=function(t,n){return t=t||window,n=n||e(t),a(t,n),o(n,0,t.document)}:(a(window,e),module.exports=o(e,window,window.document))):o(jQuery,window,document)}(function(o,t,n){"use strict";var e=o.fn.dataTable;return o.extend(!0,e.Buttons.defaults,{dom:{container:{className:"dt-buttons btn-group flex-wrap"},button:{className:"btn btn-secondary",active:"active"},collection:{action:{dropHtml:""},container:{tag:"div",className:"dropdown-menu dt-button-collection"},closeButton:!1,button:{tag:"a",className:"dt-button dropdown-item",active:"dt-button-active",disabled:"disabled",spacer:{className:"dropdown-divider",tag:"hr"}}},split:{action:{tag:"a",className:"btn btn-secondary dt-button-split-drop-button",closeButton:!1},dropdown:{tag:"button",dropHtml:"",className:"btn btn-secondary dt-button-split-drop dropdown-toggle dropdown-toggle-split",closeButton:!1,align:"split-left",splitAlignClass:"dt-button-split-left"},wrapper:{tag:"div",className:"dt-button-split btn-group",closeButton:!1}}},buttonCreated:function(t,n){return t.buttons?o('<div class="btn-group"/>').append(n):n}}),e.ext.buttons.collection.className+=" dropdown-toggle",e.ext.buttons.collection.rightAlignClassName="dropdown-menu-right",e});

/*!
 * Column visibility buttons for Buttons and DataTables.
 * © SpryMedia Ltd - datatables.net/license
 */
!function(i){var o,e;"function"==typeof define&&define.amd?define(["jquery","datatables.net","datatables.net-buttons"],function(n){return i(n,window,document)}):"object"==typeof exports?(o=require("jquery"),e=function(n,t){t.fn.dataTable||require("datatables.net")(n,t),t.fn.dataTable.Buttons||require("datatables.net-buttons")(n,t)},"undefined"==typeof window?module.exports=function(n,t){return n=n||window,t=t||o(n),e(n,t),i(t,0,n.document)}:(e(window,o),module.exports=i(o,window,window.document))):i(jQuery,window,document)}(function(n,t,i){"use strict";var e=n.fn.dataTable;return n.extend(e.ext.buttons,{colvis:function(n,t){var i=null,o={extend:"collection",init:function(n,t){i=t},text:function(n){return n.i18n("buttons.colvis","Column visibility")},className:"buttons-colvis",closeButton:!1,buttons:[{extend:"columnsToggle",columns:t.columns,columnText:t.columnText}]};return n.on("column-reorder.dt"+t.namespace,function(){n.button(null,n.button(null,i).node()).collectionRebuild([{extend:"columnsToggle",columns:t.columns,columnText:t.columnText}])}),o},columnsToggle:function(n,t){return n.columns(t.columns).indexes().map(function(n){return{extend:"columnToggle",columns:n,columnText:t.columnText}}).toArray()},columnToggle:function(n,t){return{extend:"columnVisibility",columns:t.columns,columnText:t.columnText}},columnsVisibility:function(n,t){return n.columns(t.columns).indexes().map(function(n){return{extend:"columnVisibility",columns:n,visibility:t.visibility,columnText:t.columnText}}).toArray()},columnVisibility:{columns:void 0,text:function(n,t,i){return i._columnText(n,i)},className:"buttons-columnVisibility",action:function(n,t,i,o){var t=t.columns(o.columns),e=t.visible();t.visible(void 0!==o.visibility?o.visibility:!(e.length&&e[0]))},init:function(i,n,o){var e=this;n.attr("data-cv-idx",o.columns),i.on("column-visibility.dt"+o.namespace,function(n,t){t.bDestroying||t.nTable!=i.settings()[0].nTable||e.active(i.column(o.columns).visible())}).on("column-reorder.dt"+o.namespace,function(){o.destroying||1===i.columns(o.columns).count()&&(e.text(o._columnText(i,o)),e.active(i.column(o.columns).visible()))}),this.active(i.column(o.columns).visible())},destroy:function(n,t,i){n.off("column-visibility.dt"+i.namespace).off("column-reorder.dt"+i.namespace)},_columnText:function(n,t){var i,o;return"string"==typeof t.text?t.text:(o=n.column(t.columns).title(),i=n.column(t.columns).index(),o=o.replace(/\n/g," ").replace(/<br\s*\/?>/gi," ").replace(/<select(.*?)<\/select\s*>/gi,""),o=e.Buttons.stripHtmlComments(o),o=e.util.stripHtml(o).trim(),t.columnText?t.columnText(n,i,o):o)}},colvisRestore:{className:"buttons-colvisRestore",text:function(n){return n.i18n("buttons.colvisRestore","Restore visibility")},init:function(n,t,i){n.columns().every(function(){var n=this.init();void 0===n.__visOriginal&&(n.__visOriginal=this.visible())})},action:function(n,t,i,o){t.columns().every(function(n){var t=this.init();this.visible(t.__visOriginal)})}},colvisGroup:{className:"buttons-colvisGroup",action:function(n,t,i,o){t.columns(o.show).visible(!0,!1),t.columns(o.hide).visible(!1,!1),t.columns.adjust()},show:[],hide:[]}}),e});

/*!
 * HTML5 export buttons for Buttons and DataTables.
 * © SpryMedia Ltd - datatables.net/license
 *
 * FileSaver.js (1.3.3) - MIT license
 * Copyright © 2016 Eli Grey - http://eligrey.com
 */
!function(o){var l,n;"function"==typeof define&&define.amd?define(["jquery","datatables.net","datatables.net-buttons"],function(t){return o(t,window,document)}):"object"==typeof exports?(l=require("jquery"),n=function(t,e){e.fn.dataTable||require("datatables.net")(t,e),e.fn.dataTable.Buttons||require("datatables.net-buttons")(t,e)},"undefined"==typeof window?module.exports=function(t,e){return t=t||window,e=e||l(t),n(t,e),o(e,t,t.document)}:(n(window,l),module.exports=o(l,window,window.document))):o(jQuery,window,document)}(function(S,C,u){"use strict";var e,o,t=S.fn.dataTable;function T(){return e||C.JSZip}function m(){return o||C.pdfMake}t.Buttons.pdfMake=function(t){if(!t)return m();o=t},t.Buttons.jszip=function(t){if(!t)return T();e=t};function k(t){var e="Sheet1";return e=t.sheetName?t.sheetName.replace(/[\[\]\*\/\\\?\:]/g,""):e}function c(t,e){function o(t){for(var e="",o=0,l=t.length;o<l;o++)0<o&&(e+=a),e+=r?r+(""+t[o]).replace(d,p+r)+r:t[o];return e}var l=y(e),n=t.buttons.exportData(e.exportOptions),r=e.fieldBoundary,a=e.fieldSeparator,d=new RegExp(r,"g"),p=void 0!==e.escapeChar?e.escapeChar:"\\",t="",i="",f=[];e.header&&(t=n.headerStructure.map(function(t){return o(t.map(function(t){return t?t.title:""}))}).join(l)+l),e.footer&&n.footer&&(i=n.footerStructure.map(function(t){return o(t.map(function(t){return t?t.title:""}))}).join(l)+l);for(var m=0,s=n.body.length;m<s;m++)f.push(o(n.body[m]));return{str:t+f.join(l)+l+i,rows:f.length}}function s(){var t;return-1!==navigator.userAgent.indexOf("Safari")&&-1===navigator.userAgent.indexOf("Chrome")&&-1===navigator.userAgent.indexOf("Opera")&&!!((t=navigator.userAgent.match(/AppleWebKit\/(\d+\.\d+)/))&&1<t.length&&+t[1]<603.1)}var N=function(d){var p,i,f,m,s,u,e,c,y,l,t;if(!(void 0===d||"undefined"!=typeof navigator&&/MSIE [1-9]\./.test(navigator.userAgent)))return t=d.document,p=function(){return d.URL||d.webkitURL||d},i=t.createElementNS("http://www.w3.org/1999/xhtml","a"),f="download"in i,m=/constructor/i.test(d.HTMLElement)||d.safari,s=/CriOS\/[\d]+/.test(navigator.userAgent),u=function(t){(d.setImmediate||d.setTimeout)(function(){throw t},0)},e=4e4,c=function(t){setTimeout(function(){"string"==typeof t?p().revokeObjectURL(t):t.remove()},e)},y=function(t){return/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(t.type)?new Blob([String.fromCharCode(65279),t],{type:t.type}):t},t=(l=function(t,o,e){e||(t=y(t));var l,n,r=this,e="application/octet-stream"===t.type,a=function(){for(var t=r,e="writestart progress write writeend".split(" "),o=void 0,l=(e=[].concat(e)).length;l--;){var n=t["on"+e[l]];if("function"==typeof n)try{n.call(t,o||t)}catch(t){u(t)}}};r.readyState=r.INIT,f?(l=p().createObjectURL(t),setTimeout(function(){var t,e;i.href=l,i.download=o,t=i,e=new MouseEvent("click"),t.dispatchEvent(e),a(),c(l),r.readyState=r.DONE})):(s||e&&m)&&d.FileReader?((n=new FileReader).onloadend=function(){var t=s?n.result:n.result.replace(/^data:[^;]*;/,"data:attachment/file;");d.open(t,"_blank")||(d.location.href=t),r.readyState=r.DONE,a()},n.readAsDataURL(t),r.readyState=r.INIT):(l=l||p().createObjectURL(t),!e&&d.open(l,"_blank")||(d.location.href=l),r.readyState=r.DONE,a(),c(l))}).prototype,"undefined"!=typeof navigator&&navigator.msSaveOrOpenBlob?function(t,e,o){return e=e||t.name||"download",o||(t=y(t)),navigator.msSaveOrOpenBlob(t,e)}:(t.abort=function(){},t.readyState=t.INIT=0,t.WRITING=1,t.DONE=2,t.error=t.onwritestart=t.onprogress=t.onwrite=t.onabort=t.onerror=t.onwriteend=null,function(t,e,o){return new l(t,e||t.name||"download",o)})}("undefined"!=typeof self&&self||void 0!==C&&C||this.content),y=(t.fileSave=N,function(t){return t.newline||(navigator.userAgent.match(/Windows/)?"\r\n":"\n")});function z(t){for(var e="A".charCodeAt(0),o="Z".charCodeAt(0)-e+1,l="";0<=t;)l=String.fromCharCode(t%o+e)+l,t=Math.floor(t/o)-1;return l}try{var O,D=new XMLSerializer}catch(t){}function E(t,e,o){var l=t.createElement(e);return o&&(o.attr&&S(l).attr(o.attr),o.children&&S.each(o.children,function(t,e){l.appendChild(e)}),null!==o.text)&&void 0!==o.text&&l.appendChild(t.createTextNode(o.text)),l}function A(t,e,o,l,n){var r=S("mergeCells",t);r[0].appendChild(E(t,"mergeCell",{attr:{ref:z(o)+e+":"+z(o+n-1)+(e+l-1)}})),r.attr("count",parseFloat(r.attr("count"))+1)}var R={"_rels/.rels":'<?xml version="1.0" encoding="UTF-8" standalone="yes"?><Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships"><Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument" Target="xl/workbook.xml"/></Relationships>',"xl/_rels/workbook.xml.rels":'<?xml version="1.0" encoding="UTF-8" standalone="yes"?><Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships"><Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet" Target="worksheets/sheet1.xml"/><Relationship Id="rId2" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles" Target="styles.xml"/></Relationships>',"[Content_Types].xml":'<?xml version="1.0" encoding="UTF-8" standalone="yes"?><Types xmlns="http://schemas.openxmlformats.org/package/2006/content-types"><Default Extension="xml" ContentType="application/xml" /><Default Extension="rels" ContentType="application/vnd.openxmlformats-package.relationships+xml" /><Default Extension="jpeg" ContentType="image/jpeg" /><Override PartName="/xl/workbook.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml" /><Override PartName="/xl/worksheets/sheet1.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml" /><Override PartName="/xl/styles.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml" /></Types>',"xl/workbook.xml":'<?xml version="1.0" encoding="UTF-8" standalone="yes"?><workbook xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships"><fileVersion appName="xl" lastEdited="5" lowestEdited="5" rupBuild="24816"/><workbookPr showInkAnnotation="0" autoCompressPictures="0"/><bookViews><workbookView xWindow="0" yWindow="0" windowWidth="25600" windowHeight="19020" tabRatio="500"/></bookViews><sheets><sheet name="Sheet1" sheetId="1" r:id="rId1"/></sheets><definedNames/></workbook>',"xl/worksheets/sheet1.xml":'<?xml version="1.0" encoding="UTF-8" standalone="yes"?><worksheet xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" mc:Ignorable="x14ac" xmlns:x14ac="http://schemas.microsoft.com/office/spreadsheetml/2009/9/ac"><sheetData/><mergeCells count="0"/></worksheet>',"xl/styles.xml":'<?xml version="1.0" encoding="UTF-8"?><styleSheet xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" mc:Ignorable="x14ac" xmlns:x14ac="http://schemas.microsoft.com/office/spreadsheetml/2009/9/ac"><numFmts count="6"><numFmt numFmtId="164" formatCode="[$$-409]#,##0.00;-[$$-409]#,##0.00"/><numFmt numFmtId="165" formatCode="&quot;£&quot;#,##0.00"/><numFmt numFmtId="166" formatCode="[$€-2] #,##0.00"/><numFmt numFmtId="167" formatCode="0.0%"/><numFmt numFmtId="168" formatCode="#,##0;(#,##0)"/><numFmt numFmtId="169" formatCode="#,##0.00;(#,##0.00)"/></numFmts><fonts count="5" x14ac:knownFonts="1"><font><sz val="11" /><name val="Calibri" /></font><font><sz val="11" /><name val="Calibri" /><color rgb="FFFFFFFF" /></font><font><sz val="11" /><name val="Calibri" /><b /></font><font><sz val="11" /><name val="Calibri" /><i /></font><font><sz val="11" /><name val="Calibri" /><u /></font></fonts><fills count="6"><fill><patternFill patternType="none" /></fill><fill><patternFill patternType="none" /></fill><fill><patternFill patternType="solid"><fgColor rgb="FFD9D9D9" /><bgColor indexed="64" /></patternFill></fill><fill><patternFill patternType="solid"><fgColor rgb="FFD99795" /><bgColor indexed="64" /></patternFill></fill><fill><patternFill patternType="solid"><fgColor rgb="ffc6efce" /><bgColor indexed="64" /></patternFill></fill><fill><patternFill patternType="solid"><fgColor rgb="ffc6cfef" /><bgColor indexed="64" /></patternFill></fill></fills><borders count="2"><border><left /><right /><top /><bottom /><diagonal /></border><border diagonalUp="false" diagonalDown="false"><left style="thin"><color auto="1" /></left><right style="thin"><color auto="1" /></right><top style="thin"><color auto="1" /></top><bottom style="thin"><color auto="1" /></bottom><diagonal /></border></borders><cellStyleXfs count="1"><xf numFmtId="0" fontId="0" fillId="0" borderId="0" /></cellStyleXfs><cellXfs count="68"><xf numFmtId="0" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="1" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="2" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="3" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="4" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="0" fillId="2" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="1" fillId="2" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="2" fillId="2" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="3" fillId="2" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="4" fillId="2" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="0" fillId="3" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="1" fillId="3" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="2" fillId="3" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="3" fillId="3" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="4" fillId="3" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="0" fillId="4" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="1" fillId="4" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="2" fillId="4" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="3" fillId="4" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="4" fillId="4" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="0" fillId="5" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="1" fillId="5" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="2" fillId="5" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="3" fillId="5" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="4" fillId="5" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="0" fillId="0" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="1" fillId="0" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="2" fillId="0" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="3" fillId="0" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="4" fillId="0" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="0" fillId="2" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="1" fillId="2" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="2" fillId="2" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="3" fillId="2" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="4" fillId="2" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="0" fillId="3" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="1" fillId="3" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="2" fillId="3" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="3" fillId="3" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="4" fillId="3" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="0" fillId="4" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="1" fillId="4" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="2" fillId="4" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="3" fillId="4" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="4" fillId="4" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="0" fillId="5" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="1" fillId="5" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="2" fillId="5" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="3" fillId="5" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="4" fillId="5" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/><xf numFmtId="0" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyAlignment="1"><alignment horizontal="left"/></xf><xf numFmtId="0" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyAlignment="1"><alignment horizontal="center"/></xf><xf numFmtId="0" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyAlignment="1"><alignment horizontal="right"/></xf><xf numFmtId="0" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyAlignment="1"><alignment horizontal="fill"/></xf><xf numFmtId="0" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyAlignment="1"><alignment textRotation="90"/></xf><xf numFmtId="0" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyAlignment="1"><alignment wrapText="1"/></xf><xf numFmtId="9"   fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/><xf numFmtId="164" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/><xf numFmtId="165" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/><xf numFmtId="166" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/><xf numFmtId="167" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/><xf numFmtId="168" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/><xf numFmtId="169" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/><xf numFmtId="3" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/><xf numFmtId="4" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/><xf numFmtId="1" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/><xf numFmtId="2" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/><xf numFmtId="14" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/></cellXfs><cellStyles count="1"><cellStyle name="Normal" xfId="0" builtinId="0" /></cellStyles><dxfs count="0" /><tableStyles count="0" defaultTableStyle="TableStyleMedium9" defaultPivotStyle="PivotStyleMedium4" /></styleSheet>'},_=[{match:/^\-?\d+\.\d%$/,style:60,fmt:function(t){return t/100}},{match:/^\-?\d+\.?\d*%$/,style:56,fmt:function(t){return t/100}},{match:/^\-?\$[\d,]+.?\d*$/,style:57},{match:/^\-?£[\d,]+.?\d*$/,style:58},{match:/^\-?€[\d,]+.?\d*$/,style:59},{match:/^\-?\d+$/,style:65},{match:/^\-?\d+\.\d{2}$/,style:66},{match:/^\([\d,]+\)$/,style:61,fmt:function(t){return-1*t.replace(/[\(\)]/g,"")}},{match:/^\([\d,]+\.\d{2}\)$/,style:62,fmt:function(t){return-1*t.replace(/[\(\)]/g,"")}},{match:/^\-?[\d,]+$/,style:63},{match:/^\-?[\d,]+\.\d{2}$/,style:64},{match:/^[\d]{4}\-[01][\d]\-[0123][\d]$/,style:67,fmt:function(t){return Math.round(25569+Date.parse(t)/864e5)}}];return t.ext.buttons.copyHtml5={className:"buttons-copy buttons-html5",text:function(t){return t.i18n("buttons.copy","Copy")},action:function(t,e,o,l,n){var r=c(e,l),a=e.buttons.exportInfo(l),d=y(l),p=r.str,i=S("<div/>").css({height:1,width:1,overflow:"hidden",position:"fixed",top:0,left:0}),d=(a.title&&(p=a.title+d+d+p),a.messageTop&&(p=a.messageTop+d+d+p),a.messageBottom&&(p=p+d+d+a.messageBottom),l.customize&&(p=l.customize(p,l,e)),S("<textarea readonly/>").val(p).appendTo(i));if(u.queryCommandSupported("copy")){i.appendTo(e.table().container()),d[0].focus(),d[0].select();try{var f=u.execCommand("copy");if(i.remove(),f)return e.buttons.info(e.i18n("buttons.copyTitle","Copy to clipboard"),e.i18n("buttons.copySuccess",{1:"Copied one row to clipboard",_:"Copied %d rows to clipboard"},r.rows),2e3),void n()}catch(t){}}function m(){s.off("click.buttons-copy"),S(u).off(".buttons-copy"),e.buttons.info(!1)}var a=S("<span>"+e.i18n("buttons.copyKeys","Press <i>ctrl</i> or <i>⌘</i> + <i>C</i> to copy the table data<br>to your system clipboard.<br><br>To cancel, click this message or press escape.")+"</span>").append(i),s=(e.buttons.info(e.i18n("buttons.copyTitle","Copy to clipboard"),a,0),d[0].focus(),d[0].select(),S(a).closest(".dt-button-info"));s.on("click.buttons-copy",m),S(u).on("keydown.buttons-copy",function(t){27===t.keyCode&&(m(),n())}).on("copy.buttons-copy cut.buttons-copy",function(){m(),n()})},async:100,exportOptions:{},fieldSeparator:"\t",fieldBoundary:"",header:!0,footer:!0,title:"*",messageTop:"*",messageBottom:"*"},t.ext.buttons.csvHtml5={bom:!1,className:"buttons-csv buttons-html5",available:function(){return void 0!==C.FileReader&&C.Blob},text:function(t){return t.i18n("buttons.csv","CSV")},action:function(t,e,o,l,n){var r=c(e,l).str,a=e.buttons.exportInfo(l),d=l.charset;l.customize&&(r=l.customize(r,l,e)),d=!1!==d?(d=d||u.characterSet||u.charset)&&";charset="+d:"",l.bom&&(r=String.fromCharCode(65279)+r),N(new Blob([r],{type:"text/csv"+d}),a.filename,!0),n()},async:100,filename:"*",extension:".csv",exportOptions:{},fieldSeparator:",",fieldBoundary:'"',escapeChar:'"',charset:null,header:!0,footer:!0},t.ext.buttons.excelHtml5={className:"buttons-excel buttons-html5",available:function(){return void 0!==C.FileReader&&void 0!==T()&&!s()&&D},text:function(t){return t.i18n("buttons.excel","Excel")},action:function(t,e,o,f,l){function n(t){return t=R[t],S.parseXML(t)}function r(t){s=E(c,"row",{attr:{r:m=u+1}});for(var e=0,o=t.length;e<o;e++){var l=z(e)+""+m,n=null;if(null===t[e]||void 0===t[e]||""===t[e]){if(!0!==f.createEmptyCells)continue;t[e]=""}var r=t[e];t[e]="function"==typeof t[e].trim?t[e].trim():t[e];for(var a=0,d=_.length;a<d;a++){var p=_[a];if(t[e].match&&!t[e].match(/^0\d+/)&&t[e].match(p.match)){var i=t[e].replace(/[^\d\.\-]/g,"");p.fmt&&(i=p.fmt(i)),n=E(c,"c",{attr:{r:l,s:p.style},children:[E(c,"v",{text:i})]});break}}n=n||("number"==typeof t[e]||t[e].match&&t[e].match(/^-?\d+(\.\d+)?([eE]\-?\d+)?$/)&&!t[e].match(/^0\d+/)?E(c,"c",{attr:{t:"n",r:l},children:[E(c,"v",{text:t[e]})]}):(r=r.replace?r.replace(/[\x00-\x09\x0B\x0C\x0E-\x1F\x7F-\x9F]/g,""):r,E(c,"c",{attr:{t:"inlineStr",r:l},children:{row:E(c,"is",{children:{row:E(c,"t",{text:r,attr:{"xml:space":"preserve"}})}})}}))),s.appendChild(n)}y.appendChild(s),u++}function a(t){t.forEach(function(t){r(t.map(function(t){return t?t.title:""})),S("row:last c",c).attr("s","2"),t.forEach(function(t,e){t&&(1<t.colSpan||1<t.rowSpan)&&A(c,u,e,t.rowSpan,t.colSpan)})})}var d,m,s,u=0,c=n("xl/worksheets/sheet1.xml"),y=c.getElementsByTagName("sheetData")[0],p={_rels:{".rels":n("_rels/.rels")},xl:{_rels:{"workbook.xml.rels":n("xl/_rels/workbook.xml.rels")},"workbook.xml":n("xl/workbook.xml"),"styles.xml":n("xl/styles.xml"),worksheets:{"sheet1.xml":c}},"[Content_Types].xml":n("[Content_Types].xml")},i=e.buttons.exportData(f.exportOptions),I=(f.customizeData&&f.customizeData(i),e.buttons.exportInfo(f));I.title&&(r([I.title]),A(c,u,0,1,i.header.length),S("row:last c",c).attr("s","51")),I.messageTop&&(r([I.messageTop]),A(c,u,0,1,i.header.length)),f.header&&a(i.headerStructure);for(var F=u,x=0,h=i.body.length;x<h;x++)r(i.body[x]);d=u,f.footer&&i.footer&&a(i.footerStructure),I.messageBottom&&(r([I.messageBottom]),A(c,u,0,1,i.header.length));var b=E(c,"cols");S("worksheet",c).prepend(b);for(var g=0,v=i.header.length;g<v;g++)b.appendChild(E(c,"col",{attr:{min:g+1,max:g+1,width:function(t,e){var o=t.header[e].length;t.footer&&t.footer[e]&&t.footer[e].length>o&&(o=t.footer[e].length);for(var l=0,n=t.body.length;l<n;l++){var r,a=t.body[l][e];if(40<(o=o<(r=(-1!==(a=null!=a?a.toString():"").indexOf("\n")?((r=a.split("\n")).sort(function(t,e){return e.length-t.length}),r[0]):a).length)?r:o))return 54}return 6<(o*=1.35)?o:6}(i,g),customWidth:1}}));var w=p.xl["workbook.xml"];S("sheets sheet",w).attr("name",k(f)),f.autoFilter&&(S("mergeCells",c).before(E(c,"autoFilter",{attr:{ref:"A"+F+":"+z(i.header.length-1)+d}})),S("definedNames",w).append(E(w,"definedName",{attr:{name:"_xlnm._FilterDatabase",localSheetId:"0",hidden:1},text:"'"+k(f).replace(/'/g,"''")+"'!$A$"+F+":"+z(i.header.length-1)+d}))),f.customize&&f.customize(p,f,e),0===S("mergeCells",c).children().length&&S("mergeCells",c).remove();var w=new(T()),F={compression:"DEFLATE",type:"blob",mimeType:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"},B=(!function f(m,t){void 0===O&&(O=-1===D.serializeToString((new C.DOMParser).parseFromString(R["xl/worksheets/sheet1.xml"],"text/xml")).indexOf("xmlns:r")),S.each(t,function(t,e){if(S.isPlainObject(e))f(m.folder(t),e);else{if(O){for(var o,l=e.childNodes[0],n=[],r=l.attributes.length-1;0<=r;r--){var a=l.attributes[r].nodeName,d=l.attributes[r].nodeValue;-1!==a.indexOf(":")&&(n.push({name:a,value:d}),l.removeAttribute(a))}for(r=0,o=n.length;r<o;r++){var p=e.createAttribute(n[r].name.replace(":","_dt_b_namespace_token_"));p.value=n[r].value,l.setAttributeNode(p)}}var i=D.serializeToString(e),i=(i=O?(i=(i=-1===i.indexOf("<?xml")?'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>'+i:i).replace(/_dt_b_namespace_token_/g,":")).replace(/xmlns:NS[\d]+="" NS[\d]+:/g,""):i).replace(/<([^<>]*?) xmlns=""([^<>]*?)>/g,"<$1 $2>");m.file(t,i)}})}(w,p),I.filename);175<B&&(B=B.substr(0,175)),f.customizeZip&&f.customizeZip(w,i,B),w.generateAsync?w.generateAsync(F).then(function(t){N(t,B),l()}):(N(w.generate(F),B),l())},async:100,filename:"*",extension:".xlsx",exportOptions:{},header:!0,footer:!0,title:"*",messageTop:"*",messageBottom:"*",createEmptyCells:!1,autoFilter:!1,sheetName:""},t.ext.buttons.pdfHtml5={className:"buttons-pdf buttons-html5",available:function(){return void 0!==C.FileReader&&m()},text:function(t){return t.i18n("buttons.pdf","PDF")},action:function(t,e,o,l,n){var r=e.buttons.exportData(l.exportOptions),a=e.buttons.exportInfo(l),d=[];l.header&&r.headerStructure.forEach(function(t){d.push(t.map(function(t){return t?{text:t.title,colSpan:t.colspan,rowSpan:t.rowspan,style:"tableHeader"}:{}}))});for(var p=0,i=r.body.length;p<i;p++)d.push(r.body[p].map(function(t){return{text:null==t?"":"string"==typeof t?t:t.toString()}}));l.footer&&r.footerStructure.forEach(function(t){d.push(t.map(function(t){return t?{text:t.title,colSpan:t.colspan,rowSpan:t.rowspan,style:"tableHeader"}:{}}))});var f={pageSize:l.pageSize,pageOrientation:l.orientation,content:[{style:"table",table:{headerRows:r.headerStructure.length,footerRows:r.footerStructure.length,body:d},layout:{hLineWidth:function(t,e){return 0===t||t===e.table.body.length?0:.5},vLineWidth:function(){return 0},hLineColor:function(t,e){return t===e.table.headerRows||t===e.table.body.length-e.table.footerRows?"#333":"#ddd"},fillColor:function(t){return t<r.headerStructure.length?"#fff":t%2==0?"#f3f3f3":null},paddingTop:function(){return 5},paddingBottom:function(){return 5}}}],styles:{tableHeader:{bold:!0,fontSize:11,alignment:"center"},tableFooter:{bold:!0,fontSize:11},table:{margin:[0,5,0,5]},title:{alignment:"center",fontSize:13},message:{}},defaultStyle:{fontSize:10}},e=(a.messageTop&&f.content.unshift({text:a.messageTop,style:"message",margin:[0,0,0,12]}),a.messageBottom&&f.content.push({text:a.messageBottom,style:"message",margin:[0,0,0,12]}),a.title&&f.content.unshift({text:a.title,style:"title",margin:[0,0,0,12]}),l.customize&&l.customize(f,l,e),m().createPdf(f));"open"!==l.download||s()?e.download(a.filename):e.open(),n()},async:100,title:"*",filename:"*",extension:".pdf",exportOptions:{},orientation:"portrait",pageSize:"en-US"===navigator.language||"en-CA"===navigator.language?"LETTER":"A4",header:!0,footer:!0,messageTop:"*",messageBottom:"*",customize:null,download:"download"},t});

/*!
 * Print button for Buttons and DataTables.
 * © SpryMedia Ltd - datatables.net/license
 */
!function(n){var o,r;"function"==typeof define&&define.amd?define(["jquery","datatables.net","datatables.net-buttons"],function(t){return n(t,window,document)}):"object"==typeof exports?(o=require("jquery"),r=function(t,e){e.fn.dataTable||require("datatables.net")(t,e),e.fn.dataTable.Buttons||require("datatables.net-buttons")(t,e)},"undefined"==typeof window?module.exports=function(t,e){return t=t||window,e=e||o(t),r(t,e),n(e,t,t.document)}:(r(window,o),module.exports=n(o,window,window.document))):n(jQuery,window,document)}(function(p,m,t){"use strict";function b(t){return n.href=t,-1===(t=n.host).indexOf("/")&&0!==n.pathname.indexOf("/")&&(t+="/"),n.protocol+"//"+t+n.pathname+n.search}var e=p.fn.dataTable,n=t.createElement("a");return e.ext.buttons.print={className:"buttons-print",text:function(t){return t.i18n("buttons.print","Print")},action:function(t,e,n,o,r){var i=e.buttons.exportData(p.extend({decodeEntities:!1},o.exportOptions)),a=e.buttons.exportInfo(o),u=e.columns(o.exportOptions.columns).nodes().map(function(t){return t.className}).toArray(),s='<table class="'+e.table().node().className+'">';o.header&&(s+="<thead>"+i.headerStructure.map(function(t){return"<tr>"+t.map(function(t){return t?'<th colspan="'+t.colspan+'" rowspan="'+t.rowspan+'">'+t.title+"</th>":""}).join("")+"</tr>"}).join("")+"</thead>"),s+="<tbody>";for(var d=0,c=i.body.length;d<c;d++)s+=function(t,e){for(var n="<tr>",o=0,r=t.length;o<r;o++){var i=null===t[o]||void 0===t[o]?"":t[o];n+="<"+e+" "+(u[o]?'class="'+u[o]+'"':"")+">"+i+"</"+e+">"}return n+"</tr>"}(i.body[d],"td");s+="</tbody>",o.footer&&i.footer&&(s+="<tfoot>"+i.footerStructure.map(function(t){return"<tr>"+t.map(function(t){return t?'<th colspan="'+t.colspan+'" rowspan="'+t.rowspan+'">'+t.title+"</th>":""}).join("")+"</tr>"}).join("")+"</tfoot>"),s+="</table>";var l=m.open("","");if(l){l.document.close();var f="<title>"+a.title+"</title>";p("style, link").each(function(){f+=function(t){t=p(t).clone()[0];return"link"===t.nodeName.toLowerCase()&&(t.href=b(t.href)),t.outerHTML}(this)});try{l.document.head.innerHTML=f}catch(t){p(l.document.head).html(f)}l.document.body.innerHTML="<h1>"+a.title+"</h1><div>"+(a.messageTop||"")+"</div>"+s+"<div>"+(a.messageBottom||"")+"</div>",p(l.document.body).addClass("dt-print-view"),p("img",l.document.body).each(function(t,e){e.setAttribute("src",b(e.getAttribute("src")))}),o.customize&&o.customize(l,o,e);l.setTimeout(function(){o.autoPrint&&(l.print(),l.close())},1e3),r()}else e.buttons.info(e.i18n("buttons.printErrorTitle","Unable to open print view"),e.i18n("buttons.printErrorMsg","Please allow popups in your browser for this site to be able to view the print view."),5e3)},async:100,title:"*",messageTop:"*",messageBottom:"*",exportOptions:{},header:!0,footer:!0,autoPrint:!0,customize:null},e});

/*! ColReorder 2.0.3
 * © SpryMedia Ltd - datatables.net/license
 */
!function(r){var o,n;"function"==typeof define&&define.amd?define(["jquery","datatables.net"],function(t){return r(t,window,document)}):"object"==typeof exports?(o=require("jquery"),n=function(t,e){e.fn.dataTable||require("datatables.net")(t,e)},"undefined"==typeof window?module.exports=function(t,e){return t=t||window,e=e||o(t),n(t,e),r(e,0,t.document)}:(n(window,o),module.exports=r(o,window,window.document))):r(jQuery,window,document)}(function(u,t,h){"use strict";var n=u.fn.dataTable;function f(t,e,r,o){var n=t.splice(e,r);n.unshift(0),n.unshift(o<e?o:o-r+1),t.splice.apply(t,n)}function l(t){t.rows().invalidate("data"),t.column(0).visible(t.column(0).visible()),t.columns.adjust();var e=t.colReorder.order();t.trigger("columns-reordered",[{order:e,mapping:g(e)}])}function s(t){return t.settings()[0].aoColumns.map(function(t){return t._crOriginalIdx})}function p(t,e,r,o){for(var n=[],s=0;s<t.length;s++){var i=t[s];f(i,r[0],r.length,o);for(var l=0;l<i.length;l++){var a,d=i[l].cell;n.includes(d)||(a=d.getAttribute("data-dt-column").split(",").map(function(t){return e[t]}).join(","),d.setAttribute("data-dt-column",a),n.push(d))}}}function i(t){t.columns().iterator("column",function(t,e){t=t.aoColumns;void 0===t[e]._crOriginalIdx&&(t[e]._crOriginalIdx=e)})}function g(t){for(var e=[],r=0;r<t.length;r++)e[t[r]]=r;return e}function a(t,e,r){var o,n=t.settings()[0],s=n.aoColumns,i=s.map(function(t,e){return e});if(!e.includes(r)){f(i,e[0],e.length,r);var l=g(i);for(f(s,e[0],e.length,r),o=0;o<n.aoData.length;o++){var a=n.aoData[o];if(a){var d=a.anCells;if(d)for(f(d,e[0],e.length,r),u=0;u<d.length;u++)a.nTr&&d[u]&&s[u].bVisible&&a.nTr.appendChild(d[u]),d[u]&&d[u]._DT_CellIndex&&(d[u]._DT_CellIndex.column=u)}}for(o=0;o<s.length;o++){for(var c=s[o],u=0;u<c.aDataSort.length;u++)c.aDataSort[u]=l[c.aDataSort[u]];c.idx=l[c.idx],c.bVisible&&n.colgroup.append(c.colEl)}p(n.aoHeader,l,e,r),p(n.aoFooter,l,e,r),f(n.aoPreSearchCols,e[0],e.length,r),m(l,n.aaSorting),Array.isArray(n.aaSortingFixed)?m(l,n.aaSortingFixed):(n.aaSortingFixed.pre||n.aaSortingFixed.post)&&m(l,n.aaSortingFixed.pre),n.aLastSort.forEach(function(t){t.src=l[t.src]}),t.trigger("column-reorder",[t.settings()[0],{from:e,to:r,mapping:l}])}}function m(t,e){if(e)for(var r=0;r<e.length;r++){var o=e[r];"number"==typeof o?e[r]=t[o]:u.isPlainObject(o)&&void 0!==o.idx?o.idx=t[o.idx]:Array.isArray(o)&&"number"==typeof o[0]&&(o[0]=t[o[0]])}}function d(t,e,r){var o=!1;if(e.length!==t.columns().count())t.error("ColReorder - column count mismatch");else{for(var n=g(e=r?c(t,e,"toCurrent"):e),s=0;s<n.length;s++){var i=n.indexOf(s);s!==i&&(f(n,i,1,s),a(t,[i],s),o=!0)}o&&l(t)}}function c(t,e,r){var o=t.colReorder.order(),n=t.settings()[0].aoColumns;return"toCurrent"===r||"fromOriginal"===r?Array.isArray(e)?e.map(function(t){return o.indexOf(t)}):o.indexOf(e):Array.isArray(e)?e.map(function(t){return n[t]._crOriginalIdx}):n[e]._crOriginalIdx}function v(t,e,r){var o=t.columns().count();return!(e[0]<r&&r<e[e.length]||e[0]<0&&e[e.length-1]>o||r<0&&o<r||!e.includes(r)&&(!y(t.table().header.structure(),e,r)||!y(t.table().footer.structure(),e,r)))}function y(t,e,r){for(var o=function(t){for(var e=[],r=0;r<t.length;r++){e.push([]);for(var o=0;o<t[r].length;o++){var n=t[r][o];if(n)for(var s=0;s<n.rowspan;s++){e[r+s]||(e[r+s]=[]);for(var i=0;i<n.colspan;i++)e[r+s][o+i]=n.cell}}}return e}(t),n=0;n<o.length;n++)f(o[n],e[0],e.length,r);for(n=0;n<o.length;n++)for(var s=[],i=0;i<o[n].length;i++){var l=o[n][i];if(s.includes(l)){if(s[s.length-1]!==l)return}else s.push(l)}return 1}_.prototype.disable=function(){return this.c.enable=!1,this},_.prototype.enable=function(t){return!1===(t=void 0===t?!0:t)?this.disable():(this.c.enable=!0,this)},_.prototype._addListener=function(t){var e=this;u(t).on("selectstart.colReorder",function(){return!1}).on("mousedown.colReorder touchstart.colReorder",function(t){"mousedown"===t.type&&1!==t.which||e.c.enable&&e._mouseDown(t,this)})},_.prototype._createDragNode=function(){var t=this.s.mouse.target,e=t.parent(),r=e.parent(),o=r.parent(),n=t.clone();this.dom.drag=u(o[0].cloneNode(!1)).addClass("dtcr-cloned").append(u(r[0].cloneNode(!1)).append(u(e[0].cloneNode(!1)).append(n[0]))).css({position:"absolute",top:0,left:0,width:u(t).outerWidth(),height:u(t).outerHeight()}).appendTo("body")},_.prototype._cursorPosition=function(t,e){return(-1!==t.type.indexOf("touch")?t.originalEvent.touches[0]:t)[e]},_.prototype._mouseDown=function(t,e){for(var r=this,o=u(t.target).closest("th, td"),n=o.offset(),s=this.dt.columns(this.c.columns).indexes().toArray(),i=u(e).attr("data-dt-column").split(",").map(function(t){return parseInt(t,10)}),l=0;l<i.length;l++)if(!s.includes(i[l]))return!1;this.s.mouse.start.x=this._cursorPosition(t,"pageX"),this.s.mouse.start.y=this._cursorPosition(t,"pageY"),this.s.mouse.offset.x=this._cursorPosition(t,"pageX")-n.left,this.s.mouse.offset.y=this._cursorPosition(t,"pageY")-n.top,this.s.mouse.target=o,this.s.mouse.targets=i;for(var a=0;a<i.length;a++){var d=this.dt.cells(null,i[a],{page:"current"}).nodes().to$(),c="dtcr-moving";0===a&&(c+=" dtcr-moving-first"),a===i.length-1&&(c+=" dtcr-moving-last"),d.addClass(c)}this._regions(i),this._scrollRegions(),u(h).on("mousemove.colReorder touchmove.colReorder",function(t){r._mouseMove(t)}).on("mouseup.colReorder touchend.colReorder",function(t){r._mouseUp(t)})},_.prototype._mouseMove=function(t){if(null===this.dom.drag){if(Math.pow(Math.pow(this._cursorPosition(t,"pageX")-this.s.mouse.start.x,2)+Math.pow(this._cursorPosition(t,"pageY")-this.s.mouse.start.y,2),.5)<5)return;u(h.body).addClass("dtcr-dragging"),this._createDragNode()}this.dom.drag.css({left:this._cursorPosition(t,"pageX")-this.s.mouse.offset.x,top:this._cursorPosition(t,"pageY")-this.s.mouse.offset.y});var e=u(this.dt.table().node()).offset().left,r=this._cursorPosition(t,"pageX")-e,e=this.s.dropZones.find(function(t){return t.left<=r&&r<=t.left+t.width});this.s.mouse.absLeft=this._cursorPosition(t,"pageX"),e&&!e.self&&this._move(e,r)},_.prototype._mouseUp=function(t){u(h).off(".colReorder"),u(h.body).removeClass("dtcr-dragging"),this.dom.drag&&(this.dom.drag.remove(),this.dom.drag=null),this.s.scrollInterval&&clearInterval(this.s.scrollInterval),this.dt.cells(".dtcr-moving").nodes().to$().removeClass("dtcr-moving dtcr-moving-first dtcr-moving-last")},_.prototype._move=function(t,e){var r,o,n=this,s=(this.dt.colReorder.move(this.s.mouse.targets,t.colIdx),this.s.mouse.targets=u(this.s.mouse.target).attr("data-dt-column").split(",").map(function(t){return parseInt(t,10)}),this._regions(this.s.mouse.targets),this.s.mouse.targets.filter(function(t){return n.dt.column(t).visible()})),t=this.s.dropZones.find(function(t){return t.colIdx===s[0]}),i=this.s.dropZones.indexOf(t);t.left>e&&(o=t.left-e,r=this.s.dropZones[i-1],t.left-=o,t.width+=o,r)&&(r.width-=o),(t=this.s.dropZones.find(function(t){return t.colIdx===s[s.length-1]})).left+t.width<e&&(r=e-(t.left+t.width),o=this.s.dropZones[i+1],t.width+=r,o)&&(o.left+=r,o.width-=r)},_.prototype._regions=function(n){var s=this,i=[],l=0,a=0,d=this.dt.columns(this.c.columns).indexes().toArray(),c=this.dt.columns().widths();this.dt.columns().every(function(t,e,r){var o;this.visible()&&(o=c[t],d.includes(t)&&(v(s.dt,n,t)?i.push({colIdx:t,left:l-a,self:n[0]<=t&&t<=n[n.length-1],width:o+a}):t<n[0]?i.length&&(i[i.length-1].width+=o):t>n[n.length-1]&&(a+=o)),l+=o)}),this.s.dropZones=i},_.prototype._isScrolling=function(){return this.dt.table().body().parentNode!==this.dt.table().header().parentNode},_.prototype._scrollRegions=function(){var e,r,o,n;this._isScrolling()&&(r=u((e=this).dt.table().container()).position().left,o=u(this.dt.table().container()).outerWidth(),n=this.dt.table().body().parentElement.parentElement,this.s.scrollInterval=setInterval(function(){var t=e.s.mouse.absLeft;t<r+75&&n.scrollLeft?n.scrollLeft-=5:r+o-75<t&&n.scrollLeft<n.scrollWidth&&(n.scrollLeft+=5)},25))},_.defaults={columns:"",enable:!0,order:null},_.version="2.0.3";
/*! ColReorder 2.0.3
 * © SpryMedia Ltd - datatables.net/license
 */var b=_;function _(o,t){this.dom={drag:null},this.c={columns:null,enable:null,order:null},this.s={dropZones:[],mouse:{absLeft:-1,offset:{x:-1,y:-1},start:{x:-1,y:-1},target:null,targets:[]},scrollInterval:null};var e,r=this;o.settings()[0]._colReorder||((o.settings()[0]._colReorder=this).dt=o,u.extend(this.c,_.defaults,t),i(o),o.on("stateSaveParams",function(t,e,r){r.colReorder=s(o)}),o.on("destroy",function(){o.off(".colReorder"),o.colReorder.reset()}),t=o.state.loaded(),e=this.c.order,(e=t&&t.colReorder?t.colReorder:e)&&o.ready(function(){d(o,e,!0)}),o.table().header.structure().forEach(function(t){for(var e=0;e<t.length;e++)t[e]&&t[e].cell&&r._addListener(t[e].cell)}))}return n.Api.register("colReorder.enable()",function(e){return this.iterator("table",function(t){t._colReorder&&t._colReorder.enable(e)})}),n.Api.register("colReorder.disable()",function(){return this.iterator("table",function(t){t._colReorder&&t._colReorder.disable()})}),n.Api.register("colReorder.move()",function(t,e){return i(this),v(this,t=Array.isArray(t)?t:[t],e)?this.tables().every(function(){a(this,t,e),l(this)}):(this.error("ColReorder - invalid move"),this)}),n.Api.register("colReorder.order()",function(t,e){return i(this),t?this.tables().every(function(){d(this,t,e)}):this.context.length?s(this):null}),n.Api.register("colReorder.reset()",function(){return i(this),this.tables().every(function(){var t=this.columns().every(function(t){return t}).flatten().toArray();d(this,t,!0)})}),n.Api.register("colReorder.transpose()",function(t,e){return i(this),c(this,t,e=e||"toCurrent")}),n.ColReorder=b,u(h).on("stateLoadInit.dt",function(t,e,r){if("dt"===t.namespace){t=new n.Api(e);if(r.colReorder)if(t.ready())d(t,r.colReorder,!0);else{m(g(r.colReorder),r.order);for(var o=0;o<r.columns.length;o++)r.columns[o]._cr_sort=r.colReorder[o];r.columns.sort(function(t,e){return t._cr_sort-e._cr_sort})}}}),u(h).on("preInit.dt",function(t,e){var r,o;"dt"===t.namespace&&(t=e.oInit.colReorder,o=n.defaults.colReorder,t||o)&&(r=u.extend({},o,t),!1!==t)&&(o=new n.Api(e),new b(o,r))}),n});

/*! FixedHeader 4.0.1
 * © SpryMedia Ltd - datatables.net/license
 */
!function(o){var i,s;"function"==typeof define&&define.amd?define(["jquery","datatables.net"],function(t){return o(t,window,document)}):"object"==typeof exports?(i=require("jquery"),s=function(t,e){e.fn.dataTable||require("datatables.net")(t,e)},"undefined"==typeof window?module.exports=function(t,e){return t=t||window,e=e||i(t),s(t,e),o(e,t,t.document)}:(s(window,i),module.exports=o(i,window,window.document))):o(jQuery,window,document)}(function(b,m,v){"use strict";function s(t,e){if(!d.versionCheck("2"))throw"Warning: FixedHeader requires DataTables 2 or newer";if(!(this instanceof s))throw"FixedHeader must be initialised with the 'new' keyword.";if(!0===e&&(e={}),t=new d.Api(t),this.c=b.extend(!0,{},s.defaults,e),this.s={dt:t,position:{theadTop:0,tbodyTop:0,tfootTop:0,tfootBottom:0,width:0,left:0,tfootHeight:0,theadHeight:0,windowHeight:b(m).height(),visible:!0},headerMode:null,footerMode:null,autoWidth:t.settings()[0].oFeatures.bAutoWidth,namespace:".dtfc"+o++,scrollLeft:{header:-1,footer:-1},enable:!0,autoDisable:!1},this.dom={floatingHeader:null,thead:b(t.table().header()),tbody:b(t.table().body()),tfoot:b(t.table().footer()),header:{host:null,floating:null,floatingParent:b('<div class="dtfh-floatingparent"><div></div></div>'),placeholder:null},footer:{host:null,floating:null,floatingParent:b('<div class="dtfh-floatingparent"><div></div></div>'),placeholder:null}},this.dom.header.host=this.dom.thead.parent(),this.dom.footer.host=this.dom.tfoot.parent(),(e=t.settings()[0])._fixedHeader)throw"FixedHeader already initialised on table "+e.nTable.id;(e._fixedHeader=this)._constructor()}var d=b.fn.dataTable,o=0;return b.extend(s.prototype,{destroy:function(){var t=this.dom;this.s.dt.off(".dtfc"),b(m).off(this.s.namespace),t.header.rightBlocker&&t.header.rightBlocker.remove(),t.header.leftBlocker&&t.header.leftBlocker.remove(),t.footer.rightBlocker&&t.footer.rightBlocker.remove(),t.footer.leftBlocker&&t.footer.leftBlocker.remove(),this.c.header&&this._modeChange("in-place","header",!0),this.c.footer&&t.tfoot.length&&this._modeChange("in-place","footer",!0)},enable:function(t,e,o){this.s.enable=t,this.s.enableType=o,!e&&void 0!==e||(this._positions(),this._scroll(!0))},enabled:function(){return this.s.enable},headerOffset:function(t){return void 0!==t&&(this.c.headerOffset=t,this.update()),this.c.headerOffset},footerOffset:function(t){return void 0!==t&&(this.c.footerOffset=t,this.update()),this.c.footerOffset},update:function(t){var e=this.s.dt.table().node();(this.s.enable||this.s.autoDisable)&&(b(e).is(":visible")?(this.s.autoDisable=!1,this.enable(!0,!1)):(this.s.autoDisable=!0,this.enable(!1,!1)),0!==b(e).children("thead").length)&&(this._positions(),this._scroll(void 0===t||t),this._widths(this.dom.header),this._widths(this.dom.footer))},_constructor:function(){var o=this,i=this.s.dt,t=(b(m).on("scroll"+this.s.namespace,function(){o._scroll()}).on("resize"+this.s.namespace,d.util.throttle(function(){o.s.position.windowHeight=b(m).height(),o.update()},50)),b(".fh-fixedHeader")),t=(!this.c.headerOffset&&t.length&&(this.c.headerOffset=t.outerHeight()),b(".fh-fixedFooter"));!this.c.footerOffset&&t.length&&(this.c.footerOffset=t.outerHeight()),i.on("column-reorder.dt.dtfc column-visibility.dt.dtfc column-sizing.dt.dtfc responsive-display.dt.dtfc",function(t,e){o.update()}).on("draw.dt.dtfc",function(t,e){o.update(e!==i.settings()[0])}),i.on("destroy.dtfc",function(){o.destroy()}),this._positions(),this._scroll()},_clone:function(t,e){var o,i,s=this,d=this.s.dt,n=this.dom[t],r="header"===t?this.dom.thead:this.dom.tfoot;"footer"===t&&this._scrollEnabled()||(!e&&n.floating?n.floating.removeClass("fixedHeader-floating fixedHeader-locked"):(n.floating&&(null!==n.placeholder&&n.placeholder.remove(),n.floating.children().detach(),n.floating.remove()),e=b(d.table().node()),o=b(e.parent()),i=this._scrollEnabled(),n.floating=b(d.table().node().cloneNode(!1)).attr("aria-hidden","true").css({top:0,left:0}).removeAttr("id"),n.floatingParent.css({width:o[0].offsetWidth,overflow:"hidden",height:"fit-content",position:"fixed",left:i?e.offset().left+o.scrollLeft():0}).css("header"===t?{top:this.c.headerOffset,bottom:""}:{top:"",bottom:this.c.footerOffset}).addClass("footer"===t?"dtfh-floatingparent-foot":"dtfh-floatingparent-head").appendTo("body").children().eq(0).append(n.floating),this._stickyPosition(n.floating,"-"),(i=function(){var t=o.scrollLeft();s.s.scrollLeft={footer:t,header:t},n.floatingParent.scrollLeft(s.s.scrollLeft.header)})(),o.off("scroll.dtfh").on("scroll.dtfh",i),n.floatingParent.children().css({width:"fit-content",paddingRight:s.s.dt.settings()[0].oBrowser.barWidth}),(e=b("footer"===t?"div.dtfc-bottom-blocker":"div.dtfc-top-blocker",d.table().container())).length&&e.clone().appendTo(n.floatingParent).css({position:"fixed",right:e.width()}),n.placeholder=r.clone(!1),n.placeholder.find("*[id]").removeAttr("id"),n.host.prepend(n.placeholder),n.floating.append(r),this._widths(n)))},_stickyPosition:function(t,e){var i;this._scrollEnabled()&&(i="rtl"===b(this.s.dt.table().node()).css("direction"),t.find("th").each(function(){var t,e,o;"sticky"===b(this).css("position")&&(t=b(this).css("right"),e=b(this).css("left"),"auto"===t||i?"auto"!==e&&i&&(o=+e.replace(/px/g,""),b(this).css("left",0<o?o:0)):(o=+t.replace(/px/g,""),b(this).css("right",0<o?o:0)))}))},_horizontal:function(t,e){var o,i=this.dom[t],s=this.s.scrollLeft;i.floating&&s[t]!==e&&(this._scrollEnabled()&&(o=b(b(this.s.dt.table().node()).parent()).scrollLeft(),i.floating.scrollLeft(o),i.floatingParent.scrollLeft(o)),s[t]=e)},_modeChange:function(t,e,o){var i,s,d,n,r,a,f,h=this.dom[e],l=this.s.position,c=this._scrollEnabled();"footer"===e&&c||(i=function(t){h.floating[0].style.setProperty("width",t+"px","important"),c||h.floatingParent[0].style.setProperty("width",t+"px","important")},n=this.dom["footer"===e?"tfoot":"thead"],s=b.contains(n[0],v.activeElement)?v.activeElement:null,r=b(b(this.s.dt.table().node()).parent()),"in-place"===t?(h.placeholder&&(h.placeholder.remove(),h.placeholder=null),"header"===e?h.host.prepend(n):h.host.append(n),h.floating&&(h.floating.remove(),h.floating=null,this._stickyPosition(h.host,"+")),h.floatingParent&&(h.floatingParent.find("div.dtfc-top-blocker").remove(),h.floatingParent.remove()),b(b(h.host.parent()).parent()).scrollLeft(r.scrollLeft())):"in"===t?(this._clone(e,o),n=r.offset(),f=(d=b(v).scrollTop())+b(m).height(),a=c?n.top:l.tbodyTop,n=c?n.top+r.outerHeight():l.tfootTop,r="footer"===e?f<a?l.tfootHeight:a+l.tfootHeight-f:d+this.c.headerOffset+l.theadHeight-n,a="header"===e?"top":"bottom",f=this.c[e+"Offset"]-(0<r?r:0),h.floating.addClass("fixedHeader-floating"),h.floatingParent.css(a,f).css({left:l.left,"z-index":3}),i(l.width),"footer"===e&&h.floating.css("top","")):"below"===t?(this._clone(e,o),h.floating.addClass("fixedHeader-locked"),h.floatingParent.css({position:"absolute",top:l.tfootTop-l.theadHeight,left:l.left+"px"}),i(l.width)):"above"===t&&(this._clone(e,o),h.floating.addClass("fixedHeader-locked"),h.floatingParent.css({position:"absolute",top:l.tbodyTop,left:l.left+"px"}),i(l.width)),s&&s!==v.activeElement&&setTimeout(function(){s.focus()},10),this.s.scrollLeft.header=-1,this.s.scrollLeft.footer=-1,this.s[e+"Mode"]=t)},_positions:function(){var t=this.s.dt,e=t.table(),o=this.s.position,i=this.dom,e=b(e.node()),s=this._scrollEnabled(),d=b(t.table().header()),t=b(t.table().footer()),i=i.tbody,n=e.parent();o.visible=e.is(":visible"),o.width=e.outerWidth(),o.left=e.offset().left,o.theadTop=d.offset().top,o.tbodyTop=(s?n:i).offset().top,o.tbodyHeight=(s?n:i).outerHeight(),o.theadHeight=d.outerHeight(),o.theadBottom=o.theadTop+o.theadHeight,o.tfootTop=o.tbodyTop+o.tbodyHeight,t.length?(o.tfootBottom=o.tfootTop+t.outerHeight(),o.tfootHeight=t.outerHeight()):(o.tfootBottom=o.tfootTop,o.tfootHeight=0)},_scroll:function(t){var e,o,i,s,d,n,r,a,f,h,l,c,p,g,u;this.s.dt.settings()[0].bDestroying||(e=this._scrollEnabled(),i=(o=b(this.s.dt.table().node()).parent()).offset(),h=o.outerHeight(),s=b(v).scrollLeft(),d=b(v).scrollTop(),n=b(m).height()+d,l=this.s.position,c=e?i.top:l.tbodyTop,a=(e?i:l).left,h=e?i.top+h:l.tfootTop,f=e?o.outerWidth():l.tbodyWidth,this.c.header&&(!this.s.enable||!l.visible||d+this.c.headerOffset+l.theadHeight<=c?r="in-place":d+this.c.headerOffset+l.theadHeight>c&&d+this.c.headerOffset+l.theadHeight<h?(r="in",d+this.c.headerOffset+l.theadHeight>h||void 0===this.dom.header.floatingParent?t=!0:this.dom.header.floatingParent.css({top:this.c.headerOffset,position:"fixed"}).children().eq(0).append(this.dom.header.floating)):r="below",!t&&r===this.s.headerMode||this._modeChange(r,"header",t),this._horizontal("header",s)),p={offset:{top:0,left:0},height:0},g={offset:{top:0,left:0},height:0},this.c.footer&&this.dom.tfoot.length&&this.dom.tfoot.find("th, td").length&&(!this.s.enable||!l.visible||l.tfootBottom+this.c.footerOffset<=n?u="in-place":h+l.tfootHeight+this.c.footerOffset>n&&c+this.c.footerOffset<n?(u="in",t=!0):u="above",!t&&u===this.s.footerMode||this._modeChange(u,"footer",t),this._horizontal("footer",s),h=function(t){return{offset:t.offset(),height:t.outerHeight()}},p=this.dom.header.floating?h(this.dom.header.floating):h(this.dom.thead),g=this.dom.footer.floating?h(this.dom.footer.floating):h(this.dom.tfoot),e)&&g.offset.top>d&&(c=n+((l=d-i.top)>-p.height?l:0)-(p.offset.top+(l<-p.height?p.height:0)+g.height),o.outerHeight(c=c<0?0:c),Math.round(o.outerHeight())>=Math.round(c)?b(this.dom.tfoot.parent()).addClass("fixedHeader-floating"):b(this.dom.tfoot.parent()).removeClass("fixedHeader-floating")),this.dom.header.floating&&this.dom.header.floatingParent.css("left",a-s),this.dom.footer.floating&&this.dom.footer.floatingParent.css("left",a-s),void 0!==this.s.dt.settings()[0]._fixedColumns&&(this.dom.header.rightBlocker=(u=function(t,e,o){var i;return null!==(o=void 0===o?0===(i=b("div.dtfc-"+t+"-"+e+"-blocker")).length?null:i.clone().css("z-index",1):o)&&("in"===r||"below"===r?o.appendTo("body").css({top:("top"===e?p:g).offset.top,left:"right"===t?a+f-o.width():a}):o.detach()),o})("right","top",this.dom.header.rightBlocker),this.dom.header.leftBlocker=u("left","top",this.dom.header.leftBlocker),this.dom.footer.rightBlocker=u("right","bottom",this.dom.footer.rightBlocker),this.dom.footer.leftBlocker=u("left","bottom",this.dom.footer.leftBlocker)))},_scrollEnabled:function(){var t=this.s.dt.settings()[0].oScroll;return""!==t.sY||""!==t.sX},_widths:function(t){if(t&&t.placeholder)for(var e=b(this.s.dt.table().node()),o=b(e.parent()),i=(t.floatingParent.css("width",o[0].offsetWidth),t.floating.css("width",e[0].offsetWidth),b("colgroup",t.floating).remove(),t.placeholder.parent().find("colgroup").clone().appendTo(t.floating).find("col")),s=this.s.dt.columns(":visible").widths(),d=0;d<s.length;d++)i.eq(d).css("width",s[d])}}),s.version="4.0.1",s.defaults={header:!0,footer:!1,headerOffset:0,footerOffset:0},b.fn.dataTable.FixedHeader=s,b.fn.DataTable.FixedHeader=s,b(v).on("init.dt.dtfh",function(t,e,o){var i;"dt"===t.namespace&&(t=e.oInit.fixedHeader,i=d.defaults.fixedHeader,t||i)&&!e._fixedHeader&&(i=b.extend({},i,t),!1!==t)&&new s(e,i)}),d.Api.register("fixedHeader()",function(){}),d.Api.register("fixedHeader.adjust()",function(){return this.iterator("table",function(t){t=t._fixedHeader;t&&t.update()})}),d.Api.register("fixedHeader.enable()",function(e){return this.iterator("table",function(t){t=t._fixedHeader;e=void 0===e||e,t&&e!==t.enabled()&&t.enable(e)})}),d.Api.register("fixedHeader.enabled()",function(){if(this.context.length){var t=this.context[0]._fixedHeader;if(t)return t.enabled()}return!1}),d.Api.register("fixedHeader.disable()",function(){return this.iterator("table",function(t){t=t._fixedHeader;t&&t.enabled()&&t.enable(!1)})}),b.each(["header","footer"],function(t,o){d.Api.register("fixedHeader."+o+"Offset()",function(e){var t=this.context;return void 0===e?t.length&&t[0]._fixedHeader?t[0]._fixedHeader[o+"Offset"]():void 0:this.iterator("table",function(t){t=t._fixedHeader;t&&t[o+"Offset"](e)})})}),d});

/*! Responsive 3.0.2
 * © SpryMedia Ltd - datatables.net/license
 */
!function(n){var i,r;"function"==typeof define&&define.amd?define(["jquery","datatables.net"],function(e){return n(e,window,document)}):"object"==typeof exports?(i=require("jquery"),r=function(e,t){t.fn.dataTable||require("datatables.net")(e,t)},"undefined"==typeof window?module.exports=function(e,t){return e=e||window,t=t||i(e),r(e,t),n(t,e,e.document)}:(r(window,i),module.exports=n(i,window,window.document))):n(jQuery,window,document)}(function(b,y,d){"use strict";function a(e,t){if(!i.versionCheck||!i.versionCheck("2"))throw"DataTables Responsive requires DataTables 2 or newer";this.s={childNodeStore:{},columns:[],current:[],dt:new i.Api(e)},this.s.dt.settings()[0].responsive||(t&&"string"==typeof t.details?t.details={type:t.details}:t&&!1===t.details?t.details={type:!1}:t&&!0===t.details&&(t.details={type:"inline"}),this.c=b.extend(!0,{},a.defaults,i.defaults.responsive,t),(e.responsive=this)._constructor())}var i=b.fn.dataTable,e=(b.extend(a.prototype,{_constructor:function(){var o=this,r=this.s.dt,t=b(y).innerWidth(),e=(r.settings()[0]._responsive=this,b(y).on("orientationchange.dtr",i.util.throttle(function(){var e=b(y).innerWidth();e!==t&&(o._resize(),t=e)})),r.on("row-created.dtr",function(e,t,n,i){-1!==b.inArray(!1,o.s.current)&&b(">td, >th",t).each(function(e){e=r.column.index("toData",e);!1===o.s.current[e]&&b(this).css("display","none").addClass("dtr-hidden")})}),r.on("destroy.dtr",function(){r.off(".dtr"),b(r.table().body()).off(".dtr"),b(y).off("resize.dtr orientationchange.dtr"),r.cells(".dtr-control").nodes().to$().removeClass("dtr-control"),b(r.table().node()).removeClass("dtr-inline collapsed"),b.each(o.s.current,function(e,t){!1===t&&o._setColumnVis(e,!0)})}),this.c.breakpoints.sort(function(e,t){return e.width<t.width?1:e.width>t.width?-1:0}),this._classLogic(),this._resizeAuto(),this.c.details);!1!==e.type&&(o._detailsInit(),r.on("column-visibility.dtr",function(){o._timer&&clearTimeout(o._timer),o._timer=setTimeout(function(){o._timer=null,o._classLogic(),o._resizeAuto(),o._resize(!0),o._redrawChildren()},100)}),r.on("draw.dtr",function(){o._redrawChildren()}),b(r.table().node()).addClass("dtr-"+e.type)),r.on("column-reorder.dtr",function(e,t,n){o._classLogic(),o._resizeAuto(),o._resize(!0)}),r.on("column-sizing.dtr",function(){o._resizeAuto(),o._resize()}),r.on("column-calc.dt",function(e,t){for(var n=o.s.current,i=0;i<n.length;i++){var r=t.visible.indexOf(i);!1===n[i]&&0<=r&&t.visible.splice(r,1)}}),r.on("preXhr.dtr",function(){var e=[];r.rows().every(function(){this.child.isShown()&&e.push(this.id(!0))}),r.one("draw.dtr",function(){o._resizeAuto(),o._resize(),r.rows(e).every(function(){o._detailsDisplay(this,!1)})})}),r.on("draw.dtr",function(){o._controlClass()}).on("init.dtr",function(e,t,n){"dt"===e.namespace&&(o._resizeAuto(),o._resize())}),this._resize()},_colGroupAttach:function(e,t,n){var i=null;if(t[n].get(0).parentNode!==e[0]){for(var r=n+1;r<t.length;r++)if(e[0]===t[r].get(0).parentNode){i=r;break}null!==i?t[n].insertBefore(t[i][0]):e.append(t[n])}},_childNodes:function(e,t,n){var i=t+"-"+n;if(this.s.childNodeStore[i])return this.s.childNodeStore[i];for(var r=[],o=e.cell(t,n).node().childNodes,s=0,d=o.length;s<d;s++)r.push(o[s]);return this.s.childNodeStore[i]=r},_childNodesRestore:function(e,t,n){var i=t+"-"+n;if(this.s.childNodeStore[i]){var r=e.cell(t,n).node(),e=this.s.childNodeStore[i];if(0<e.length){for(var o=e[0].parentNode.childNodes,s=[],d=0,a=o.length;d<a;d++)s.push(o[d]);for(var l=0,c=s.length;l<c;l++)r.appendChild(s[l])}this.s.childNodeStore[i]=void 0}},_columnsVisiblity:function(n){for(var i=this.s.dt,e=this.s.columns,t=e.map(function(e,t){return{columnIdx:t,priority:e.priority}}).sort(function(e,t){return e.priority!==t.priority?e.priority-t.priority:e.columnIdx-t.columnIdx}),r=b.map(e,function(e,t){return!1===i.column(t).visible()?"not-visible":(!e.auto||null!==e.minWidth)&&(!0===e.auto?"-":-1!==b.inArray(n,e.includeIn))}),o=0,s=0,d=r.length;s<d;s++)!0===r[s]&&(o+=e[s].minWidth);var a=i.settings()[0].oScroll,a=a.sY||a.sX?a.iBarWidth:0,l=i.table().container().offsetWidth-a-o;for(s=0,d=r.length;s<d;s++)e[s].control&&(l-=e[s].minWidth);var c=!1;for(s=0,d=t.length;s<d;s++){var u=t[s].columnIdx;"-"===r[u]&&!e[u].control&&e[u].minWidth&&(c||l-e[u].minWidth<0?r[u]=!(c=!0):r[u]=!0,l-=e[u].minWidth)}var h=!1;for(s=0,d=e.length;s<d;s++)if(!e[s].control&&!e[s].never&&!1===r[s]){h=!0;break}for(s=0,d=e.length;s<d;s++)e[s].control&&(r[s]=h),"not-visible"===r[s]&&(r[s]=!1);return-1===b.inArray(!0,r)&&(r[0]=!0),r},_classLogic:function(){function d(e,t,n,i){var r,o,s;if(n){if("max-"===n)for(r=a._find(t).width,o=0,s=l.length;o<s;o++)l[o].width<=r&&u(e,l[o].name);else if("min-"===n)for(r=a._find(t).width,o=0,s=l.length;o<s;o++)l[o].width>=r&&u(e,l[o].name);else if("not-"===n)for(o=0,s=l.length;o<s;o++)-1===l[o].name.indexOf(i)&&u(e,l[o].name)}else c[e].includeIn.push(t)}var a=this,l=this.c.breakpoints,c=this.s.dt.columns().eq(0).map(function(e){var e=this.column(e),t=e.header().className,n=e.init().responsivePriority,e=e.header().getAttribute("data-priority");return void 0===n&&(n=null==e?1e4:+e),{className:t,includeIn:[],auto:!1,control:!1,never:!!t.match(/\b(dtr\-)?never\b/),priority:n}}),u=function(e,t){e=c[e].includeIn;-1===b.inArray(t,e)&&e.push(t)};c.each(function(e,r){for(var t=e.className.split(" "),o=!1,n=0,i=t.length;n<i;n++){var s=t[n].trim();if("all"===s||"dtr-all"===s)return o=!0,void(e.includeIn=b.map(l,function(e){return e.name}));if("none"===s||"dtr-none"===s||e.never)return void(o=!0);if("control"===s||"dtr-control"===s)return o=!0,void(e.control=!0);b.each(l,function(e,t){var n=t.name.split("-"),i=new RegExp("(min\\-|max\\-|not\\-)?("+n[0]+")(\\-[_a-zA-Z0-9])?"),i=s.match(i);i&&(o=!0,i[2]===n[0]&&i[3]==="-"+n[1]?d(r,t.name,i[1],i[2]+i[3]):i[2]!==n[0]||i[3]||d(r,t.name,i[1],i[2]))})}o||(e.auto=!0)}),this.s.columns=c},_controlClass:function(){var e,t,n;"inline"===this.c.details.type&&(e=this.s.dt,t=this.s.current,n=b.inArray(!0,t),e.cells(null,function(e){return e!==n},{page:"current"}).nodes().to$().filter(".dtr-control").removeClass("dtr-control"),e.cells(null,n,{page:"current"}).nodes().to$().addClass("dtr-control"))},_detailsDisplay:function(t,n){function e(e){b(t.node()).toggleClass("dtr-expanded",!1!==e),b(o.table().node()).triggerHandler("responsive-display.dt",[o,t,e,n])}var i,r=this,o=this.s.dt,s=this.c.details;s&&!1!==s.type&&(i="string"==typeof s.renderer?a.renderer[s.renderer]():s.renderer,"boolean"==typeof(s=s.display(t,n,function(){return i.call(r,o,t[0][0],r._detailsObj(t[0]))},function(){e(!1)})))&&e(s)},_detailsInit:function(){var n=this,i=this.s.dt,e=this.c.details,r=("inline"===e.type&&(e.target="td.dtr-control, th.dtr-control"),i.on("draw.dtr",function(){n._tabIndexes()}),n._tabIndexes(),b(i.table().body()).on("keyup.dtr","td, th",function(e){13===e.keyCode&&b(this).data("dtr-keyboard")&&b(this).click()}),e.target),e="string"==typeof r?r:"td, th";void 0===r&&null===r||b(i.table().body()).on("click.dtr mousedown.dtr mouseup.dtr",e,function(e){if(b(i.table().node()).hasClass("collapsed")&&-1!==b.inArray(b(this).closest("tr").get(0),i.rows().nodes().toArray())){if("number"==typeof r){var t=r<0?i.columns().eq(0).length+r:r;if(i.cell(this).index().column!==t)return}t=i.row(b(this).closest("tr"));"click"===e.type?n._detailsDisplay(t,!1):"mousedown"===e.type?b(this).css("outline","none"):"mouseup"===e.type&&b(this).trigger("blur").css("outline","")}})},_detailsObj:function(n){var i=this,r=this.s.dt;return b.map(this.s.columns,function(e,t){if(!e.never&&!e.control)return{className:r.settings()[0].aoColumns[t].sClass,columnIndex:t,data:r.cell(n,t).render(i.c.orthogonal),hidden:r.column(t).visible()&&!i.s.current[t],rowIndex:n,title:r.column(t).title()}})},_find:function(e){for(var t=this.c.breakpoints,n=0,i=t.length;n<i;n++)if(t[n].name===e)return t[n]},_redrawChildren:function(){var n=this,i=this.s.dt;i.rows({page:"current"}).iterator("row",function(e,t){n._detailsDisplay(i.row(t),!0)})},_resize:function(n){for(var e,i=this,r=this.s.dt,t=b(y).innerWidth(),o=this.c.breakpoints,s=o[0].name,d=this.s.columns,a=this.s.current.slice(),l=o.length-1;0<=l;l--)if(t<=o[l].width){s=o[l].name;break}var c=this._columnsVisiblity(s),u=(this.s.current=c,!1);for(l=0,e=d.length;l<e;l++)if(!1===c[l]&&!d[l].never&&!d[l].control&&!1==!r.column(l).visible()){u=!0;break}b(r.table().node()).toggleClass("collapsed",u);var h=!1,p=0,f=r.settings()[0],m=b(r.table().node()).children("colgroup"),v=f.aoColumns.map(function(e){return e.colEl});r.columns().eq(0).each(function(e,t){r.column(e).visible()&&(!0===c[t]&&p++,!n&&c[t]===a[t]||(h=!0,i._setColumnVis(e,c[t])),c[t]?i._colGroupAttach(m,v,t):v[t].detach())}),h&&(r.columns.adjust(),this._redrawChildren(),b(r.table().node()).trigger("responsive-resize.dt",[r,this._responsiveOnlyHidden()]),0===r.page.info().recordsDisplay)&&b("td",r.table().body()).eq(0).attr("colspan",p),i._controlClass()},_resizeAuto:function(){var t=this.s.dt,n=this.s.columns,r=this,o=t.columns().indexes().filter(function(e){return t.column(e).visible()});if(this.c.auto&&-1!==b.inArray(!0,b.map(n,function(e){return e.auto}))){for(var e=t.table().node().cloneNode(!1),i=b(t.table().header().cloneNode(!1)).appendTo(e),s=b(t.table().footer().cloneNode(!1)).appendTo(e),d=b(t.table().body()).clone(!1,!1).empty().appendTo(e),a=(e.style.width="auto",t.table().header.structure(o).forEach(e=>{e=e.filter(function(e){return!!e}).map(function(e){return b(e.cell).clone(!1).css("display","table-cell").css("width","auto").css("min-width",0)});b("<tr/>").append(e).appendTo(i)}),b("<tr/>").appendTo(d)),l=0;l<o.count();l++)a.append("<td/>");t.rows({page:"current"}).every(function(n){var i,e=this.node();e&&(i=e.cloneNode(!1),t.cells(n,o).every(function(e,t){t=r.s.childNodeStore[n+"-"+t];(t?b(this.node().cloneNode(!1)).append(b(t).clone()):b(this.node()).clone(!1)).appendTo(i)}),d.append(i))}),d.find("th, td").css("display",""),t.table().footer.structure(o).forEach(e=>{e=e.filter(function(e){return!!e}).map(function(e){return b(e.cell).clone(!1).css("display","table-cell").css("width","auto").css("min-width",0)});b("<tr/>").append(e).appendTo(s)}),"inline"===this.c.details.type&&b(e).addClass("dtr-inline collapsed"),b(e).find("[name]").removeAttr("name"),b(e).css("position","relative");e=b("<div/>").css({width:1,height:1,overflow:"hidden",clear:"both"}).append(e);e.insertBefore(t.table().node()),a.children().each(function(e){e=t.column.index("fromVisible",e);n[e].minWidth=this.offsetWidth||0}),e.remove()}},_responsiveOnlyHidden:function(){var n=this.s.dt;return b.map(this.s.current,function(e,t){return!1===n.column(t).visible()||e})},_setColumnVis:function(e,t){var n=this,i=this.s.dt,r=t?"":"none";this._setHeaderVis(e,t,i.table().header.structure()),this._setHeaderVis(e,t,i.table().footer.structure()),i.column(e).nodes().to$().css("display",r).toggleClass("dtr-hidden",!t),b.isEmptyObject(this.s.childNodeStore)||i.cells(null,e).indexes().each(function(e){n._childNodesRestore(i,e.row,e.column)})},_setHeaderVis:function(n,i,e){var r=this,o=i?"":"none";e.forEach(function(e){if(e[n])b(e[n].cell).css("display",o).toggleClass("dtr-hidden",!i);else for(var t=n;0<=t;){if(e[t]){e[t].cell.colSpan=r._colspan(e,t);break}t--}})},_colspan:function(e,t){for(var n=1,i=t+1;i<e.length;i++)if(null===e[i]&&this.s.current[i])n++;else if(e[i])break;return n},_tabIndexes:function(){var e=this.s.dt,t=e.cells({page:"current"}).nodes().to$(),n=e.settings()[0],i=this.c.details.target;t.filter("[data-dtr-keyboard]").removeData("[data-dtr-keyboard]"),("number"==typeof i?e.cells(null,i,{page:"current"}).nodes().to$():b(i="td:first-child, th:first-child"===i?">td:first-child, >th:first-child":i,e.rows({page:"current"}).nodes())).attr("tabIndex",n.iTabIndex).data("dtr-keyboard",1)}}),a.defaults={breakpoints:a.breakpoints=[{name:"desktop",width:1/0},{name:"tablet-l",width:1024},{name:"tablet-p",width:768},{name:"mobile-l",width:480},{name:"mobile-p",width:320}],auto:!0,details:{display:(a.display={childRow:function(e,t,n){var i=b(e.node());return t?i.hasClass("dtr-expanded")?(e.child(n(),"child").show(),!0):void 0:i.hasClass("dtr-expanded")?(e.child(!1),!1):!1!==(t=n())&&(e.child(t,"child").show(),!0)},childRowImmediate:function(e,t,n){var i=b(e.node());return!t&&i.hasClass("dtr-expanded")||!e.responsive.hasHidden()?(e.child(!1),!1):!1!==(t=n())&&(e.child(t,"child").show(),!0)},modal:function(s){return function(e,t,n,i){n=n();if(!1===n)return!1;if(t){if(!(o=b("div.dtr-modal-content")).length||e.index()!==o.data("dtr-row-idx"))return null;o.empty().append(n)}else{var r=function(){o.remove(),b(d).off("keypress.dtr"),b(e.node()).removeClass("dtr-expanded"),i()},o=b('<div class="dtr-modal"/>').append(b('<div class="dtr-modal-display"/>').append(b('<div class="dtr-modal-content"/>').data("dtr-row-idx",e.index()).append(n)).append(b('<div class="dtr-modal-close">&times;</div>').click(function(){r()}))).append(b('<div class="dtr-modal-background"/>').click(function(){r()})).appendTo("body");b(e.node()).addClass("dtr-expanded"),b(d).on("keyup.dtr",function(e){27===e.keyCode&&(e.stopPropagation(),r())})}return s&&s.header&&b("div.dtr-modal-content").prepend("<h2>"+s.header(e)+"</h2>"),!0}}}).childRow,renderer:(a.renderer={listHiddenNodes:function(){return function(i,e,t){var r=this,o=b('<ul data-dtr-index="'+e+'" class="dtr-details"/>'),s=!1;return b.each(t,function(e,t){var n;t.hidden&&(n=t.className?'class="'+t.className+'"':"",b("<li "+n+' data-dtr-index="'+t.columnIndex+'" data-dt-row="'+t.rowIndex+'" data-dt-column="'+t.columnIndex+'"><span class="dtr-title">'+t.title+"</span> </li>").append(b('<span class="dtr-data"/>').append(r._childNodes(i,t.rowIndex,t.columnIndex))).appendTo(o),s=!0)}),!!s&&o}},listHidden:function(){return function(e,t,n){n=b.map(n,function(e){var t=e.className?'class="'+e.className+'"':"";return e.hidden?"<li "+t+' data-dtr-index="'+e.columnIndex+'" data-dt-row="'+e.rowIndex+'" data-dt-column="'+e.columnIndex+'"><span class="dtr-title">'+e.title+'</span> <span class="dtr-data">'+e.data+"</span></li>":""}).join("");return!!n&&b('<ul data-dtr-index="'+t+'" class="dtr-details"/>').append(n)}},tableAll:function(i){return i=b.extend({tableClass:""},i),function(e,t,n){n=b.map(n,function(e){return"<tr "+(e.className?'class="'+e.className+'"':"")+' data-dt-row="'+e.rowIndex+'" data-dt-column="'+e.columnIndex+'"><td>'+e.title+":</td> <td>"+e.data+"</td></tr>"}).join("");return b('<table class="'+i.tableClass+' dtr-details" width="100%"/>').append(n)}}}).listHidden(),target:0,type:"inline"},orthogonal:"display"},b.fn.dataTable.Api);return e.register("responsive()",function(){return this}),e.register("responsive.index()",function(e){return{column:(e=b(e)).data("dtr-index"),row:e.parent().data("dtr-index")}}),e.register("responsive.rebuild()",function(){return this.iterator("table",function(e){e._responsive&&e._responsive._classLogic()})}),e.register("responsive.recalc()",function(){return this.iterator("table",function(e){e._responsive&&(e._responsive._resizeAuto(),e._responsive._resize())})}),e.register("responsive.hasHidden()",function(){var e=this.context[0];return!!e._responsive&&-1!==b.inArray(!1,e._responsive._responsiveOnlyHidden())}),e.registerPlural("columns().responsiveHidden()","column().responsiveHidden()",function(){return this.iterator("column",function(e,t){return!!e._responsive&&e._responsive._responsiveOnlyHidden()[t]},1)}),a.version="3.0.2",b.fn.dataTable.Responsive=a,b.fn.DataTable.Responsive=a,b(d).on("preInit.dt.dtr",function(e,t,n){"dt"===e.namespace&&(b(t.nTable).hasClass("responsive")||b(t.nTable).hasClass("dt-responsive")||t.oInit.responsive||i.defaults.responsive)&&!1!==(e=t.oInit.responsive)&&new a(t,b.isPlainObject(e)?e:{})}),i});

/*! Bootstrap 5 integration for DataTables' Responsive
 * © SpryMedia Ltd - datatables.net/license
 */

(function( factory ){
	if ( typeof define === 'function' && define.amd ) {
		// AMD
		define( ['jquery', 'datatables.net-bs5', 'datatables.net-responsive'], function ( $ ) {
			return factory( $, window, document );
		} );
	}
	else if ( typeof exports === 'object' ) {
		// CommonJS
		var jq = require('jquery');
		var cjsRequires = function (root, $) {
			if ( ! $.fn.dataTable ) {
				require('datatables.net-bs5')(root, $);
			}

			if ( ! $.fn.dataTable.Responsive ) {
				require('datatables.net-responsive')(root, $);
			}
		};

		if (typeof window === 'undefined') {
			module.exports = function (root, $) {
				if ( ! root ) {
					// CommonJS environments without a window global must pass a
					// root. This will give an error otherwise
					root = window;
				}

				if ( ! $ ) {
					$ = jq( root );
				}

				cjsRequires( root, $ );
				return factory( $, root, root.document );
			};
		}
		else {
			cjsRequires( window, jq );
			module.exports = factory( jq, window, window.document );
		}
	}
	else {
		// Browser
		factory( jQuery, window, document );
	}
}(function( $, window, document ) {
'use strict';
var DataTable = $.fn.dataTable;



var _display = DataTable.Responsive.display;
var _original = _display.modal;
var _modal = $(
	'<div class="modal fade dtr-bs-modal" role="dialog">' +
		'<div class="modal-dialog" role="document">' +
		'<div class="modal-content">' +
		'<div class="modal-header">' +
		'<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>' +
		'</div>' +
		'<div class="modal-body"/>' +
		'</div>' +
		'</div>' +
		'</div>'
);
var modal;

// Note this could be undefined at the time of initialisation - the
// DataTable.Responsive.bootstrap function can be used to set a different
// bootstrap object
var _bs = window.bootstrap;

DataTable.Responsive.bootstrap = function (bs) {
	_bs = bs;
};

_display.modal = function (options) {
	if (!modal && _bs.Modal) {
		modal = new _bs.Modal(_modal[0]);
	}

	return function (row, update, render, closeCallback) {
		if (! modal) {
			return _original(row, update, render, closeCallback);
		}
		else {
			var rendered = render();

			if (rendered === false) {
				return false;
			}

			if (!update) {
				if (options && options.header) {
					var header = _modal.find('div.modal-header');
					var button = header.find('button').detach();

					header
						.empty()
						.append('<h4 class="modal-title">' + options.header(row) + '</h4>')
						.append(button);
				}

				_modal.find('div.modal-body').empty().append(rendered);

				_modal
					.data('dtr-row-idx', row.index())
					.one('hidden.bs.modal', closeCallback)
					.appendTo('body');

				modal.show();
			}
			else {
				if ($.contains(document, _modal[0]) && row.index() === _modal.data('dtr-row-idx')) {
					_modal.find('div.modal-body').empty().append(rendered);
				}
				else {
					// Modal not shown for this row - do nothing
					return null;
				}
			}

			return true;
		}
	};
};


return DataTable;
}));


/*! RowReorder 1.5.0
 * © SpryMedia Ltd - datatables.net/license
 */
!function(o){var r,n;"function"==typeof define&&define.amd?define(["jquery","datatables.net"],function(t){return o(t,window,document)}):"object"==typeof exports?(r=require("jquery"),n=function(t,e){e.fn.dataTable||require("datatables.net")(t,e)},"undefined"==typeof window?module.exports=function(t,e){return t=t||window,e=e||r(t),n(t,e),o(e,t,t.document)}:(n(window,r),module.exports=o(r,window,window.document))):o(jQuery,window,document)}(function(v,d,l){"use strict";function s(t,e){if(!i.versionCheck||!i.versionCheck("1.11"))throw"DataTables RowReorder requires DataTables 1.11 or newer";if(this.c=v.extend(!0,{},i.defaults.rowReorder,s.defaults,e),this.s={bodyTop:null,dt:new i.Api(t),getDataFn:i.util.get(this.c.dataSrc),middles:null,scroll:{},scrollInterval:null,setDataFn:i.util.set(this.c.dataSrc),start:{top:0,left:0,offsetTop:0,offsetLeft:0,nodes:[],rowIndex:0},windowHeight:0,documentOuterHeight:0,domCloneOuterHeight:0,dropAllowed:!0},this.dom={clone:null,cloneParent:null,dtScroll:v("div.dataTables_scrollBody, div.dt-scroll-body",this.s.dt.table().container())},e=this.s.dt.settings()[0],t=e.rowreorder)return t;this.dom.dtScroll.length||(this.dom.dtScroll=v(this.s.dt.table().container(),"tbody")),(e.rowreorder=this)._constructor()}var i=v.fn.dataTable,t=(v.extend(s.prototype,{_constructor:function(){var r=this,n=this.s.dt,t=v(n.table().node());"static"===t.css("position")&&t.css("position","relative"),v(n.table().container()).on("mousedown.rowReorder touchstart.rowReorder",this.c.selector,function(t){var e,o;if(r.c.enable)return!!v(t.target).is(r.c.excludedChildren)||(e=v(this).closest("tr"),(o=n.row(e)).any()?(r._emitEvent("pre-row-reorder",{node:o.node(),index:o.index()}),r._mouseDown(t,e),!1):void 0)}),n.on("destroy.rowReorder",function(){v(n.table().container()).off(".rowReorder"),n.off(".rowReorder")}),this._keyup=this._keyup.bind(this)},_cachePositions:function(){var t=this.s.dt,r=v(t.table().node()).find("thead").outerHeight(),e=v.unique(t.rows({page:"current"}).nodes().toArray()),e=v.map(e,function(t,e){var o=v(t).position().top-r;return(o+o+v(t).outerHeight())/2});this.s.middles=e,this.s.bodyTop=v(t.table().body()).offset().top,this.s.windowHeight=v(d).height(),this.s.documentOuterHeight=v(l).outerHeight(),this.s.bodyArea=this._calcBodyArea()},_clone:function(t){var e=this.s.dt,e=v(e.table().node().cloneNode(!1)).addClass("dt-rowReorder-float").append("<tbody/>").append(t.clone(!1)),o=t.outerWidth(),r=t.outerHeight(),n=v(v(this.s.dt.table().node()).parent()),s=n.width(),n=n.scrollLeft(),i=t.children().map(function(){return v(this).width()}),t=(e.width(o).height(r).find("tr").children().each(function(t){this.style.width=i[t]+"px"}),v("<div>").addClass("dt-rowReorder-float-parent").width(s).append(e).appendTo("body").scrollLeft(n));this.dom.clone=e,this.dom.cloneParent=t,this.s.domCloneOuterHeight=e.outerHeight()},_clonePosition:function(t){var e=this.s.start,o=this._eventToPage(t,"Y")-e.top,t=this._eventToPage(t,"X")-e.left,r=this.c.snapX,o=o+e.offsetTop,r=!0===r?e.offsetLeft:"number"==typeof r?e.offsetLeft+r:t+e.offsetLeft+this.dom.cloneParent.scrollLeft();o<0?o=0:o+this.s.domCloneOuterHeight>this.s.documentOuterHeight&&(o=this.s.documentOuterHeight-this.s.domCloneOuterHeight),this.dom.cloneParent.css({top:o,left:r})},_emitEvent:function(o,r){var n;return this.s.dt.iterator("table",function(t,e){t=v(t.nTable).triggerHandler(o+".dt",r);void 0!==t&&(n=t)}),n},_eventToPage:function(t,e){return(-1!==t.type.indexOf("touch")?t.originalEvent.touches[0]:t)["page"+e]},_mouseDown:function(t,e){var o=this,r=this.s.dt,n=this.s.start,s=this.c.cancelable,i=e.offset(),i=(n.top=this._eventToPage(t,"Y"),n.left=this._eventToPage(t,"X"),n.offsetTop=i.top,n.offsetLeft=i.left,n.nodes=v.unique(r.rows({page:"current"}).nodes().toArray()),this._cachePositions(),this._clone(e),this._clonePosition(t),this._eventToPage(t,"Y")-this.s.bodyTop),r=(n.rowIndex=this._calcRowIndexByPos(i),(this.dom.target=e).addClass("dt-rowReorder-moving"),v(l).on("mouseup.rowReorder touchend.rowReorder",function(t){o._mouseUp(t)}).on("mousemove.rowReorder touchmove.rowReorder",function(t){o._mouseMove(t)}),v(d).width()===v(l).width()&&v(l.body).addClass("dt-rowReorder-noOverflow"),this.dom.dtScroll);this.s.scroll={windowHeight:v(d).height(),windowWidth:v(d).width(),dtTop:r.length?r.offset().top:null,dtLeft:r.length?r.offset().left:null,dtHeight:r.length?r.outerHeight():null,dtWidth:r.length?r.outerWidth():null},s&&v(l).on("keyup",this._keyup)},_mouseMove:function(t){this._clonePosition(t);for(var e,o,r=this.s.start,n=this.c.cancelable,s=(n&&(e=this.s.bodyArea,o=this._calcCloneParentArea(),this.s.dropAllowed=this._rectanglesIntersect(e,o),this.s.dropAllowed?v(this.dom.cloneParent).removeClass("drop-not-allowed"):v(this.dom.cloneParent).addClass("drop-not-allowed")),this._eventToPage(t,"Y")-this.s.bodyTop),i=this.s.middles,d=null,l=0,a=i.length;l<a;l++)if(s<i[l]){d=l;break}null===d&&(d=i.length),n&&(this.s.dropAllowed||(d=r.rowIndex>this.s.lastInsert?r.rowIndex+1:r.rowIndex),this.dom.target.toggleClass("dt-rowReorder-moving",this.s.dropAllowed)),this._moveTargetIntoPosition(d),this._shiftScroll(t)},_mouseUp:function(t){var e=this,o=this.s.dt,r=this.c.dataSrc;if(this.s.dropAllowed){for(var n,s,i,d=this.s.start.nodes,l=v.unique(o.rows({page:"current"}).nodes().toArray()),a={},c=[],h=[],u=this.s.getDataFn,f=this.s.setDataFn,w=0,p=d.length;w<p;w++)d[w]!==l[w]&&(n=o.row(l[w]).id(),s=o.row(l[w]).data(),i=o.row(d[w]).data(),n&&(a[n]=u(i)),c.push({node:l[w],oldData:u(s),newData:u(i),newPosition:w,oldPosition:v.inArray(l[w],d)}),h.push(l[w]));var g,m=[c,{dataSrc:r,nodes:h,values:a,triggerRow:o.row(this.dom.target),originalEvent:t}];!1===this._emitEvent("row-reorder",m)?e._cancel():(this._cleanupDragging(),g=function(){if(e.c.update){for(w=0,p=c.length;w<p;w++){var t=o.row(c[w].node).data();f(t,c[w].newData),o.columns().every(function(){this.dataSrc()===r&&o.cell(c[w].node,this.index()).invalidate("data")})}e._emitEvent("row-reordered",m),o.draw(!1)}},this.c.editor?(this.c.enable=!1,this.c.editor.edit(h,!1,v.extend({submit:"changed"},this.c.formOptions)).multiSet(r,a).one("preSubmitCancelled.rowReorder",function(){e.c.enable=!0,e.c.editor.off(".rowReorder"),o.draw(!1)}).one("submitUnsuccessful.rowReorder",function(){o.draw(!1)}).one("submitSuccess.rowReorder",function(){g()}).one("submitComplete",function(){e.c.enable=!0,e.c.editor.off(".rowReorder")}).submit()):g())}else e._cancel()},_moveTargetIntoPosition:function(t){var e,o,r=this.s.dt;null!==this.s.lastInsert&&this.s.lastInsert===t||(e=v.unique(r.rows({page:"current"}).nodes().toArray()),o="",o=t>this.s.lastInsert?(this.dom.target.insertAfter(e[t-1]),"after"):(this.dom.target.insertBefore(e[t]),"before"),this._cachePositions(),this.s.lastInsert=t,this._emitEvent("row-reorder-changed",{insertPlacement:o,insertPoint:t,row:r.row(this.dom.target)}))},_cleanupDragging:function(){var t=this.c.cancelable;this.dom.clone.remove(),this.dom.cloneParent.remove(),this.dom.clone=null,this.dom.cloneParent=null,this.dom.target.removeClass("dt-rowReorder-moving"),v(l).off(".rowReorder"),v(l.body).removeClass("dt-rowReorder-noOverflow"),clearInterval(this.s.scrollInterval),this.s.scrollInterval=null,t&&v(l).off("keyup",this._keyup)},_shiftScroll:function(t){var e,o,r=this,n=this.s.scroll,s=!1,i=t.pageY-l.body.scrollTop;i<v(d).scrollTop()+65?e=-5:i>n.windowHeight+v(d).scrollTop()-65&&(e=5),null!==n.dtTop&&t.pageY<n.dtTop+65?o=-5:null!==n.dtTop&&t.pageY>n.dtTop+n.dtHeight-65&&(o=5),e||o?(n.windowVert=e,n.dtVert=o,s=!0):this.s.scrollInterval&&(clearInterval(this.s.scrollInterval),this.s.scrollInterval=null),!this.s.scrollInterval&&s&&(this.s.scrollInterval=setInterval(function(){var t;n.windowVert&&(t=v(l).scrollTop(),v(l).scrollTop(t+n.windowVert),t!==v(l).scrollTop())&&(t=parseFloat(r.dom.cloneParent.css("top")),r.dom.cloneParent.css("top",t+n.windowVert)),n.dtVert&&(t=r.dom.dtScroll[0],n.dtVert)&&(t.scrollTop+=n.dtVert)},20))},_calcBodyArea:function(t){var e=this.s.dt,o=v(e.table().body()).offset();return{left:o.left,top:o.top,right:o.left+v(e.table().body()).width(),bottom:o.top+v(e.table().body()).height()}},_calcCloneParentArea:function(t){var e=v(this.dom.cloneParent).offset();return{left:e.left,top:e.top,right:e.left+v(this.dom.cloneParent).width(),bottom:e.top+v(this.dom.cloneParent).height()}},_rectanglesIntersect:function(t,e){return!(t.left>=e.right||e.left>=t.right||t.top>=e.bottom||e.top>=t.bottom)},_calcRowIndexByPos:function(r){var t=this.s.dt,e=v.unique(t.rows({page:"current"}).nodes().toArray()),n=-1,s=v(t.table().node()).find("thead").outerHeight();return v.each(e,function(t,e){var o=v(e).position().top-s,e=o+v(e).outerHeight();o<=r&&r<=e&&(n=t)}),n},_keyup:function(t){this.c.cancelable&&27===t.which&&(t.preventDefault(),this._cancel())},_cancel:function(){var t=this.s.start,t=t.rowIndex>this.s.lastInsert?t.rowIndex+1:t.rowIndex;this._moveTargetIntoPosition(t),this._cleanupDragging(),this._emitEvent("row-reorder-canceled",[this.s.start.rowIndex])}}),s.defaults={dataSrc:0,editor:null,enable:!0,formOptions:{},selector:"td:first-child",snapX:!1,update:!0,excludedChildren:"a",cancelable:!1},v.fn.dataTable.Api);return t.register("rowReorder()",function(){return this}),t.register("rowReorder.enable()",function(e){return void 0===e&&(e=!0),this.iterator("table",function(t){t.rowreorder&&(t.rowreorder.c.enable=e)})}),t.register("rowReorder.disable()",function(){return this.iterator("table",function(t){t.rowreorder&&(t.rowreorder.c.enable=!1)})}),s.version="1.5.0",v.fn.dataTable.RowReorder=s,v.fn.DataTable.RowReorder=s,v(l).on("init.dt.dtr",function(t,e,o){var r,n;"dt"===t.namespace&&(t=e.oInit.rowReorder,r=i.defaults.rowReorder,t||r)&&(n=v.extend({},t,r),!1!==t)&&new s(e,n)}),i});

/*! Scroller 2.4.3
 * © SpryMedia Ltd - datatables.net/license
 */
!function(o){var e,i;"function"==typeof define&&define.amd?define(["jquery","datatables.net"],function(t){return o(t,window,document)}):"object"==typeof exports?(e=require("jquery"),i=function(t,s){s.fn.dataTable||require("datatables.net")(t,s)},"undefined"==typeof window?module.exports=function(t,s){return t=t||window,s=s||e(t),i(t,s),o(s,t,t.document)}:(i(window,e),module.exports=o(e,window,window.document))):o(jQuery,window,document)}(function(d,l,o){"use strict";function i(t,s){this instanceof i?(void 0===s&&(s={}),t=d.fn.dataTable.Api(t),this.s={dt:t.settings()[0],dtApi:t,tableTop:0,tableBottom:0,redrawTop:0,redrawBottom:0,autoHeight:!0,viewportRows:0,stateTO:null,stateSaveThrottle:function(){},drawTO:null,heights:{jump:null,page:null,virtual:null,scroll:null,row:null,viewport:null,labelHeight:0,xbar:0},topRowFloat:0,scrollDrawDiff:null,loaderVisible:!1,forceReposition:!1,baseRowTop:0,baseScrollTop:0,mousedown:!1,lastScrollTop:0},this.s=d.extend(this.s,i.oDefaults,s),this.s.heights.row=this.s.rowHeight,this.dom={force:o.createElement("div"),label:d('<div class="dts_label">0</div>'),scroller:null,table:null,loader:null},this.s.dt.oScroller||(this.s.dt.oScroller=this).construct()):alert("Scroller warning: Scroller must be initialised with the 'new' keyword.")}var a=d.fn.dataTable,t=(d.extend(i.prototype,{measure:function(t){this.s.autoHeight&&this._calcRowHeight();var s=this.s.heights,o=(s.row&&(s.viewport=this._parseHeight(d(this.dom.scroller).css("max-height")),this.s.viewportRows=parseInt(s.viewport/s.row,10)+1,this.s.dt._iDisplayLength=this.s.viewportRows*this.s.displayBuffer),this.dom.label.outerHeight());s.xbar=this.dom.scroller.offsetHeight-this.dom.scroller.clientHeight,s.labelHeight=o,void 0!==t&&!t||this.s.dtApi.draw(!1)},pageInfo:function(){var t=this.s.dt,s=this.dom.scroller.scrollTop,t=t.fnRecordsDisplay(),o=Math.ceil(this.pixelsToRow(s+this.s.heights.viewport,!1,this.s.ani));return{start:Math.floor(this.pixelsToRow(s,!1,this.s.ani)),end:t<o?t-1:o-1}},pixelsToRow:function(t,s,o){t-=this.s.baseScrollTop,o=o?(this._domain("physicalToVirtual",this.s.baseScrollTop)+t)/this.s.heights.row:t/this.s.heights.row+this.s.baseRowTop;return s||void 0===s?parseInt(o,10):o},rowToPixels:function(t,s,o){t-=this.s.baseRowTop,o=o?this._domain("virtualToPhysical",this.s.baseScrollTop):this.s.baseScrollTop;return o+=t*this.s.heights.row,s||void 0===s?parseInt(o,10):o},scrollToRow:function(t,s){var o=this,e=!1,i=this.rowToPixels(t),r=t-(this.s.displayBuffer-1)/2*this.s.viewportRows;r<0&&(r=0),void 0===(s=(i>this.s.redrawBottom||i<this.s.redrawTop)&&this.s.dt._iDisplayStart!==r&&(e=!0,i=this._domain("virtualToPhysical",t*this.s.heights.row),this.s.redrawTop<i)&&i<this.s.redrawBottom?!(this.s.forceReposition=!0):s)||s?(this.s.ani=e,d(this.dom.scroller).animate({scrollTop:i},function(){setTimeout(function(){o.s.ani=!1},250)})):d(this.dom.scroller).scrollTop(i)},construct:function(){var e=this,t=this.s.dtApi;if(!this.s.dt.oFeatures.bPaginate)throw new Error("Pagination must be enabled for Scroller to operate");this.dom.force.style.position="relative",this.dom.force.style.top="0px",this.dom.force.style.left="0px",this.dom.force.style.width="1px",this.dom.scroller=t.table().node().parentNode,this.dom.scroller.appendChild(this.dom.force),this.dom.scroller.style.position="relative",this.dom.table=d(">table",this.dom.scroller)[0],this.dom.table.style.position="absolute",this.dom.table.style.top="0px",this.dom.table.style.left="0px",d(t.table().container()).addClass("dts DTS"),this.dom.label.appendTo(this.dom.scroller),this.s.heights.row&&"auto"!=this.s.heights.row&&(this.s.autoHeight=!1),this.s.ingnoreScroll=!0,d(this.dom.scroller).on("scroll.dt-scroller",function(t){e._scroll.call(e)}),d(this.dom.scroller).on("touchstart.dt-scroller",function(){e._scroll.call(e)}),d(this.dom.scroller).on("mousedown.dt-scroller",function(){e.s.mousedown=!0}).on("mouseup.dt-scroller",function(){e.s.labelVisible=!1,e.s.mousedown=!1,e.dom.label.css("display","none")}),d(l).on("resize.dt-scroller",function(){e.measure(!1),e._info()});var i=!0,r=t.state.loaded();t.on("stateSaveParams.scroller",function(t,s,o){i&&r?(o.scroller=r.scroller,i=!1,o.scroller&&(e.s.lastScrollTop=o.scroller.scrollTop)):o.scroller={topRow:e.s.topRowFloat,baseRowTop:e.s.baseRowTop}}),t.on("stateLoadParams.scroller",function(t,s,o){void 0!==o.scroller&&e.scrollToRow(o.scroller.topRow)}),this.measure(!1),r&&r.scroller&&(this.s.topRowFloat=r.scroller.topRow,this.s.baseRowTop=r.scroller.baseRowTop,this.s.baseScrollTop=this.s.baseRowTop*this.s.heights.row,r.scroller.scrollTop=this._domain("physicalToVirtual",this.s.topRowFloat*this.s.heights.row)),e.s.stateSaveThrottle=a.util.throttle(function(){e.s.dtApi.state.save()},500),t.on("init.scroller",function(){e.measure(!1),e.s.scrollType="jump",e._draw(),t.on("draw.scroller",function(){e._draw()})}),t.on("preDraw.dt.scroller",function(){e._scrollForce()}),t.on("destroy.scroller",function(){d(l).off("resize.dt-scroller"),d(e.dom.scroller).off(".dt-scroller"),d(e.s.dt.nTable).off(".scroller"),d(e.s.dt.nTableWrapper).removeClass("DTS"),d("div.DTS_Loading",e.dom.scroller.parentNode).remove(),e.dom.table.style.position="",e.dom.table.style.top="",e.dom.table.style.left=""})},_calcRowHeight:function(){var t=this.s.dt,s=t.nTable,o=s.cloneNode(!1),e=d("<tbody/>").appendTo(o),t=t.oClasses,t=a.versionCheck("2")?{container:t.container,scroller:t.scrolling.container,body:t.scrolling.body}:{container:t.sWrapper,scroller:t.sScrollWrapper,body:t.sScrollBody},i=d('<div class="'+t.container+' DTS"><div class="'+t.scroller+'"><div class="'+t.body+'"></div></div></div>'),r=(d("tbody tr:lt(4)",s).clone().appendTo(e),d("tr",e).length);if(1===r)e.prepend("<tr><td>&#160;</td></tr>"),e.append("<tr><td>&#160;</td></tr>");else for(;r<3;r++)e.append("<tr><td>&#160;</td></tr>");d("div."+t.body,i).append(o);t=this.s.dt.nHolding||s.parentNode;d(t).is(":visible")||(t="body"),i.find("input").removeAttr("name"),i.appendTo(t),this.s.heights.row=d("tr",e).eq(1).outerHeight(),i.remove()},_draw:function(){var t=this,s=this.s.heights,o=this.dom.scroller.scrollTop,e=d(this.s.dt.nTable).height(),i=this.s.dt._iDisplayStart,r=this.s.dt._iDisplayLength,l=this.s.dt.fnRecordsDisplay(),a=o+s.viewport,n=(this.s.skip=!0,!this.s.dt.bSorted&&!this.s.dt.bFiltered||0!==i||this.s.dt._drawHold||(this.s.topRowFloat=0),o="jump"===this.s.scrollType?this._domain("virtualToPhysical",this.s.topRowFloat*s.row):o,this.s.baseScrollTop=o,this.s.baseRowTop=this.s.topRowFloat,o-(this.s.topRowFloat-i)*s.row),i=(0===i?n=0:l<=i+r?n=s.scroll-e:n+e<a&&(this.s.baseScrollTop+=1+((l=a-e)-n),n=l),this.dom.table.style.top=n+"px",this.s.tableTop=n,this.s.tableBottom=e+this.s.tableTop,(o-this.s.tableTop)*this.s.boundaryScale);this.s.redrawTop=o-i,this.s.redrawBottom=o+i>s.scroll-s.viewport-s.row?s.scroll-s.viewport-s.row:o+i,this.s.skip=!1,t.s.ingnoreScroll&&(this.s.dt.oFeatures.bStateSave&&null!==this.s.dt.oLoadedState&&void 0!==this.s.dt.oLoadedState.scroller?((r=!(!this.s.dt.sAjaxSource&&!t.s.dt.ajax||this.s.dt.oFeatures.bServerSide))&&2<=this.s.dt.iDraw||!r&&1<=this.s.dt.iDraw)&&setTimeout(function(){d(t.dom.scroller).scrollTop(t.s.dt.oLoadedState.scroller.scrollTop),setTimeout(function(){t.s.ingnoreScroll=!1},0)},0):t.s.ingnoreScroll=!1),this.s.dt.oFeatures.bInfo&&setTimeout(function(){t._info.call(t)},0),d(this.s.dt.nTable).triggerHandler("position.dts.dt",n)},_domain:function(t,s){var o,e=this.s.heights,i=1e4;return e.virtual===e.scroll||s<i?s:"virtualToPhysical"===t&&s>=e.virtual-i?(o=e.virtual-s,e.scroll-o):"physicalToVirtual"===t&&s>=e.scroll-i?(o=e.scroll-s,e.virtual-o):(e=i-(o=(e.virtual-i-i)/(e.scroll-i-i))*i,"virtualToPhysical"===t?(s-e)/o:o*s+e)},_info:function(){if(this.s.dt.oFeatures.bInfo){var t=this.s.dt,s=this.s.dtApi,o=t.oLanguage,e=s.page.info(),i=e.recordsDisplay,e=e.recordsTotal,r=(this.s.lastScrollTop-this.s.baseScrollTop)/this.s.heights.row,r=Math.floor(this.s.baseRowTop+r)+1,l=(r="jump"===this.s.scrollType?Math.floor(this.s.topRowFloat)+1:r)+Math.floor(this.s.heights.viewport/this.s.heights.row),l=i<l?i:l,a=0===i&&i==e?o.sInfoEmpty+o.sInfoPostFix:0===i?o.sInfoEmpty+" "+o.sInfoFiltered+o.sInfoPostFix:i==e?o.sInfo+o.sInfoPostFix:o.sInfo+" "+o.sInfoFiltered+o.sInfoPostFix,o=(a=this._macros(a,r,l,e,i),o.fnInfoCallback),n=(o&&(a=o.call(t.oInstance,t,r,l,e,i,a)),t.aanFeatures.i);if(void 0!==n){for(var h=0,c=n.length;h<c;h++)d(n[h]).html(a);d(t.nTable).triggerHandler("info.dt")}d("div.dt-info",s.table().container()).each(function(){d(this).html(a),s.trigger("info",[s.settings()[0],this,a])})}},_macros:function(t,s,o,e,i){var r=this.s.dtApi,l=this.s.dt,a=l.fnFormatNumber;return t.replace(/_START_/g,a.call(l,s)).replace(/_END_/g,a.call(l,o)).replace(/_MAX_/g,a.call(l,e)).replace(/_TOTAL_/g,a.call(l,i)).replace(/_ENTRIES_/g,r.i18n("entries","")).replace(/_ENTRIES-MAX_/g,r.i18n("entries","",e)).replace(/_ENTRIES-TOTAL_/g,r.i18n("entries","",i))},_parseHeight:function(t){var s,o,t=/^([+-]?(?:\d+(?:\.\d+)?|\.\d+))(px|em|rem|vh)$/.exec(t);return null!==t&&(o=parseFloat(t[1]),"px"===(t=t[2])?s=o:"vh"===t?s=o/100*d(l).height():"rem"===t?s=o*parseFloat(d(":root").css("font-size")):"em"===t&&(s=o*parseFloat(d("body").css("font-size"))),s)||0},_scroll:function(){var t,s=this,o=this.s.heights,e=this.dom.scroller.scrollTop;this.s.skip||this.s.ingnoreScroll||e!==this.s.lastScrollTop&&(this.s.dt.bFiltered||this.s.dt.bSorted?this.s.lastScrollTop=0:(clearTimeout(this.s.stateTO),this.s.stateTO=setTimeout(function(){s.s.dtApi.state.save(),s._info()},250),this.s.scrollType=Math.abs(e-this.s.lastScrollTop)>o.viewport?"jump":"cont",this.s.topRowFloat="cont"===this.s.scrollType?this.pixelsToRow(e,!1,!1):this._domain("physicalToVirtual",e)/o.row,this.s.topRowFloat<0&&(this.s.topRowFloat=0),this.s.forceReposition||e<this.s.redrawTop||e>this.s.redrawBottom?(t=Math.ceil((this.s.displayBuffer-1)/2*this.s.viewportRows),t=parseInt(this.s.topRowFloat,10)-t,this.s.forceReposition=!1,t<=0?t=0:t+this.s.dt._iDisplayLength>this.s.dt.fnRecordsDisplay()?(t=this.s.dt.fnRecordsDisplay()-this.s.dt._iDisplayLength)<0&&(t=0):t%2!=0&&t++,(this.s.targetTop=t)!=this.s.dt._iDisplayStart&&(this.s.tableTop=d(this.s.dt.nTable).offset().top,this.s.tableBottom=d(this.s.dt.nTable).height()+this.s.tableTop,t=function(){s.s.dt._iDisplayStart=s.s.targetTop,s.s.dtApi.draw("page")},this.s.dt.oFeatures.bServerSide?(this.s.forceReposition=!0,d(this.s.dt.nTable).triggerHandler("scroller-will-draw.dt"),a.versionCheck("2")?s.s.dtApi.processing(!0):this.s.dt.oApi._fnProcessingDisplay(this.s.dt,!0),clearTimeout(this.s.drawTO),this.s.drawTO=setTimeout(t,this.s.serverWait)):t())):this.s.topRowFloat=this.pixelsToRow(e,!1,!0),this._info(),this.s.lastScrollTop=e,this.s.stateSaveThrottle(),"jump"===this.s.scrollType&&this.s.mousedown&&(this.s.labelVisible=!0),this.s.labelVisible&&(t=(o.viewport-o.labelHeight-o.xbar)/o.scroll,this.dom.label.html(this.s.dt.fnFormatNumber(parseInt(this.s.topRowFloat,10)+1)).css("top",e+e*t).css("display","block"))))},_scrollForce:function(){var t=this.s.heights;t.virtual=t.row*this.s.dt.fnRecordsDisplay(),t.scroll=t.virtual,1e6<t.scroll&&(t.scroll=1e6),this.dom.force.style.height=t.scroll>this.s.heights.row?t.scroll+"px":this.s.heights.row+"px"}}),i.oDefaults=i.defaults={boundaryScale:.5,displayBuffer:9,rowHeight:"auto",serverWait:200},i.version="2.4.3",d(o).on("preInit.dt.dtscroller",function(t,s){var o,e;"dt"===t.namespace&&(t=s.oInit.scroller,o=a.defaults.scroller,t||o)&&(e=d.extend({},t,o),!1!==t)&&new i(s,e)}),d.fn.dataTable.Scroller=i,d.fn.DataTable.Scroller=i,d.fn.dataTable.Api);return t.register("scroller()",function(){return this}),t.register("scroller().rowToPixels()",function(t,s,o){var e=this.context;if(e.length&&e[0].oScroller)return e[0].oScroller.rowToPixels(t,s,o)}),t.register("scroller().pixelsToRow()",function(t,s,o){var e=this.context;if(e.length&&e[0].oScroller)return e[0].oScroller.pixelsToRow(t,s,o)}),t.register(["scroller().scrollToRow()","scroller.toPosition()"],function(s,o){return this.iterator("table",function(t){t.oScroller&&t.oScroller.scrollToRow(s,o)}),this}),t.register("row().scrollTo()",function(o){var e=this;return this.iterator("row",function(t,s){t.oScroller&&(s=e.rows({order:"applied",search:"applied"}).indexes().indexOf(s),t.oScroller.scrollToRow(s,o))}),this}),t.register("scroller.measure()",function(s){return this.iterator("table",function(t){t.oScroller&&t.oScroller.measure(s)}),this}),t.register("scroller.page()",function(){var t=this.context;if(t.length&&t[0].oScroller)return t[0].oScroller.pageInfo()}),a});

/*! SearchBuilder 1.7.1
 * ©SpryMedia Ltd - datatables.net/license/mit
 */
!function(e){var n,s;"function"==typeof define&&define.amd?define(["jquery","datatables.net"],function(t){return e(t,window,document)}):"object"==typeof exports?(n=require("jquery"),s=function(t,i){i.fn.dataTable||require("datatables.net")(t,i)},"undefined"==typeof window?module.exports=function(t,i){return t=t||window,i=i||n(t),s(t,i),e(i,t,t.document)}:(s(window,n),module.exports=e(n,window,window.document))):e(jQuery,window,document)}(function(s,u,t){"use strict";var I,c,a,d,l,h,p,m,e,i,f=s.fn.dataTable;function o(){return u.moment}function r(){return u.luxon}function w(t,i,e,n,s,o,r){void 0===n&&(n=0),void 0===s&&(s=1),void 0===o&&(o=void 0),void 0===r&&(r=!1);var a=this;if(!c||!c.versionCheck||!c.versionCheck("1.10.0"))throw new Error("SearchPane requires DataTables 1.10 or newer");this.classes=I.extend(!0,{},w.classes),this.c=I.extend(!0,{},w.defaults,I.fn.dataTable.ext.searchBuilder,i);i=this.c.i18n;if(this.s={condition:void 0,conditions:{},data:void 0,dataIdx:-1,dataPoints:[],dateFormat:!1,depth:s,dt:t,filled:!1,index:n,liveSearch:r,origData:void 0,preventRedraw:!1,serverData:o,topGroup:e,type:"",value:[]},this.dom={buttons:I("<div/>").addClass(this.classes.buttonContainer),condition:I("<select disabled/>").addClass(this.classes.condition).addClass(this.classes.dropDown).addClass(this.classes.italic).attr("autocomplete","hacking"),conditionTitle:I('<option value="" disabled selected hidden/>').html(this.s.dt.i18n("searchBuilder.condition",i.condition)),container:I("<div/>").addClass(this.classes.container),data:I("<select/>").addClass(this.classes.data).addClass(this.classes.dropDown).addClass(this.classes.italic),dataTitle:I('<option value="" disabled selected hidden/>').html(this.s.dt.i18n("searchBuilder.data",i.data)),defaultValue:I("<select disabled/>").addClass(this.classes.value).addClass(this.classes.dropDown).addClass(this.classes.select).addClass(this.classes.italic),delete:I("<button/>").html(this.s.dt.i18n("searchBuilder.delete",i.delete)).addClass(this.classes.delete).addClass(this.classes.button).attr("title",this.s.dt.i18n("searchBuilder.deleteTitle",i.deleteTitle)).attr("type","button"),inputCont:I("<div/>").addClass(this.classes.inputCont),left:I("<button/>").html(this.s.dt.i18n("searchBuilder.left",i.left)).addClass(this.classes.left).addClass(this.classes.button).attr("title",this.s.dt.i18n("searchBuilder.leftTitle",i.leftTitle)).attr("type","button"),right:I("<button/>").html(this.s.dt.i18n("searchBuilder.right",i.right)).addClass(this.classes.right).addClass(this.classes.button).attr("title",this.s.dt.i18n("searchBuilder.rightTitle",i.rightTitle)).attr("type","button"),value:[I("<select disabled/>").addClass(this.classes.value).addClass(this.classes.dropDown).addClass(this.classes.italic).addClass(this.classes.select)],valueTitle:I('<option value="--valueTitle--" disabled selected hidden/>').html(this.s.dt.i18n("searchBuilder.value",i.value))},this.c.greyscale){this.dom.data.addClass(this.classes.greyscale),this.dom.condition.addClass(this.classes.greyscale),this.dom.defaultValue.addClass(this.classes.greyscale);for(var d=0,l=this.dom.value;d<l.length;d++)l[d].addClass(this.classes.greyscale)}return I(u).on("resize.dtsb",c.util.throttle(function(){a.s.topGroup.trigger("dtsb-redrawLogic")})),this._buildCriteria(),this}function g(t,i,e,n,s,o,r){if(void 0===n&&(n=0),void 0===s&&(s=!1),void 0===o&&(o=1),void 0===r&&(r=void 0),d&&d.versionCheck&&d.versionCheck("1.10.0"))return this.classes=a.extend(!0,{},g.classes),this.c=a.extend(!0,{},g.defaults,i),this.s={criteria:[],depth:o,dt:t,index:n,isChild:s,logic:void 0,opts:i,preventRedraw:!1,serverData:r,toDrop:void 0,topGroup:e},this.dom={add:a("<button/>").addClass(this.classes.add).addClass(this.classes.button).attr("type","button"),clear:a("<button>&times</button>").addClass(this.classes.button).addClass(this.classes.clearGroup).attr("type","button"),container:a("<div/>").addClass(this.classes.group),logic:a("<button><div/></button>").addClass(this.classes.logic).addClass(this.classes.button).attr("type","button"),logicContainer:a("<div/>").addClass(this.classes.logicContainer),search:a("<button/>").addClass(this.classes.search).addClass(this.classes.button).attr("type","button").css("display","none")},void 0===this.s.topGroup&&(this.s.topGroup=this.dom.container),this._setup(),this;throw new Error("SearchBuilder requires DataTables 1.10 or newer")}function n(t,i){var s=this;if(!p||!p.versionCheck||!p.versionCheck("1.10.0"))throw new Error("SearchBuilder requires DataTables 1.10 or newer");t=new p.Api(t);if(this.classes=h.extend(!0,{},n.classes),this.c=h.extend(!0,{},n.defaults,i),this.dom={clearAll:h('<button type="button">'+t.i18n("searchBuilder.clearAll",this.c.i18n.clearAll)+"</button>").addClass(this.classes.clearAll).addClass(this.classes.button).attr("type","button"),container:h("<div/>").addClass(this.classes.container),title:h("<div/>").addClass(this.classes.title),titleRow:h("<div/>").addClass(this.classes.titleRow),topGroup:void 0},this.s={dt:t,opts:i,search:void 0,serverData:void 0,topGroup:void 0},void 0===t.settings()[0]._searchBuilder)return(t.settings()[0]._searchBuilder=this).s.dt.page.info().serverSide&&(this.s.dt.on("preXhr.dtsb",function(t,i,e){var n=s.s.dt.state.loaded();n&&n.searchBuilder&&(e.searchBuilder=s._collapseArray(n.searchBuilder))}),this.s.dt.on("xhr.dtsb",function(t,i,e){e&&e.searchBuilder&&e.searchBuilder.options&&(s.s.serverData=e.searchBuilder.options)})),this.s.dt.settings()[0]._bInitComplete?this._setUp():t.one("init.dt",function(){s._setUp()}),this}function v(t,i){t=new f.Api(t),i=i||t.init().searchBuilder||f.defaults.searchBuilder;return new e(t,i).getNode()}return w._escapeHTML=function(t){return t.toString().replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&quot;/g,'"').replace(/&amp;/g,"&")},w.prototype.doSearch=function(){this.c.liveSearch&&this.s.dt.draw()},w.parseNumFmt=function(t){return+t.replace(/(?!^-)[^0-9.]/g,"")},w.prototype.updateArrows=function(t){void 0===t&&(t=!1),this.dom.container.children().detach(),this.dom.container.append(this.dom.data).append(this.dom.condition).append(this.dom.inputCont),this.setListeners(),void 0!==this.dom.value[0]&&I(this.dom.value[0]).trigger("dtsb-inserted");for(var i=1;i<this.dom.value.length;i++)this.dom.inputCont.append(this.dom.value[i]),I(this.dom.value[i]).trigger("dtsb-inserted");1<this.s.depth&&this.dom.buttons.append(this.dom.left),(!1===this.c.depthLimit||this.s.depth<this.c.depthLimit)&&t?this.dom.buttons.append(this.dom.right):this.dom.right.remove(),this.dom.buttons.append(this.dom.delete),this.dom.container.append(this.dom.buttons)},w.prototype.destroy=function(){this.dom.data.off(".dtsb"),this.dom.condition.off(".dtsb"),this.dom.delete.off(".dtsb");for(var t=0,i=this.dom.value;t<i.length;t++)i[t].off(".dtsb");this.dom.container.remove()},w.prototype.search=function(t,i){var e=this.s.dt.settings()[0],n=this.s.conditions[this.s.condition];if(void 0!==this.s.condition&&void 0!==n){var s=t[this.s.dataIdx];if(this.s.type.includes("num")&&(""!==e.oLanguage.sDecimal||""!==e.oLanguage.sThousands)){var o=[t[this.s.dataIdx]];if(""!==e.oLanguage.sDecimal&&(o=t[this.s.dataIdx].split(e.oLanguage.sDecimal)),""!==e.oLanguage.sThousands)for(var r=0;r<o.length;r++)o[r]=o[r].replace(e.oLanguage.sThousands,",");s=o.join(".")}if("filter"!==this.c.orthogonal.search&&(s=e.fastData(i,this.s.dataIdx,"string"==typeof this.c.orthogonal?this.c.orthogonal:this.c.orthogonal.search)),"array"===this.s.type){(s=Array.isArray(s)?s:[s]).sort();for(var a=0,d=s;a<d.length;a++){var l=d[a];l&&"string"==typeof l&&(l=l.replace(/[\r\n\u2028]/g," "))}}else null!==s&&"string"==typeof s&&(s=s.replace(/[\r\n\u2028]/g," "));return this.s.type.includes("html")&&"string"==typeof s&&(s=s.replace(/(<([^>]+)>)/gi,"")),n.search(s=null===s?"":s,this.s.value,this)}},w.prototype.getDetails=function(t){void 0===t&&(t=!1);var i,e=this.s.dt.settings()[0];if(null===this.s.type||!this.s.type.includes("num")||""===e.oLanguage.sDecimal&&""===e.oLanguage.sThousands){if(null!==this.s.type&&t)if(this.s.type.includes("date")||this.s.type.includes("time"))for(i=0;i<this.s.value.length;i++)null===this.s.value[i].match(/^\d{4}-([0]\d|1[0-2])-([0-2]\d|3[01])$/g)&&(this.s.value[i]="");else if(this.s.type.includes("moment"))for(i=0;i<this.s.value.length;i++)this.s.value[i]&&0<this.s.value[i].length&&o()(this.s.value[i],this.s.dateFormat,!0).isValid()&&(this.s.value[i]=o()(this.s.value[i],this.s.dateFormat).format("YYYY-MM-DD HH:mm:ss"));else if(this.s.type.includes("luxon"))for(i=0;i<this.s.value.length;i++)this.s.value[i]&&0<this.s.value[i].length&&null===r().DateTime.fromFormat(this.s.value[i],this.s.dateFormat).invalid&&(this.s.value[i]=r().DateTime.fromFormat(this.s.value[i],this.s.dateFormat).toFormat("yyyy-MM-dd HH:mm:ss"))}else for(i=0;i<this.s.value.length;i++){var n=[this.s.value[i].toString()];if(""!==e.oLanguage.sDecimal&&(n=this.s.value[i].split(e.oLanguage.sDecimal)),""!==e.oLanguage.sThousands)for(var s=0;s<n.length;s++)n[s]=n[s].replace(e.oLanguage.sThousands,",");this.s.value[i]=n.join(".")}if(this.s.type.includes("num")&&this.s.dt.page.info().serverSide)for(i=0;i<this.s.value.length;i++)this.s.value[i]=this.s.value[i].replace(/[^0-9.\-]/g,"");return{condition:this.s.condition,data:this.s.data,origData:this.s.origData,type:this.s.type,value:this.s.value.map(function(t){return null!=t?t.toString():t})}},w.prototype.getNode=function(){return this.dom.container},w.prototype.parseNumber=function(t){var i=this.s.dt.i18n("decimal");return+(t=i&&"."!==i?t.replace(/\./g,"").replace(i,"."):t).replace(/(?!^-)[^0-9.]/g,"")},w.prototype.populate=function(){this._populateData(),-1!==this.s.dataIdx&&(this._populateCondition(),void 0!==this.s.condition)&&this._populateValue()},w.prototype.rebuild=function(t){var i,e,n,s=!1;if(this._populateData(),void 0!==t.data&&(e=this.classes.italic,n=this.dom.data,this.dom.data.children("option").each(function(){!s&&(I(this).text()===t.data||t.origData&&I(this).prop("origData")===t.origData)?(I(this).prop("selected",!0),n.removeClass(e),s=!0,i=parseInt(I(this).val(),10)):I(this).removeProp("selected")})),s){this.s.data=t.data,this.s.origData=t.origData,this.s.dataIdx=i,this.c.orthogonal=this._getOptions().orthogonal,this.dom.dataTitle.remove(),this._populateCondition(),this.dom.conditionTitle.remove();for(var o=void 0,r=this.dom.condition.children("option"),a=0;a<r.length;a++){var d=I(r[a]);void 0!==t.condition&&d.val()===t.condition&&"string"==typeof t.condition?(d.prop("selected",!0),o=d.val()):d.removeProp("selected")}if(this.s.condition=o,void 0!==this.s.condition){for(this.dom.conditionTitle.removeProp("selected"),this.dom.conditionTitle.remove(),this.dom.condition.removeClass(this.classes.italic),a=0;a<r.length;a++){var l=I(r[a]);l.val()!==this.s.condition&&l.removeProp("selected")}this._populateValue(t)}else this.dom.conditionTitle.prependTo(this.dom.condition).prop("selected",!0)}},w.prototype.setListeners=function(){var l=this;this.dom.data.unbind("change").on("change.dtsb",function(){l.dom.dataTitle.removeProp("selected");for(var t=l.dom.data.children("option."+l.classes.option),i=0;i<t.length;i++){var e=I(t[i]);e.val()===l.dom.data.val()?(l.dom.data.removeClass(l.classes.italic),e.prop("selected",!0),l.s.dataIdx=+e.val(),l.s.data=e.text(),l.s.origData=e.prop("origData"),l.c.orthogonal=l._getOptions().orthogonal,l._clearCondition(),l._clearValue(),l._populateCondition(),l.s.filled&&(l.s.filled=!1,l.doSearch(),l.setListeners()),l.s.dt.state.save()):e.removeProp("selected")}}),this.dom.condition.unbind("change").on("change.dtsb",function(){l.dom.conditionTitle.removeProp("selected");for(var t=l.dom.condition.children("option."+l.classes.option),i=0;i<t.length;i++){var e=I(t[i]);if(e.val()===l.dom.condition.val()){l.dom.condition.removeClass(l.classes.italic),e.prop("selected",!0);for(var n=e.val(),s=0,o=Object.keys(l.s.conditions);s<o.length;s++)if(o[s]===n){l.s.condition=n;break}l._clearValue(),l._populateValue();for(var r=0,a=l.dom.value;r<a.length;r++){var d=a[r];l.s.filled&&void 0!==d&&0!==l.dom.inputCont.has(d[0]).length&&(l.s.filled=!1,l.doSearch(),l.setListeners())}(0===l.dom.value.length||1===l.dom.value.length&&void 0===l.dom.value[0])&&l.doSearch()}else e.removeProp("selected")}})},w.prototype.setupButtons=function(){550<u.innerWidth?(this.dom.container.removeClass(this.classes.vertical),this.dom.buttons.css("left",null),this.dom.buttons.css("top",null)):(this.dom.container.addClass(this.classes.vertical),this.dom.buttons.css("left",this.dom.data.innerWidth()),this.dom.buttons.css("top",this.dom.data.position().top))},w.prototype._buildCriteria=function(){this.dom.data.append(this.dom.dataTitle),this.dom.condition.append(this.dom.conditionTitle),this.dom.container.append(this.dom.data).append(this.dom.condition),this.dom.inputCont.empty();for(var t=0,i=this.dom.value;t<i.length;t++){var e=i[t];e.append(this.dom.valueTitle),this.dom.inputCont.append(e)}this.dom.buttons.append(this.dom.delete).append(this.dom.right),this.dom.container.append(this.dom.inputCont).append(this.dom.buttons),this.setListeners()},w.prototype._clearCondition=function(){this.dom.condition.empty(),this.dom.conditionTitle.prop("selected",!0).attr("disabled","true"),this.dom.condition.prepend(this.dom.conditionTitle).prop("selectedIndex",0),this.s.conditions={},this.s.condition=void 0},w.prototype._clearValue=function(){var t;if(void 0!==this.s.condition){if(0<this.dom.value.length&&void 0!==this.dom.value[0])for(var i=0,e=this.dom.value;i<e.length;i++)void 0!==(t=e[i])&&setTimeout(function(){t.remove()},50);if(this.dom.value=[].concat(this.s.conditions[this.s.condition].init(this,w.updateListener)),0<this.dom.value.length&&void 0!==this.dom.value[0]){this.dom.inputCont.empty().append(this.dom.value[0]).insertAfter(this.dom.condition),I(this.dom.value[0]).trigger("dtsb-inserted");for(var n=1;n<this.dom.value.length;n++)this.dom.inputCont.append(this.dom.value[n]),I(this.dom.value[n]).trigger("dtsb-inserted")}}else{for(var s=0,o=this.dom.value;s<o.length;s++)void 0!==(t=o[s])&&setTimeout(function(){t.remove()},50);this.dom.valueTitle.prop("selected",!0),this.dom.defaultValue.append(this.dom.valueTitle).insertAfter(this.dom.condition)}this.s.value=[],this.dom.value=[I("<select disabled/>").addClass(this.classes.value).addClass(this.classes.dropDown).addClass(this.classes.italic).addClass(this.classes.select).append(this.dom.valueTitle.clone())]},w.prototype._getOptions=function(){var t=this.s.dt;return I.extend(!0,{},w.defaults,t.settings()[0].aoColumns[this.s.dataIdx].searchBuilder)},w.prototype._populateCondition=function(){var t=[],i=Object.keys(this.s.conditions).length,e=this.s.dt.settings()[0].aoColumns,n=+this.dom.data.children("option:selected").val();if(0===i){this.s.type=this.s.dt.column(n).type(),void 0!==e&&(void 0!==(s=e[n]).searchBuilderType&&null!==s.searchBuilderType?this.s.type=s.searchBuilderType:void 0!==this.s.type&&null!==this.s.type||(this.s.type=s.sType)),null!==this.s.type&&void 0!==this.s.type||(I.fn.dataTable.ext.oApi&&I.fn.dataTable.ext.oApi._fnColumnTypes(this.s.dt.settings()[0]),this.s.type=this.s.dt.column(n).type()),this.dom.condition.removeAttr("disabled").empty().append(this.dom.conditionTitle).addClass(this.classes.italic),this.dom.conditionTitle.prop("selected",!0);var s=this.s.dt.settings()[0].oLanguage.sDecimal,o=(""!==s&&this.s.type.indexOf(s)===this.s.type.length-s.length&&(this.s.type.includes("num-fmt")||this.s.type.includes("num"))&&(this.s.type=this.s.type.replace(s,"")),void 0!==this.c.conditions[this.s.type]?this.c.conditions[this.s.type]:this.s.type.includes("moment")?this.c.conditions.moment:this.s.type.includes("luxon")?this.c.conditions.luxon:this.c.conditions.string);this.s.type.includes("moment")?this.s.dateFormat=this.s.type.replace(/moment-/g,""):this.s.type.includes("luxon")&&(this.s.dateFormat=this.s.type.replace(/luxon-/g,""));for(var r,a,d=0,l=Object.keys(o);d<l.length;d++)null!==o[a=l[d]]&&(this.s.dt.page.info().serverSide&&o[a].init===w.initSelect&&(r=e[n],this.s.serverData&&this.s.serverData[r.data]?(o[a].init=w.initSelectSSP,o[a].inputValue=w.inputValueSelect,o[a].isInputValid=w.isInputValidSelect):(o[a].init=w.initInput,o[a].inputValue=w.inputValueInput,o[a].isInputValid=w.isInputValidInput)),this.s.conditions[a]=o[a],"function"==typeof(C=o[a].conditionName)&&(C=C(this.s.dt,this.c.i18n)),t.push(I("<option>",{text:C,value:a}).addClass(this.classes.option).addClass(this.classes.notItalic)))}else{if(!(0<i))return void this.dom.condition.attr("disabled","true").addClass(this.classes.italic);this.dom.condition.empty().removeAttr("disabled").addClass(this.classes.italic);for(var u=0,c=Object.keys(this.s.conditions);u<c.length;u++){a=c[u];var h=this.s.conditions[a].conditionName,p=("function"==typeof h&&(h=h(this.s.dt,this.c.i18n)),I("<option>",{text:h,value:a}).addClass(this.classes.option).addClass(this.classes.notItalic));void 0!==this.s.condition&&this.s.condition===h&&(p.prop("selected",!0),this.dom.condition.removeClass(this.classes.italic)),t.push(p)}}for(var m=0,f=t;m<f.length;m++)this.dom.condition.append(f[m]);if(e[n].searchBuilder&&e[n].searchBuilder.defaultCondition){var g=e[n].searchBuilder.defaultCondition;if("number"==typeof g)this.dom.condition.prop("selectedIndex",g),this.dom.condition.trigger("change");else if("string"==typeof g)for(var v=0;v<t.length;v++)for(var b=0,y=Object.keys(this.s.conditions);b<y.length;b++){var C,V=y[b];if(("string"==typeof(C=this.s.conditions[V].conditionName)?C:C(this.s.dt,this.c.i18n))===t[v].text()&&V===g){this.dom.condition.prop("selectedIndex",this.dom.condition.children().toArray().indexOf(t[v][0])).removeClass(this.classes.italic),this.dom.condition.trigger("change"),v=t.length;break}}}else this.dom.condition.prop("selectedIndex",0)},w.prototype._populateData=function(){var t=this.s.dt.settings()[0].aoColumns,i=this.s.dt.columns(this.c.columns).indexes().toArray();this.dom.data.empty().append(this.dom.dataTitle);for(var e,n,s=0;s<t.length;s++)!0!==this.c.columns&&!i.includes(s)||(n={index:s,origData:(e=t[s]).data,text:(e.searchBuilderTitle||e.sTitle).replace(/(<([^>]+)>)/gi,"")},this.dom.data.append(I("<option>",{text:n.text,value:n.index}).addClass(this.classes.option).addClass(this.classes.notItalic).prop("origData",e.data).prop("selected",this.s.dataIdx===n.index)),this.s.dataIdx!==n.index)||this.dom.dataTitle.removeProp("selected")},w.prototype._populateValue=function(i){for(var t,e=this,n=this.s.filled,s=(this.s.filled=!1,setTimeout(function(){e.dom.defaultValue.remove()},50),0),o=this.dom.value;s<o.length;s++)!function(t){setTimeout(function(){void 0!==t&&t.remove()},50)}(o[s]);var r=this.dom.inputCont.children();if(1<r.length)for(t=0;t<r.length;t++)I(r[t]).remove();for(void 0!==i&&this.s.dt.columns().every(function(t){e.s.dt.settings()[0].aoColumns[t].sTitle===i.data&&(e.s.dataIdx=t)}),this.dom.value=[].concat(this.s.conditions[this.s.condition].init(this,w.updateListener,void 0!==i?i.value:void 0)),void 0!==i&&void 0!==i.value&&(this.s.value=i.value),this.dom.inputCont.empty(),void 0!==this.dom.value[0]&&I(this.dom.value[0]).appendTo(this.dom.inputCont).trigger("dtsb-inserted"),t=1;t<this.dom.value.length;t++)I(this.dom.value[t]).insertAfter(this.dom.value[t-1]).trigger("dtsb-inserted");this.s.filled=this.s.conditions[this.s.condition].isInputValid(this.dom.value,this),this.setListeners(),this.s.preventRedraw||n===this.s.filled||(this.s.dt.page.info().serverSide||this.doSearch(),this.setListeners())},w.prototype._throttle=function(n,s){var o=null,r=null,a=this;return null===(s=void 0===s?200:s)&&(s=200),function(){for(var t=[],i=0;i<arguments.length;i++)t[i]=arguments[i];var e=+new Date;null!==o&&e<o+s?clearTimeout(r):o=e,r=setTimeout(function(){o=null,n.apply(a,t)},s)}},w.version="1.1.0",w.classes={button:"dtsb-button",buttonContainer:"dtsb-buttonContainer",condition:"dtsb-condition",container:"dtsb-criteria",data:"dtsb-data",delete:"dtsb-delete",dropDown:"dtsb-dropDown",greyscale:"dtsb-greyscale",input:"dtsb-input",inputCont:"dtsb-inputCont",italic:"dtsb-italic",joiner:"dtsb-joiner",left:"dtsb-left",notItalic:"dtsb-notItalic",option:"dtsb-option",right:"dtsb-right",select:"dtsb-select",value:"dtsb-value",vertical:"dtsb-vertical"},w.initSelect=function(e,t,n,i){void 0===n&&(n=null),void 0===i&&(i=!1);for(var s=e.dom.data.children("option:selected").val(),o=e.s.dt.rows().indexes().toArray(),r=e.s.dt.settings()[0].fastData,a=(e.dom.valueTitle.prop("selected",!0),I("<select/>").addClass(w.classes.value).addClass(w.classes.dropDown).addClass(w.classes.italic).addClass(w.classes.select).append(e.dom.valueTitle).on("change.dtsb",function(){I(this).removeClass(w.classes.italic),t(e,this)})),d=(e.c.greyscale&&a.addClass(w.classes.greyscale),[]),l=[],u=0,c=o;u<c.length;u++){var h=c[u],p=r(h,s,"string"==typeof e.c.orthogonal?e.c.orthogonal:e.c.orthogonal.search),m={filter:"string"==typeof p?p.replace(/[\r\n\u2028]/g," "):p,index:h,text:r(h,s,"string"==typeof e.c.orthogonal?e.c.orthogonal:e.c.orthogonal.display)},f=("array"===e.s.type&&(m.filter=Array.isArray(m.filter)?m.filter:[m.filter],m.text=Array.isArray(m.text)?m.text:[m.text]),function(t,i){e.s.type.includes("html")&&null!==t&&"string"==typeof t&&t.replace(/(<([^>]+)>)/gi,"");t=I("<option>",{type:Array.isArray(t)?"Array":"String",value:t}).data("sbv",t).addClass(e.classes.option).addClass(e.classes.notItalic).html("string"==typeof i?i.replace(/(<([^>]+)>)/gi,""):i),i=t.val();-1===d.indexOf(i)&&(d.push(i),l.push(t),null!==n&&Array.isArray(n[0])&&(n[0]=n[0].sort().join(",")),null!==n)&&t.val()===n[0]&&(t.prop("selected",!0),a.removeClass(w.classes.italic),e.dom.valueTitle.removeProp("selected"))});if(i)for(var g=0;g<m.filter.length;g++)f(m.filter[g],m.text[g]);else f(m.filter,Array.isArray(m.text)?m.text.join(", "):m.text)}l.sort(function(t,i){return"array"===e.s.type||"string"===e.s.type||"html"===e.s.type?t.val()<i.val()?-1:t.val()>i.val()?1:0:"num"===e.s.type||"html-num"===e.s.type?+t.val().replace(/(<([^>]+)>)/gi,"")<+i.val().replace(/(<([^>]+)>)/gi,"")?-1:+t.val().replace(/(<([^>]+)>)/gi,"")>+i.val().replace(/(<([^>]+)>)/gi,"")?1:0:"num-fmt"===e.s.type||"html-num-fmt"===e.s.type?+t.val().replace(/[^0-9.]/g,"")<+i.val().replace(/[^0-9.]/g,"")?-1:+t.val().replace(/[^0-9.]/g,"")>+i.val().replace(/[^0-9.]/g,"")?1:0:void 0});for(var v=0,b=l;v<b.length;v++)a.append(b[v]);return a},w.initSelectSSP=function(t,i,e){void 0===e&&(e=null),t.dom.valueTitle.prop("selected",!0);for(var n=I("<select/>").addClass(w.classes.value).addClass(w.classes.dropDown).addClass(w.classes.italic).addClass(w.classes.select).append(t.dom.valueTitle).on("change.dtsb",function(){I(this).removeClass(w.classes.italic),i(t,this)}),s=(t.c.greyscale&&n.addClass(w.classes.greyscale),[]),o=0,r=t.s.serverData[t.s.origData];o<r.length;o++){var a=r[o],d=a.value,a=a.label;t.s.type.includes("html")&&null!==d&&"string"==typeof d&&d.replace(/(<([^>]+)>)/gi,""),d=I("<option>",{type:Array.isArray(d)?"Array":"String",value:d}).data("sbv",d).addClass(t.classes.option).addClass(t.classes.notItalic).html("string"==typeof a?a.replace(/(<([^>]+)>)/gi,""):a),s.push(d),null!==e&&d.val()===e[0]&&(d.prop("selected",!0),n.removeClass(w.classes.italic),t.dom.valueTitle.removeProp("selected"))}for(var l=0,u=s;l<u.length;l++)n.append(u[l]);return n},w.initSelectArray=function(t,i,e){return w.initSelect(t,i,e=void 0===e?null:e,!0)},w.initInput=function(i,e,t){void 0===t&&(t=null);var n=i.s.dt.settings()[0].searchDelay,n=I("<input/>").addClass(w.classes.value).addClass(w.classes.input).on("input.dtsb keypress.dtsb",i._throttle(function(t){t=t.keyCode||t.which;return e(i,this,t)},null===n?100:n));return i.c.greyscale&&n.addClass(w.classes.greyscale),null!==t&&n.val(t[0]),i.s.dt.one("draw.dtsb",function(){i.s.topGroup.trigger("dtsb-redrawLogic")}),n},w.init2Input=function(i,e,t){void 0===t&&(t=null);var n=i.s.dt.settings()[0].searchDelay,n=[I("<input/>").addClass(w.classes.value).addClass(w.classes.input).on("input.dtsb keypress.dtsb",i._throttle(function(t){t=t.keyCode||t.which;return e(i,this,t)},null===n?100:n)),I("<span>").addClass(i.classes.joiner).html(i.s.dt.i18n("searchBuilder.valueJoiner",i.c.i18n.valueJoiner)),I("<input/>").addClass(w.classes.value).addClass(w.classes.input).on("input.dtsb keypress.dtsb",i._throttle(function(t){t=t.keyCode||t.which;return e(i,this,t)},null===n?100:n))];return i.c.greyscale&&(n[0].addClass(w.classes.greyscale),n[2].addClass(w.classes.greyscale)),null!==t&&(n[0].val(t[0]),n[2].val(t[1])),i.s.dt.one("draw.dtsb",function(){i.s.topGroup.trigger("dtsb-redrawLogic")}),n},w.initDate=function(e,n,t){void 0===t&&(t=null);var s=e.s.dt.settings()[0].searchDelay,i=e.s.dt.i18n("datetime",{}),i=I("<input/>").addClass(w.classes.value).addClass(w.classes.input).dtDateTime({attachTo:"input",format:e.s.dateFormat||void 0,i18n:i}).on("change.dtsb",e._throttle(function(){return n(e,this)},null===s?100:s)).on("input.dtsb keypress.dtsb",function(i){e._throttle(function(){var t=i.keyCode||i.which;return n(e,this,t)},null===s?100:s)});return e.c.greyscale&&i.addClass(w.classes.greyscale),null!==t&&i.val(t[0]),e.s.dt.one("draw.dtsb",function(){e.s.topGroup.trigger("dtsb-redrawLogic")}),i},w.initNoValue=function(t){return t.s.dt.one("draw.dtsb",function(){t.s.topGroup.trigger("dtsb-redrawLogic")}),[]},w.init2Date=function(e,n,t){var i=this,s=(void 0===t&&(t=null),e.s.dt.settings()[0].searchDelay),o=e.s.dt.i18n("datetime",{}),o=[I("<input/>").addClass(w.classes.value).addClass(w.classes.input).dtDateTime({attachTo:"input",format:e.s.dateFormat||void 0,i18n:o}).on("change.dtsb",null!==s?f.util.throttle(function(){return n(e,this)},s):function(){n(e,i)}).on("input.dtsb keypress.dtsb",function(i){f.util.throttle(function(){var t=i.keyCode||i.which;return n(e,this,t)},null===s?0:s)}),I("<span>").addClass(e.classes.joiner).html(e.s.dt.i18n("searchBuilder.valueJoiner",e.c.i18n.valueJoiner)),I("<input/>").addClass(w.classes.value).addClass(w.classes.input).dtDateTime({attachTo:"input",format:e.s.dateFormat||void 0,i18n:o}).on("change.dtsb",null!==s?f.util.throttle(function(){return n(e,this)},s):function(){n(e,i)}).on("input.dtsb keypress.dtsb",e.c.enterSearch||void 0!==e.s.dt.settings()[0].oInit.search&&e.s.dt.settings()[0].oInit.search.return||null===s?function(t){t=t.keyCode||t.which;n(e,i,t)}:f.util.throttle(function(){return n(e,this)},s))];return e.c.greyscale&&(o[0].addClass(w.classes.greyscale),o[2].addClass(w.classes.greyscale)),null!==t&&0<t.length&&(o[0].val(t[0]),o[2].val(t[1])),e.s.dt.one("draw.dtsb",function(){e.s.topGroup.trigger("dtsb-redrawLogic")}),o},w.isInputValidSelect=function(t){for(var i=!0,e=0,n=t;e<n.length;e++){var s=n[e];s.children("option:selected").length===s.children("option").length-s.children("option."+w.classes.notItalic).length&&1===s.children("option:selected").length&&s.children("option:selected")[0]===s.children("option")[0]&&(i=!1)}return i},w.isInputValidInput=function(t){for(var i=!0,e=0,n=t;e<n.length;e++){var s=n[e];s.is("input")&&0===s.val().length&&(i=!1)}return i},w.inputValueSelect=function(t){for(var i=[],e=0,n=t;e<n.length;e++){var s=n[e];s.is("select")&&i.push(w._escapeHTML(s.children("option:selected").data("sbv")))}return i},w.inputValueInput=function(t){for(var i=[],e=0,n=t;e<n.length;e++){var s=n[e];s.is("input")&&i.push(w._escapeHTML(s.val()))}return i},w.updateListener=function(t,i,e){var n=t.s.conditions[t.s.condition];if(t.s.filled=n.isInputValid(t.dom.value,t),t.s.value=n.inputValue(t.dom.value,t),t.s.filled){for(Array.isArray(t.s.value)||(t.s.value=[t.s.value]),r=0;r<t.s.value.length;r++)Array.isArray(t.s.value[r])&&t.s.value[r].sort();for(var s=null,o=null,r=0;r<t.dom.value.length;r++)i===t.dom.value[r][0]&&(s=r,void 0!==i.selectionStart)&&(o=i.selectionStart);(t.c.enterSearch||void 0!==t.s.dt.settings()[0].oInit.search&&t.s.dt.settings()[0].oInit.search.return)&&13!==e||t.doSearch(),null!==s&&(t.dom.value[s].removeClass(t.classes.italic),t.dom.value[s].focus(),null!==o)&&t.dom.value[s][0].setSelectionRange(o,o)}else(t.c.enterSearch||void 0!==t.s.dt.settings()[0].oInit.search&&t.s.dt.settings()[0].oInit.search.return)&&13!==e||t.doSearch()},w.dateConditions={"=":{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.date.equals",i.conditions.date.equals)},init:w.initDate,inputValue:w.inputValueInput,isInputValid:w.isInputValidInput,search:function(t,i){return(t=t.replace(/(\/|-|,)/g,"-"))===i[0]}},"!=":{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.date.not",i.conditions.date.not)},init:w.initDate,inputValue:w.inputValueInput,isInputValid:w.isInputValidInput,search:function(t,i){return(t=t.replace(/(\/|-|,)/g,"-"))!==i[0]}},"<":{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.date.before",i.conditions.date.before)},init:w.initDate,inputValue:w.inputValueInput,isInputValid:w.isInputValidInput,search:function(t,i){return(t=t.replace(/(\/|-|,)/g,"-"))<i[0]}},">":{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.date.after",i.conditions.date.after)},init:w.initDate,inputValue:w.inputValueInput,isInputValid:w.isInputValidInput,search:function(t,i){return(t=t.replace(/(\/|-|,)/g,"-"))>i[0]}},between:{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.date.between",i.conditions.date.between)},init:w.init2Date,inputValue:w.inputValueInput,isInputValid:w.isInputValidInput,search:function(t,i){return t=t.replace(/(\/|-|,)/g,"-"),i[0]<i[1]?i[0]<=t&&t<=i[1]:i[1]<=t&&t<=i[0]}},"!between":{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.date.notBetween",i.conditions.date.notBetween)},init:w.init2Date,inputValue:w.inputValueInput,isInputValid:w.isInputValidInput,search:function(t,i){return t=t.replace(/(\/|-|,)/g,"-"),i[0]<i[1]?!(i[0]<=t&&t<=i[1]):!(i[1]<=t&&t<=i[0])}},null:{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.date.empty",i.conditions.date.empty)},init:w.initNoValue,inputValue:function(){},isInputValid:function(){return!0},search:function(t){return null==t||0===t.length}},"!null":{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.date.notEmpty",i.conditions.date.notEmpty)},init:w.initNoValue,inputValue:function(){},isInputValid:function(){return!0},search:function(t){return!(null==t||0===t.length)}}},w.momentDateConditions={"=":{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.date.equals",i.conditions.date.equals)},init:w.initDate,inputValue:w.inputValueInput,isInputValid:w.isInputValidInput,search:function(t,i,e){return o()(t,e.s.dateFormat).valueOf()===o()(i[0],e.s.dateFormat).valueOf()}},"!=":{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.date.not",i.conditions.date.not)},init:w.initDate,inputValue:w.inputValueInput,isInputValid:w.isInputValidInput,search:function(t,i,e){return o()(t,e.s.dateFormat).valueOf()!==o()(i[0],e.s.dateFormat).valueOf()}},"<":{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.date.before",i.conditions.date.before)},init:w.initDate,inputValue:w.inputValueInput,isInputValid:w.isInputValidInput,search:function(t,i,e){return o()(t,e.s.dateFormat).valueOf()<o()(i[0],e.s.dateFormat).valueOf()}},">":{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.date.after",i.conditions.date.after)},init:w.initDate,inputValue:w.inputValueInput,isInputValid:w.isInputValidInput,search:function(t,i,e){return o()(t,e.s.dateFormat).valueOf()>o()(i[0],e.s.dateFormat).valueOf()}},between:{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.date.between",i.conditions.date.between)},init:w.init2Date,inputValue:w.inputValueInput,isInputValid:w.isInputValidInput,search:function(t,i,e){var t=o()(t,e.s.dateFormat).valueOf(),n=o()(i[0],e.s.dateFormat).valueOf(),i=o()(i[1],e.s.dateFormat).valueOf();return n<i?n<=t&&t<=i:i<=t&&t<=n}},"!between":{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.date.notBetween",i.conditions.date.notBetween)},init:w.init2Date,inputValue:w.inputValueInput,isInputValid:w.isInputValidInput,search:function(t,i,e){var t=o()(t,e.s.dateFormat).valueOf(),n=o()(i[0],e.s.dateFormat).valueOf(),i=o()(i[1],e.s.dateFormat).valueOf();return n<i?!(+n<=+t&&+t<=+i):!(+i<=+t&&+t<=+n)}},null:{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.date.empty",i.conditions.date.empty)},init:w.initNoValue,inputValue:function(){},isInputValid:function(){return!0},search:function(t){return null==t||0===t.length}},"!null":{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.date.notEmpty",i.conditions.date.notEmpty)},init:w.initNoValue,inputValue:function(){},isInputValid:function(){return!0},search:function(t){return!(null==t||0===t.length)}}},w.luxonDateConditions={"=":{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.date.equals",i.conditions.date.equals)},init:w.initDate,inputValue:w.inputValueInput,isInputValid:w.isInputValidInput,search:function(t,i,e){return r().DateTime.fromFormat(t,e.s.dateFormat).ts===r().DateTime.fromFormat(i[0],e.s.dateFormat).ts}},"!=":{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.date.not",i.conditions.date.not)},init:w.initDate,inputValue:w.inputValueInput,isInputValid:w.isInputValidInput,search:function(t,i,e){return r().DateTime.fromFormat(t,e.s.dateFormat).ts!==r().DateTime.fromFormat(i[0],e.s.dateFormat).ts}},"<":{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.date.before",i.conditions.date.before)},init:w.initDate,inputValue:w.inputValueInput,isInputValid:w.isInputValidInput,search:function(t,i,e){return r().DateTime.fromFormat(t,e.s.dateFormat).ts<r().DateTime.fromFormat(i[0],e.s.dateFormat).ts}},">":{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.date.after",i.conditions.date.after)},init:w.initDate,inputValue:w.inputValueInput,isInputValid:w.isInputValidInput,search:function(t,i,e){return r().DateTime.fromFormat(t,e.s.dateFormat).ts>r().DateTime.fromFormat(i[0],e.s.dateFormat).ts}},between:{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.date.between",i.conditions.date.between)},init:w.init2Date,inputValue:w.inputValueInput,isInputValid:w.isInputValidInput,search:function(t,i,e){var t=r().DateTime.fromFormat(t,e.s.dateFormat).ts,n=r().DateTime.fromFormat(i[0],e.s.dateFormat).ts,i=r().DateTime.fromFormat(i[1],e.s.dateFormat).ts;return n<i?n<=t&&t<=i:i<=t&&t<=n}},"!between":{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.date.notBetween",i.conditions.date.notBetween)},init:w.init2Date,inputValue:w.inputValueInput,isInputValid:w.isInputValidInput,search:function(t,i,e){var t=r().DateTime.fromFormat(t,e.s.dateFormat).ts,n=r().DateTime.fromFormat(i[0],e.s.dateFormat).ts,i=r().DateTime.fromFormat(i[1],e.s.dateFormat).ts;return n<i?!(+n<=+t&&+t<=+i):!(+i<=+t&&+t<=+n)}},null:{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.date.empty",i.conditions.date.empty)},init:w.initNoValue,inputValue:function(){},isInputValid:function(){return!0},search:function(t){return null==t||0===t.length}},"!null":{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.date.notEmpty",i.conditions.date.notEmpty)},init:w.initNoValue,inputValue:function(){},isInputValid:function(){return!0},search:function(t){return!(null==t||0===t.length)}}},w.numConditions={"=":{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.number.equals",i.conditions.number.equals)},init:w.initSelect,inputValue:w.inputValueSelect,isInputValid:w.isInputValidSelect,search:function(t,i){return+t==+i[0]}},"!=":{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.number.not",i.conditions.number.not)},init:w.initSelect,inputValue:w.inputValueSelect,isInputValid:w.isInputValidSelect,search:function(t,i){return+t!=+i[0]}},"<":{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.number.lt",i.conditions.number.lt)},init:w.initInput,inputValue:w.inputValueInput,isInputValid:w.isInputValidInput,search:function(t,i){return+t<+i[0]}},"<=":{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.number.lte",i.conditions.number.lte)},init:w.initInput,inputValue:w.inputValueInput,isInputValid:w.isInputValidInput,search:function(t,i){return+t<=+i[0]}},">=":{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.number.gte",i.conditions.number.gte)},init:w.initInput,inputValue:w.inputValueInput,isInputValid:w.isInputValidInput,search:function(t,i){return+t>=+i[0]}},">":{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.number.gt",i.conditions.number.gt)},init:w.initInput,inputValue:w.inputValueInput,isInputValid:w.isInputValidInput,search:function(t,i){return+t>+i[0]}},between:{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.number.between",i.conditions.number.between)},init:w.init2Input,inputValue:w.inputValueInput,isInputValid:w.isInputValidInput,search:function(t,i){return+i[0]<+i[1]?+i[0]<=+t&&+t<=+i[1]:+i[1]<=+t&&+t<=+i[0]}},"!between":{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.number.notBetween",i.conditions.number.notBetween)},init:w.init2Input,inputValue:w.inputValueInput,isInputValid:w.isInputValidInput,search:function(t,i){return+i[0]<+i[1]?!(+i[0]<=+t&&+t<=+i[1]):!(+i[1]<=+t&&+t<=+i[0])}},null:{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.number.empty",i.conditions.number.empty)},init:w.initNoValue,inputValue:function(){},isInputValid:function(){return!0},search:function(t){return null==t||0===t.length}},"!null":{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.number.notEmpty",i.conditions.number.notEmpty)},init:w.initNoValue,inputValue:function(){},isInputValid:function(){return!0},search:function(t){return!(null==t||0===t.length)}}},w.numFmtConditions={"=":{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.number.equals",i.conditions.number.equals)},init:w.initSelect,inputValue:w.inputValueSelect,isInputValid:w.isInputValidSelect,search:function(t,i,e){return e.parseNumber(t)===e.parseNumber(i[0])}},"!=":{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.number.not",i.conditions.number.not)},init:w.initSelect,inputValue:w.inputValueSelect,isInputValid:w.isInputValidSelect,search:function(t,i,e){return e.parseNumber(t)!==e.parseNumber(i[0])}},"<":{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.number.lt",i.conditions.number.lt)},init:w.initInput,inputValue:w.inputValueInput,isInputValid:w.isInputValidInput,search:function(t,i,e){return e.parseNumber(t)<e.parseNumber(i[0])}},"<=":{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.number.lte",i.conditions.number.lte)},init:w.initInput,inputValue:w.inputValueInput,isInputValid:w.isInputValidInput,search:function(t,i,e){return e.parseNumber(t)<=e.parseNumber(i[0])}},">=":{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.number.gte",i.conditions.number.gte)},init:w.initInput,inputValue:w.inputValueInput,isInputValid:w.isInputValidInput,search:function(t,i,e){return e.parseNumber(t)>=e.parseNumber(i[0])}},">":{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.number.gt",i.conditions.number.gt)},init:w.initInput,inputValue:w.inputValueInput,isInputValid:w.isInputValidInput,search:function(t,i,e){return e.parseNumber(t)>e.parseNumber(i[0])}},between:{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.number.between",i.conditions.number.between)},init:w.init2Input,inputValue:w.inputValueInput,isInputValid:w.isInputValidInput,search:function(t,i,e){var t=e.parseNumber(t),n=e.parseNumber(i[0]),e=e.parseNumber(i[1]);return+n<+e?+n<=+t&&+t<=+e:+e<=+t&&+t<=+n}},"!between":{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.number.notBetween",i.conditions.number.notBetween)},init:w.init2Input,inputValue:w.inputValueInput,isInputValid:w.isInputValidInput,search:function(t,i,e){var t=e.parseNumber(t),n=e.parseNumber(i[0]),e=e.parseNumber(i[1]);return+n<+e?!(+n<=+t&&+t<=+e):!(+e<=+t&&+t<=+n)}},null:{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.number.empty",i.conditions.number.empty)},init:w.initNoValue,inputValue:function(){},isInputValid:function(){return!0},search:function(t){return null==t||0===t.length}},"!null":{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.number.notEmpty",i.conditions.number.notEmpty)},init:w.initNoValue,inputValue:function(){},isInputValid:function(){return!0},search:function(t){return!(null==t||0===t.length)}}},w.stringConditions={"=":{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.string.equals",i.conditions.string.equals)},init:w.initSelect,inputValue:w.inputValueSelect,isInputValid:w.isInputValidSelect,search:function(t,i){return t===i[0]}},"!=":{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.string.not",i.conditions.string.not)},init:w.initSelect,inputValue:w.inputValueSelect,isInputValid:w.isInputValidInput,search:function(t,i){return t!==i[0]}},starts:{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.string.startsWith",i.conditions.string.startsWith)},init:w.initInput,inputValue:w.inputValueInput,isInputValid:w.isInputValidInput,search:function(t,i){return 0===t.toLowerCase().indexOf(i[0].toLowerCase())}},"!starts":{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.string.notStartsWith",i.conditions.string.notStartsWith)},init:w.initInput,inputValue:w.inputValueInput,isInputValid:w.isInputValidInput,search:function(t,i){return 0!==t.toLowerCase().indexOf(i[0].toLowerCase())}},contains:{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.string.contains",i.conditions.string.contains)},init:w.initInput,inputValue:w.inputValueInput,isInputValid:w.isInputValidInput,search:function(t,i){return t.toLowerCase().includes(i[0].toLowerCase())}},"!contains":{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.string.notContains",i.conditions.string.notContains)},init:w.initInput,inputValue:w.inputValueInput,isInputValid:w.isInputValidInput,search:function(t,i){return!t.toLowerCase().includes(i[0].toLowerCase())}},ends:{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.string.endsWith",i.conditions.string.endsWith)},init:w.initInput,inputValue:w.inputValueInput,isInputValid:w.isInputValidInput,search:function(t,i){return t.toLowerCase().endsWith(i[0].toLowerCase())}},"!ends":{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.string.notEndsWith",i.conditions.string.notEndsWith)},init:w.initInput,inputValue:w.inputValueInput,isInputValid:w.isInputValidInput,search:function(t,i){return!t.toLowerCase().endsWith(i[0].toLowerCase())}},null:{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.string.empty",i.conditions.string.empty)},init:w.initNoValue,inputValue:function(){},isInputValid:function(){return!0},search:function(t){return null==t||0===t.length}},"!null":{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.string.notEmpty",i.conditions.string.notEmpty)},init:w.initNoValue,inputValue:function(){},isInputValid:function(){return!0},search:function(t){return!(null==t||0===t.length)}}},w.defaults={columns:!0,conditions:{array:w.arrayConditions={contains:{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.array.contains",i.conditions.array.contains)},init:w.initSelectArray,inputValue:w.inputValueSelect,isInputValid:w.isInputValidSelect,search:function(t,i){return t.includes(i[0])}},without:{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.array.without",i.conditions.array.without)},init:w.initSelectArray,inputValue:w.inputValueSelect,isInputValid:w.isInputValidSelect,search:function(t,i){return-1===t.indexOf(i[0])}},"=":{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.array.equals",i.conditions.array.equals)},init:w.initSelect,inputValue:w.inputValueSelect,isInputValid:w.isInputValidSelect,search:function(t,i){if(t.length!==i[0].length)return!1;for(var e=0;e<t.length;e++)if(t[e]!==i[0][e])return!1;return!0}},"!=":{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.array.not",i.conditions.array.not)},init:w.initSelect,inputValue:w.inputValueSelect,isInputValid:w.isInputValidSelect,search:function(t,i){if(t.length!==i[0].length)return!0;for(var e=0;e<t.length;e++)if(t[e]!==i[0][e])return!0;return!1}},null:{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.array.empty",i.conditions.array.empty)},init:w.initNoValue,inputValue:function(){},isInputValid:function(){return!0},search:function(t){return null==t||0===t.length}},"!null":{conditionName:function(t,i){return t.i18n("searchBuilder.conditions.array.notEmpty",i.conditions.array.notEmpty)},init:w.initNoValue,inputValue:function(){},isInputValid:function(){return!0},search:function(t){return null!=t&&0!==t.length}}},date:w.dateConditions,html:w.stringConditions,"html-num":w.numConditions,"html-num-fmt":w.numFmtConditions,luxon:w.luxonDateConditions,moment:w.momentDateConditions,num:w.numConditions,"num-fmt":w.numFmtConditions,string:w.stringConditions},depthLimit:!1,enterSearch:!1,filterChanged:void 0,greyscale:!1,i18n:{add:"Add Condition",button:{0:"Search Builder",_:"Search Builder (%d)"},clearAll:"Clear All",condition:"Condition",data:"Data",delete:"&times",deleteTitle:"Delete filtering rule",left:"<",leftTitle:"Outdent criteria",logicAnd:"And",logicOr:"Or",right:">",rightTitle:"Indent criteria",search:"Search",title:{0:"Custom Search Builder",_:"Custom Search Builder (%d)"},value:"Value",valueJoiner:"and"},liveSearch:!0,logic:"AND",orthogonal:{display:"display",search:"filter"},preDefined:!1},l=w,g.prototype.destroy=function(){this.dom.add.off(".dtsb"),this.dom.logic.off(".dtsb"),this.dom.search.off(".dtsb"),this.dom.container.trigger("dtsb-destroy").remove(),this.s.criteria=[]},g.prototype.getDetails=function(t){if(void 0===t&&(t=!1),0===this.s.criteria.length)return{};for(var i={criteria:[],logic:this.s.logic},e=0,n=this.s.criteria;e<n.length;e++){var s=n[e];i.criteria.push(s.criteria.getDetails(t))}return i},g.prototype.getNode=function(){return this.dom.container},g.prototype.rebuild=function(t){var i;if(!(void 0===t.criteria||null===t.criteria||Array.isArray(t.criteria)&&0===t.criteria.length)){if(this.s.logic=t.logic,this.dom.logic.children().first().html("OR"===this.s.logic?this.s.dt.i18n("searchBuilder.logicOr",this.c.i18n.logicOr):this.s.dt.i18n("searchBuilder.logicAnd",this.c.i18n.logicAnd)),Array.isArray(t.criteria))for(var e=0,n=t.criteria;e<n.length;e++)void 0!==(i=n[e]).logic?this._addPrevGroup(i):void 0===i.logic&&this._addPrevCriteria(i);for(var s=0,o=this.s.criteria;s<o.length;s++)(i=o[s]).criteria instanceof l&&(i.criteria.updateArrows(1<this.s.criteria.length),this._setCriteriaListeners(i.criteria))}},g.prototype.redrawContents=function(){if(!this.s.preventRedraw){this.dom.container.children().detach(),this.dom.container.append(this.dom.logicContainer).append(this.dom.add),this.c.liveSearch||this.dom.container.append(this.dom.search),this.s.criteria.sort(function(t,i){return t.criteria.s.index<i.criteria.s.index?-1:t.criteria.s.index>i.criteria.s.index?1:0}),this.setListeners();for(var t=0;t<this.s.criteria.length;t++){var i=this.s.criteria[t].criteria;i instanceof l?(this.s.criteria[t].index=t,this.s.criteria[t].criteria.s.index=t,this.s.criteria[t].criteria.dom.container.insertBefore(this.dom.add),this._setCriteriaListeners(i),this.s.criteria[t].criteria.s.preventRedraw=this.s.preventRedraw,this.s.criteria[t].criteria.rebuild(this.s.criteria[t].criteria.getDetails()),this.s.criteria[t].criteria.s.preventRedraw=!1):i instanceof g&&0<i.s.criteria.length?(this.s.criteria[t].index=t,this.s.criteria[t].criteria.s.index=t,this.s.criteria[t].criteria.dom.container.insertBefore(this.dom.add),i.s.preventRedraw=this.s.preventRedraw,i.redrawContents(),i.s.preventRedraw=!1,this._setGroupListeners(i)):(this.s.criteria.splice(t,1),t--)}this.setupLogic()}},g.prototype.redrawLogic=function(){for(var t=0,i=this.s.criteria;t<i.length;t++){var e=i[t];e.criteria instanceof g&&e.criteria.redrawLogic()}this.setupLogic()},g.prototype.search=function(t,i){return"AND"===this.s.logic?this._andSearch(t,i):"OR"!==this.s.logic||this._orSearch(t,i)},g.prototype.setupLogic=function(){if(this.dom.logicContainer.remove(),this.dom.clear.remove(),this.s.criteria.length<1)this.s.isChild||(this.dom.container.trigger("dtsb-destroy"),this.dom.container.css("margin-left",0)),this.dom.search.css("display","none");else{this.dom.clear.height("0px"),this.dom.logicContainer.append(this.dom.clear),this.s.isChild||this.dom.search.css("display","inline-block"),this.dom.container.prepend(this.dom.logicContainer);for(var t=0,i=this.s.criteria;t<i.length;t++){var e=i[t];e.criteria instanceof l&&e.criteria.setupButtons()}var n=this.dom.container.outerHeight()-1,n=(this.dom.logicContainer.width(n),this._setLogicListener(),this.dom.container.css("margin-left",this.dom.logicContainer.outerHeight(!0)),this.dom.logicContainer.offset()),s=n.left,s=s-(s-this.dom.container.offset().left)-this.dom.logicContainer.outerHeight(!0),s=(this.dom.logicContainer.offset({left:s}),this.dom.logicContainer.next()),n=n.top,s=a(s).offset().top;this.dom.logicContainer.offset({top:n-(n-s)}),this.dom.clear.outerHeight(this.dom.logicContainer.height()),this._setClearListener()}},g.prototype.setListeners=function(){var t=this;this.dom.add.unbind("click"),this.dom.add.on("click.dtsb",function(){return t.s.isChild||t.dom.container.prepend(t.dom.logicContainer),t.addCriteria(),t.dom.container.trigger("dtsb-add"),t.s.dt.state.save(),!1}),this.dom.search.off("click.dtsb").on("click.dtsb",function(){t.s.dt.draw()});for(var i=0,e=this.s.criteria;i<e.length;i++)e[i].criteria.setListeners();this._setClearListener(),this._setLogicListener()},g.prototype.addCriteria=function(t){for(var i=null===(t=void 0===t?null:t)?this.s.criteria.length:t.s.index,e=new l(this.s.dt,this.s.opts,this.s.topGroup,i,this.s.depth,this.s.serverData,this.c.liveSearch),n=(null!==t&&(e.c=t.c,e.s=t.s,e.s.depth=this.s.depth,e.classes=t.classes),e.populate(),!1),s=0;s<this.s.criteria.length;s++)0===s&&this.s.criteria[s].criteria.s.index>e.s.index?(e.getNode().insertBefore(this.s.criteria[s].criteria.dom.container),n=!0):s<this.s.criteria.length-1&&this.s.criteria[s].criteria.s.index<e.s.index&&this.s.criteria[s+1].criteria.s.index>e.s.index&&(e.getNode().insertAfter(this.s.criteria[s].criteria.dom.container),n=!0);n||e.getNode().insertBefore(this.dom.add),this.s.criteria.push({criteria:e,index:i}),this.s.criteria=this.s.criteria.sort(function(t,i){return t.criteria.s.index-i.criteria.s.index});for(var o=0,r=this.s.criteria;o<r.length;o++){var a=r[o];a.criteria instanceof l&&a.criteria.updateArrows(1<this.s.criteria.length)}this._setCriteriaListeners(e),e.setListeners(),this.setupLogic()},g.prototype.checkFilled=function(){for(var t=0,i=this.s.criteria;t<i.length;t++){var e=i[t];if(e.criteria instanceof l&&e.criteria.s.filled||e.criteria instanceof g&&e.criteria.checkFilled())return!0}return!1},g.prototype.count=function(){for(var t=0,i=0,e=this.s.criteria;i<e.length;i++){var n=e[i];n.criteria instanceof g?t+=n.criteria.count():t++}return t},g.prototype._addPrevGroup=function(t){var i=this.s.criteria.length,e=new g(this.s.dt,this.c,this.s.topGroup,i,!0,this.s.depth+1,this.s.serverData);this.s.criteria.push({criteria:e,index:i,logic:e.s.logic}),e.rebuild(t),this.s.criteria[i].criteria=e,this.s.topGroup.trigger("dtsb-redrawContents"),this._setGroupListeners(e)},g.prototype._addPrevCriteria=function(t){var i=this.s.criteria.length,e=new l(this.s.dt,this.s.opts,this.s.topGroup,i,this.s.depth,this.s.serverData);e.populate(),this.s.criteria.push({criteria:e,index:i}),e.s.preventRedraw=this.s.preventRedraw,e.rebuild(t),e.s.preventRedraw=!1,this.s.criteria[i].criteria=e,this.s.preventRedraw||this.s.topGroup.trigger("dtsb-redrawContents")},g.prototype._andSearch=function(t,i){if(0!==this.s.criteria.length)for(var e=0,n=this.s.criteria;e<n.length;e++){var s=n[e];if((!(s.criteria instanceof l)||s.criteria.s.filled)&&!s.criteria.search(t,i))return!1}return!0},g.prototype._orSearch=function(t,i){if(0===this.s.criteria.length)return!0;for(var e=!1,n=0,s=this.s.criteria;n<s.length;n++){var o=s[n];if(o.criteria instanceof l&&o.criteria.s.filled){if(e=!0,o.criteria.search(t,i))return!0}else if(o.criteria instanceof g&&o.criteria.checkFilled()&&(e=!0,o.criteria.search(t,i)))return!0}return!e},g.prototype._removeCriteria=function(t,i){if(void 0===i&&(i=!1),this.s.criteria.length<=1&&this.s.isChild)this.destroy();else{for(var e=void 0,n=0;n<this.s.criteria.length;n++)this.s.criteria[n].index===t.s.index&&(!i||this.s.criteria[n].criteria instanceof g)&&(e=n);for(void 0!==e&&this.s.criteria.splice(e,1),n=0;n<this.s.criteria.length;n++)this.s.criteria[n].index=n,this.s.criteria[n].criteria.s.index=n}},g.prototype._setCriteriaListeners=function(n){var s=this;n.dom.delete.unbind("click").on("click.dtsb",function(){s._removeCriteria(n),n.dom.container.remove();for(var t=0,i=s.s.criteria;t<i.length;t++){var e=i[t];e.criteria instanceof l&&e.criteria.updateArrows(1<s.s.criteria.length)}return n.destroy(),s.s.dt.draw(),s.s.topGroup.trigger("dtsb-redrawContents"),!1}),n.dom.right.unbind("click").on("click.dtsb",function(){var t=n.s.index,i=new g(s.s.dt,s.s.opts,s.s.topGroup,n.s.index,!0,s.s.depth+1,s.s.serverData);return i.addCriteria(n),s.s.criteria[t].criteria=i,s.s.criteria[t].logic="AND",s.s.topGroup.trigger("dtsb-redrawContents"),s._setGroupListeners(i),!1}),n.dom.left.unbind("click").on("click.dtsb",function(){s.s.toDrop=new l(s.s.dt,s.s.opts,s.s.topGroup,n.s.index,void 0,s.s.serverData),s.s.toDrop.s=n.s,s.s.toDrop.c=n.c,s.s.toDrop.classes=n.classes,s.s.toDrop.populate();var t=s.s.toDrop.s.index;return s.dom.container.trigger("dtsb-dropCriteria"),n.s.index=t,s._removeCriteria(n),s.s.topGroup.trigger("dtsb-redrawContents"),s.s.dt.draw(),!1})},g.prototype._setClearListener=function(){var t=this;this.dom.clear.unbind("click").on("click.dtsb",function(){return t.s.isChild?(t.destroy(),t.s.topGroup.trigger("dtsb-redrawContents")):t.dom.container.trigger("dtsb-clearContents"),!1})},g.prototype._setGroupListeners=function(i){var e=this;i.dom.add.unbind("click").on("click.dtsb",function(){return e.setupLogic(),e.dom.container.trigger("dtsb-add"),!1}),i.dom.container.unbind("dtsb-add").on("dtsb-add.dtsb",function(){return e.setupLogic(),e.dom.container.trigger("dtsb-add"),!1}),i.dom.container.unbind("dtsb-destroy").on("dtsb-destroy.dtsb",function(){return e._removeCriteria(i,!0),i.dom.container.remove(),e.setupLogic(),!1}),i.dom.container.unbind("dtsb-dropCriteria").on("dtsb-dropCriteria.dtsb",function(){var t=i.s.toDrop;return t.s.index=i.s.index,t.updateArrows(1<e.s.criteria.length),e.addCriteria(t),!1}),i.setListeners()},g.prototype._setup=function(){this.setListeners(),this.dom.add.html(this.s.dt.i18n("searchBuilder.add",this.c.i18n.add)),this.dom.search.html(this.s.dt.i18n("searchBuilder.search",this.c.i18n.search)),this.dom.logic.children().first().html("OR"===this.c.logic?this.s.dt.i18n("searchBuilder.logicOr",this.c.i18n.logicOr):this.s.dt.i18n("searchBuilder.logicAnd",this.c.i18n.logicAnd)),this.s.logic="OR"===this.c.logic?"OR":"AND",this.c.greyscale&&this.dom.logic.addClass(this.classes.greyscale),this.dom.logicContainer.append(this.dom.logic).append(this.dom.clear),this.s.isChild&&this.dom.container.append(this.dom.logicContainer),this.dom.container.append(this.dom.add),this.c.liveSearch||this.dom.container.append(this.dom.search)},g.prototype._setLogicListener=function(){var e=this;this.dom.logic.unbind("click").on("click.dtsb",function(){e._toggleLogic(),e.s.dt.draw();for(var t=0,i=e.s.criteria;t<i.length;t++)i[t].criteria.setListeners()})},g.prototype._toggleLogic=function(){"OR"===this.s.logic?(this.s.logic="AND",this.dom.logic.children().first().html(this.s.dt.i18n("searchBuilder.logicAnd",this.c.i18n.logicAnd))):"AND"===this.s.logic&&(this.s.logic="OR",this.dom.logic.children().first().html(this.s.dt.i18n("searchBuilder.logicOr",this.c.i18n.logicOr)))},g.version="1.1.0",g.classes={add:"dtsb-add",button:"dtsb-button",clearGroup:"dtsb-clearGroup",greyscale:"dtsb-greyscale",group:"dtsb-group",inputButton:"dtsb-iptbtn",logic:"dtsb-logic",logicContainer:"dtsb-logicContainer",search:"dtsb-search"},g.defaults={columns:!0,conditions:{date:l.dateConditions,html:l.stringConditions,"html-num":l.numConditions,"html-num-fmt":l.numFmtConditions,luxon:l.luxonDateConditions,moment:l.momentDateConditions,num:l.numConditions,"num-fmt":l.numFmtConditions,string:l.stringConditions},depthLimit:!1,enterSearch:!1,filterChanged:void 0,greyscale:!1,liveSearch:!0,i18n:{add:"Add Condition",button:{0:"Search Builder",_:"Search Builder (%d)"},clearAll:"Clear All",condition:"Condition",data:"Data",delete:"&times",deleteTitle:"Delete filtering rule",left:"<",leftTitle:"Outdent criteria",logicAnd:"And",logicOr:"Or",right:">",rightTitle:"Indent criteria",search:"Search",title:{0:"Custom Search Builder",_:"Custom Search Builder (%d)"},value:"Value",valueJoiner:"and"},logic:"AND",orthogonal:{display:"display",search:"filter"},preDefined:!1},m=g,n.prototype.getDetails=function(t){return this.s.topGroup.getDetails(t=void 0===t?!1:t)},n.prototype.getNode=function(){return this.dom.container},n.prototype.rebuild=function(t){return this.dom.clearAll.click(),null!=t&&(this.s.topGroup.s.preventRedraw=!0,this.s.topGroup.rebuild(t),this.s.topGroup.s.preventRedraw=!1,this._checkClear(),this._updateTitle(this.s.topGroup.count()),this.s.topGroup.redrawContents(),this.s.dt.draw(!1),this.s.topGroup.setListeners()),this},n.prototype._applyPreDefDefaults=function(t){for(var e=this,n=(void 0!==t.criteria&&void 0===t.logic&&(t.logic="AND"),this),i=0,s=t.criteria;i<s.length;i++)!function(i){void 0!==i.criteria?i=n._applyPreDefDefaults(i):n.s.dt.columns().every(function(t){e.s.dt.settings()[0].aoColumns[t].sTitle===i.data&&(i.dataIdx=t)})}(s[i]);return t},n.prototype._setUp=function(t){var n=this;if(void 0===t&&(t=!0),"function"!=typeof this.s.dt.column().type&&f.Api.registerPlural("columns().types()","column().type()",function(){return this.iterator("column",function(t,i){return t.aoColumns[i].sType},1)}),!p.DateTime){if(void 0===(i=this.s.dt.columns().types().toArray())||i.includes(void 0)||i.includes(null))for(var i=[],e=0,s=this.s.dt.settings()[0].aoColumns;e<s.length;e++){var o=s[e];i.push(void 0!==o.searchBuilderType?o.searchBuilderType:o.sType)}var r=this.s.dt.columns().toArray();(void 0===i||i.includes(void 0)||i.includes(null))&&(h.fn.dataTable.ext.oApi&&h.fn.dataTable.ext.oApi._fnColumnTypes(this.s.dt.settings()[0]),i=this.s.dt.columns().types().toArray());for(var a=0;a<r[0].length;a++){var d=i[r[0][a]];if((!0===this.c.columns||Array.isArray(this.c.columns)&&this.c.columns.includes(a))&&(d.includes("date")||d.includes("moment")||d.includes("luxon")))throw alert("SearchBuilder Requires DateTime when used with dates."),new Error("SearchBuilder requires DateTime")}}this.s.topGroup=new m(this.s.dt,this.c,void 0,void 0,void 0,void 0,this.s.serverData),this._setClearListener(),this.s.dt.on("stateSaveParams.dtsb",function(t,i,e){e.searchBuilder=n.getDetails(),e.scroller?e.start=n.s.dt.state().start:e.page=n.s.dt.page()}),this.s.dt.on("stateLoadParams.dtsb",function(t,i,e){n.rebuild(e.searchBuilder)}),this._build(),this.s.dt.on("preXhr.dtsb",function(t,i,e){n.s.dt.page.info().serverSide&&(e.searchBuilder=n._collapseArray(n.getDetails(!0)))}),this.s.dt.on(p.versionCheck("2")?"columns-reordered":"column-reorder",function(){n.rebuild(n.getDetails())}),t&&(null!==(t=this.s.dt.state.loaded())&&void 0!==t.searchBuilder?(this.s.topGroup.rebuild(t.searchBuilder),this.s.topGroup.dom.container.trigger("dtsb-redrawContents"),this.s.dt.page.info().serverSide||(t.page?this.s.dt.page(t.page).draw("page"):this.s.dt.scroller&&t.scroller&&this.s.dt.scroller().scrollToRow(t.scroller.topRow)),this.s.topGroup.setListeners()):!1!==this.c.preDefined&&(this.c.preDefined=this._applyPreDefDefaults(this.c.preDefined),this.rebuild(this.c.preDefined))),this._setEmptyListener(),this.s.dt.state.save()},n.prototype._collapseArray=function(t){if(void 0===t.logic)void 0!==t.value&&(t.value.sort(function(t,i){return isNaN(+t)||(t=+t,i=+i),t<i?-1:i<t?1:0}),t.value1=t.value[0],t.value2=t.value[1]);else for(var i=0;i<t.criteria.length;i++)t.criteria[i]=this._collapseArray(t.criteria[i]);return t},n.prototype._updateTitle=function(t){this.dom.title.html(this.s.dt.i18n("searchBuilder.title",this.c.i18n.title,t))},n.prototype._build=function(){var n=this,t=(this.dom.clearAll.remove(),this.dom.container.empty(),this.s.topGroup.count()),s=(this._updateTitle(t),this.dom.titleRow.append(this.dom.title),this.dom.container.append(this.dom.titleRow),this.dom.topGroup=this.s.topGroup.getNode(),this.dom.container.append(this.dom.topGroup),this._setRedrawListener(),this.s.dt.table(0).node());h.fn.dataTable.ext.search.includes(this.s.search)||(this.s.search=function(t,i,e){return t.nTable!==s||n.s.topGroup.search(i,e)},h.fn.dataTable.ext.search.push(this.s.search)),this.s.dt.on("destroy.dtsb",function(){n.dom.container.remove(),n.dom.clearAll.remove();for(var t=h.fn.dataTable.ext.search.indexOf(n.s.search);-1!==t;)h.fn.dataTable.ext.search.splice(t,1),t=h.fn.dataTable.ext.search.indexOf(n.s.search);n.s.dt.off(".dtsb"),h(n.s.dt.table().node()).off(".dtsb")})},n.prototype._checkClear=function(){0<this.s.topGroup.s.criteria.length?(this.dom.clearAll.insertAfter(this.dom.title),this._setClearListener()):this.dom.clearAll.remove()},n.prototype._filterChanged=function(t){var i=this.c.filterChanged;"function"==typeof i&&i(t,this.s.dt.i18n("searchBuilder.button",this.c.i18n.button,t))},n.prototype._setClearListener=function(){var t=this;this.dom.clearAll.unbind("click"),this.dom.clearAll.on("click.dtsb",function(){return t.s.topGroup=new m(t.s.dt,t.c,void 0,void 0,void 0,void 0,t.s.serverData),t._build(),t.s.dt.draw(),t.s.topGroup.setListeners(),t.dom.clearAll.remove(),t._setEmptyListener(),t._filterChanged(0),!1})},n.prototype._setRedrawListener=function(){var i=this;this.s.topGroup.dom.container.unbind("dtsb-redrawContents"),this.s.topGroup.dom.container.on("dtsb-redrawContents.dtsb",function(){i._checkClear(),i.s.topGroup.redrawContents(),i.s.topGroup.setupLogic(),i._setEmptyListener();var t=i.s.topGroup.count();i._updateTitle(t),i._filterChanged(t),i.s.dt.page.info().serverSide||i.s.dt.draw(),i.s.dt.state.save()}),this.s.topGroup.dom.container.unbind("dtsb-redrawContents-noDraw"),this.s.topGroup.dom.container.on("dtsb-redrawContents-noDraw.dtsb",function(){i._checkClear(),i.s.topGroup.s.preventRedraw=!0,i.s.topGroup.redrawContents(),i.s.topGroup.s.preventRedraw=!1,i.s.topGroup.setupLogic(),i._setEmptyListener();var t=i.s.topGroup.count();i._updateTitle(t),i._filterChanged(t)}),this.s.topGroup.dom.container.unbind("dtsb-redrawLogic"),this.s.topGroup.dom.container.on("dtsb-redrawLogic.dtsb",function(){i.s.topGroup.redrawLogic();var t=i.s.topGroup.count();i._updateTitle(t),i._filterChanged(t)}),this.s.topGroup.dom.container.unbind("dtsb-add"),this.s.topGroup.dom.container.on("dtsb-add.dtsb",function(){var t=i.s.topGroup.count();i._updateTitle(t),i._filterChanged(t),i._checkClear()}),this.s.dt.on("postEdit.dtsb postCreate.dtsb postRemove.dtsb",function(){i.s.topGroup.redrawContents()}),this.s.topGroup.dom.container.unbind("dtsb-clearContents"),this.s.topGroup.dom.container.on("dtsb-clearContents.dtsb",function(){i._setUp(!1),i._filterChanged(0),i.s.dt.draw()})},n.prototype._setEmptyListener=function(){var t=this;this.s.topGroup.dom.add.on("click.dtsb",function(){t._checkClear()}),this.s.topGroup.dom.container.on("dtsb-destroy.dtsb",function(){t.dom.clearAll.remove()})},n.version="1.7.1",n.classes={button:"dtsb-button",clearAll:"dtsb-clearAll",container:"dtsb-searchBuilder",inputButton:"dtsb-iptbtn",title:"dtsb-title",titleRow:"dtsb-titleRow"},n.defaults={columns:!0,conditions:{date:l.dateConditions,html:l.stringConditions,"html-num":l.numConditions,"html-num-fmt":l.numFmtConditions,luxon:l.luxonDateConditions,moment:l.momentDateConditions,num:l.numConditions,"num-fmt":l.numFmtConditions,string:l.stringConditions},depthLimit:!1,enterSearch:!1,filterChanged:void 0,greyscale:!1,liveSearch:!0,i18n:{add:"Add Condition",button:{0:"Search Builder",_:"Search Builder (%d)"},clearAll:"Clear All",condition:"Condition",conditions:{array:{contains:"Contains",empty:"Empty",equals:"Equals",not:"Not",notEmpty:"Not Empty",without:"Without"},date:{after:"After",before:"Before",between:"Between",empty:"Empty",equals:"Equals",not:"Not",notBetween:"Not Between",notEmpty:"Not Empty"},number:{between:"Between",empty:"Empty",equals:"Equals",gt:"Greater Than",gte:"Greater Than Equal To",lt:"Less Than",lte:"Less Than Equal To",not:"Not",notBetween:"Not Between",notEmpty:"Not Empty"},string:{contains:"Contains",empty:"Empty",endsWith:"Ends With",equals:"Equals",not:"Not",notContains:"Does Not Contain",notEmpty:"Not Empty",notEndsWith:"Does Not End With",notStartsWith:"Does Not Start With",startsWith:"Starts With"}},data:"Data",delete:"&times",deleteTitle:"Delete filtering rule",left:"<",leftTitle:"Outdent criteria",logicAnd:"And",logicOr:"Or",right:">",rightTitle:"Indent criteria",search:"Search",title:{0:"Custom Search Builder",_:"Custom Search Builder (%d)"},value:"Value",valueJoiner:"and"},logic:"AND",orthogonal:{display:"display",search:"filter"},preDefined:!1},e=n,p=(h=s).fn.DataTable,d=(a=s).fn.dataTable,c=(I=s).fn.dataTable,i=s.fn.dataTable,f.SearchBuilder=e,i.SearchBuilder=e,f.Group=m,i.Group=m,f.Criteria=l,i.Criteria=l,i=f.Api.register,f.ext.searchBuilder={conditions:{}},f.ext.buttons.searchBuilder={action:function(t,i,e,n){this.popover(n._searchBuilder.getNode(),{align:"container",span:"container"});n=n._searchBuilder.s.topGroup;void 0!==n&&n.dom.container.trigger("dtsb-redrawContents-noDraw"),0===n.s.criteria.length&&s("."+s.fn.dataTable.Group.classes.add.replace(/ /g,".")).click()},config:{},init:function(e,n,t){var i=new f.SearchBuilder(e,s.extend({filterChanged:function(t,i){e.button(n).text(i)}},t.config));e.button(n).text(t.text||e.i18n("searchBuilder.button",i.c.i18n.button,0)),t._searchBuilder=i},text:null},i("searchBuilder.getDetails()",function(t){void 0===t&&(t=!1);var i=this.context[0];return i._searchBuilder?i._searchBuilder.getDetails(t):null}),i("searchBuilder.rebuild()",function(t){var i=this.context[0];return void 0===i._searchBuilder?null:(i._searchBuilder.rebuild(t),this)}),i("searchBuilder.container()",function(){var t=this.context[0];return t._searchBuilder?t._searchBuilder.getNode():null}),s(t).on("preInit.dt.dtsp",function(t,i){"dt"!==t.namespace||!i.oInit.searchBuilder&&!f.defaults.searchBuilder||i._searchBuilder||v(i)}),f.ext.feature.push({cFeature:"Q",fnInit:v}),f.feature&&f.feature.register("searchBuilder",v),f});

/*! Bootstrap 5 ui integration for DataTables' SearchBuilder
 * © SpryMedia Ltd - datatables.net/license
 */
!function(n){var d,r;"function"==typeof define&&define.amd?define(["jquery","datatables.net-bs5","datatables.net-searchbuilder"],function(e){return n(e,window,document)}):"object"==typeof exports?(d=require("jquery"),r=function(e,t){t.fn.dataTable||require("datatables.net-bs5")(e,t),t.fn.dataTable.SearchBuilder||require("datatables.net-searchbuilder")(e,t)},"undefined"==typeof window?module.exports=function(e,t){return e=e||window,t=t||d(e),r(e,t),n(t,0,e.document)}:(r(window,d),module.exports=n(d,window,window.document))):n(jQuery,window,document)}(function(e,t,n){"use strict";var d=e.fn.dataTable;return e.extend(!0,d.SearchBuilder.classes,{clearAll:"btn btn-secondary dtsb-clearAll"}),e.extend(!0,d.Group.classes,{add:"btn btn-secondary dtsb-add",clearGroup:"btn btn-secondary dtsb-clearGroup",logic:"btn btn-secondary dtsb-logic",search:"btn btn-secondary dtsb-search"}),e.extend(!0,d.Criteria.classes,{condition:"form-select dtsb-condition",data:"dtsb-data form-select",delete:"btn btn-secondary dtsb-delete",input:"form-control dtsb-input",left:"btn btn-secondary dtsb-left",right:"btn btn-secondary dtsb-right",select:"form-select",value:"dtsb-value"}),d});

/*! SearchPanes 2.3.1
 * © SpryMedia Ltd - datatables.net/license
 */
!function(e){var a,i;"function"==typeof define&&define.amd?define(["jquery","datatables.net"],function(t){return e(t,window,document)}):"object"==typeof exports?(a=require("jquery"),i=function(t,s){s.fn.dataTable||require("datatables.net")(t,s)},"undefined"==typeof window?module.exports=function(t,s){return t=t||window,s=s||a(t),i(t,s),e(s,t,t.document)}:(i(window,a),module.exports=e(a,window,window.document))):e(jQuery,window,document)}(function(o,r,n){"use strict";var _,l,h,a,d,i,c,t,p,u,f,g,v,m,w,P,y,b,C,S,O,x,j,A,D,T=o.fn.dataTable;function B(t,s,e,a,i){var n,o=this;if(void 0===i&&(i=null),!l||!l.versionCheck||!l.versionCheck("1.10.0"))throw new Error("SearchPane requires DataTables 1.10 or newer");if(l.select)return t=new l.Api(t),this.classes=_.extend(!0,{},B.classes),this.c=_.extend(!0,{},B.defaults,s,i),s&&s.hideCount&&void 0===s.viewCount&&(this.c.viewCount=!this.c.hideCount),s=t.columns().eq(0).toArray().length,this.s={colExists:e<s,colOpts:void 0,customPaneSettings:i,displayed:!1,dt:t,dtPane:void 0,firstSet:!0,index:e,indexes:[],listSet:!1,name:void 0,rowData:{arrayFilter:[],arrayOriginal:[],bins:{},binsOriginal:{},filterMap:new Map,totalOptions:0},scrollTop:0,searchFunction:void 0,selections:[],serverSelect:[],serverSelecting:!1,tableLength:null,updating:!1},this.s.colOpts=this.s.colExists?this._getOptions():this._getBonusOptions(),this.dom={buttonGroup:_("<div/>").addClass(this.classes.buttonGroup),clear:_('<button type="button">&#215;</button>').attr("disabled","true").addClass(this.classes.disabledButton).addClass(this.classes.paneButton).addClass(this.classes.clearButton).html(this.s.dt.i18n("searchPanes.clearPane",this.c.i18n.clearPane)),collapseButton:_('<button type="button"><span class="'+this.classes.caret+'">&#x5e;</span></button>').addClass(this.classes.paneButton).addClass(this.classes.collapseButton),container:_("<div/>").addClass(this.classes.container).addClass(this.s.colOpts.className).addClass(this.classes.layout+(parseInt(this.c.layout.split("-")[1],10)<10?this.c.layout:this.c.layout.split("-")[0]+"-9")).addClass(this.s.customPaneSettings&&this.s.customPaneSettings.className?this.s.customPaneSettings.className:""),countButton:_('<button type="button"><span></span></button>').addClass(this.classes.paneButton).addClass(this.classes.countButton),dtP:_('<table width="100%"><thead><tr><th></th><th></th></tr></thead></table>'),lower:_("<div/>").addClass(this.classes.subRow2).addClass(this.classes.narrowButton),nameButton:_('<button type="button"><span></span></button>').addClass(this.classes.paneButton).addClass(this.classes.nameButton),panesContainer:_(a),searchBox:_("<input/>").addClass(this.classes.paneInputButton).addClass(this.classes.search),searchButton:_('<button type="button"><span></span></button>').addClass(this.classes.searchIcon).addClass(this.classes.paneButton),searchCont:_("<div/>").addClass(this.classes.searchCont),searchLabelCont:_("<div/>").addClass(this.classes.searchLabelCont),topRow:_("<div/>").addClass(this.classes.topRow),upper:_("<div/>").addClass(this.classes.subRow1).addClass(this.classes.narrowSearch)},s="",this.s.colExists?(s=_(this.s.dt.column(this.s.index).header()).text(),this.dom.dtP.find("th").eq(0).text(s)):(s=this.s.customPaneSettings.header||"Custom Pane",this.dom.dtP.find("th").eq(0).html(s)),this.s.colOpts.name?this.s.name=this.s.colOpts.name:this.s.customPaneSettings&&this.s.customPaneSettings.name?this.s.name=this.s.customPaneSettings.name:this.s.name=s,n=this.s.dt.table(0).node(),this.s.searchFunction=function(t,s,e){return 0===o.s.selections.length||t.nTable!==n||(t=null,o.s.colExists&&(t=s[o.s.index],"filter"!==o.s.colOpts.orthogonal.filter)&&(t=o.s.rowData.filterMap.get(e))instanceof _.fn.dataTable.Api&&(t=t.toArray()),o._search(t,e))},_.fn.dataTable.ext.search.push(this.s.searchFunction),this.c.clear&&this.dom.clear.on("click.dtsp",function(){o.dom.container.find("."+o.classes.search.replace(/\s+/g,".")).each(function(){_(this).val("").trigger("input")}),o.clearPane()}),this.s.dt.on("draw.dtsp",function(){return o.adjustTopRow()}),this.s.dt.on("buttons-action.dtsp",function(){return o.adjustTopRow()}),this.s.dt.on("column-reorder.dtsp",function(t,s,e){o.s.index=e.mapping[o.s.index]}),this;throw new Error("SearchPane requires Select")}function s(t,s,e,a,i){return d.call(this,t,s,e,a,i)||this}function e(t,s,e,a,i){return p.call(this,t,c.extend({i18n:{countFiltered:"{shown} ({total})"}},s),e,a,i)||this}function L(t,s,e,a,i){return v.call(this,t,f.extend({i18n:{count:"{shown}"}},s),e,a,i)||this}function R(t,s,e,a,i){return y.call(this,t,w.extend({i18n:{count:"{total}",countFiltered:"{shown} ({total})"}},s),e,a,i)||this}function k(t,s,e,a){var l=this;if(void 0===e&&(e=!1),void 0===a&&(a=h),!C||!C.versionCheck||!C.versionCheck("1.10.0"))throw new Error("SearchPane requires DataTables 1.10 or newer");if(!C.select)throw new Error("SearchPane requires Select");var d,i=new C.Api(t);if(this.classes=b.extend(!0,{},k.classes),this.c=b.extend(!0,{},k.defaults,s),this.dom={clearAll:b('<button type="button"/>').addClass(this.classes.clearAll).html(i.i18n("searchPanes.clearMessage",this.c.i18n.clearMessage)),collapseAll:b('<button type="button"/>').addClass(this.classes.collapseAll).html(i.i18n("searchPanes.collapseMessage",this.c.i18n.collapseMessage)),container:b("<div/>").addClass(this.classes.panes).html(i.i18n("searchPanes.loadMessage",this.c.i18n.loadMessage)),emptyMessage:b("<div/>").addClass(this.classes.emptyMessage),panes:b("<div/>").addClass(this.classes.container),showAll:b('<button type="button"/>').addClass(this.classes.showAll).addClass(this.classes.disabledButton).attr("disabled","true").html(i.i18n("searchPanes.showMessage",this.c.i18n.showMessage)),title:b("<div/>").addClass(this.classes.title),titleRow:b("<div/>").addClass(this.classes.titleRow)},this.s={colOpts:[],dt:i,filterCount:0,minPaneWidth:260,page:0,paging:!1,pagingST:!1,paneClass:a,panes:[],selectionList:[],serverData:{},stateRead:!1,updating:!1},!i.settings()[0]._searchPanes)return b(n).on("draw.dt",function(t){l.dom.container.find(t.target).length&&l._updateFilterCount()}),this._getState(),this.s.dt.page.info().serverSide&&(d=this.s.dt.settings()[0],this.s.dt.on("preXhr.dtsps",function(t,s,e){if(d===s){void 0===e.searchPanes&&(e.searchPanes={}),void 0===e.searchPanes_null&&(e.searchPanes_null={});for(var a=0,i=l.s.selectionList;a<i.length;a++){var n=i[a],o=l.s.dt.column(n.column).dataSrc();void 0===e.searchPanes[o]&&(e.searchPanes[o]={}),void 0===e.searchPanes_null[o]&&(e.searchPanes_null[o]={});for(var r=0;r<n.rows.length;r++)e.searchPanes[o][r]=n.rows[r],null===e.searchPanes[o][r]?e.searchPanes_null[o][r]=!0:e.searchPanes_null[o][r]=!1}0<l.s.selectionList.length&&(e.searchPanesLast=o),e.searchPanes_options={cascade:l.c.cascadePanes,viewCount:l.c.viewCount,viewTotal:l.c.viewTotal}}})),this._setXHR(),(i.settings()[0]._searchPanes=this).s.dt.settings()[0]._bInitComplete||e?this._paneDeclare(i,t,s):i.one("preInit.dtsps",function(){l._paneDeclare(i,t,s)}),this}function M(t,s,e){function a(){return n._initSelectionListeners(!0,o&&o.searchPanes&&o.searchPanes.selectionList?o.searchPanes.selectionList:n.c.preSelect)}var i,n=this,t=(s.cascadePanes&&s.viewTotal?i=S:s.cascadePanes?i=P:s.viewTotal&&(i=g),(n=j.call(this,t,s,e=void 0===e?!1:e,i)||this).s.dt),o=t.state.loaded();return t.settings()[0]._bInitComplete?a():t.off("init.dtsps").on("init.dtsps",a),n}function N(s,e,t){var a=o.extend({filterChanged:function(t){s.button(e).text(s.i18n("searchPanes.collapse",(void 0!==s.context[0].oLanguage.searchPanes?s.context[0].oLanguage.searchPanes:s.context[0]._searchPanes.c.i18n).collapse,t))}},t.config),a=new(a&&(a.cascadePanes||a.viewTotal)?T.SearchPanesST:T.SearchPanes)(s,a);s.button(e).text(t.text||s.i18n("searchPanes.collapse",a.c.i18n.collapse,0)),t._panes=a}function F(t,s,e){void 0===s&&(s=null),void 0===e&&(e=!1);t=new D.Api(t),s=s||t.init().searchPanes||D.defaults.searchPanes;return new(s&&(s.cascadePanes||s.viewTotal)?A:O)(t,s,e).getNode()}return B.prototype.addRow=function(t,s,e,a,i,n,o){var r;n=n||this.s.rowData.bins[s]||0,o=o||this._getShown(s);for(var l=0,d=this.s.indexes;l<d.length;l++){var h=d[l];h.filter===s&&(r=h.index)}return void 0===r&&(r=this.s.indexes.length,this.s.indexes.push({filter:s,index:r})),this.s.dtPane.row.add({className:i,display:""!==t?t:this.emptyMessage(),filter:s,index:r,shown:o,sort:e,total:n,type:a})},B.prototype.adjustTopRow=function(){var t=this.dom.container.find("."+this.classes.subRowsContainer.replace(/\s+/g,".")),s=this.dom.container.find("."+this.classes.subRow1.replace(/\s+/g,".")),e=this.dom.container.find("."+this.classes.subRow2.replace(/\s+/g,".")),a=this.dom.container.find("."+this.classes.topRow.replace(/\s+/g,"."));(_(t[0]).width()<252||_(a[0]).width()<252)&&0!==_(t[0]).width()?(_(t[0]).addClass(this.classes.narrow),_(s[0]).addClass(this.classes.narrowSub).removeClass(this.classes.narrowSearch),_(e[0]).addClass(this.classes.narrowSub).removeClass(this.classes.narrowButton)):(_(t[0]).removeClass(this.classes.narrow),_(s[0]).removeClass(this.classes.narrowSub).addClass(this.classes.narrowSearch),_(e[0]).removeClass(this.classes.narrowSub).addClass(this.classes.narrowButton))},B.prototype.clearData=function(){this.s.rowData={arrayFilter:[],arrayOriginal:[],bins:{},binsOriginal:{},filterMap:new Map,totalOptions:0}},B.prototype.clearPane=function(){return this.s.dtPane.rows({selected:!0}).deselect(),this.updateTable(),this},B.prototype.collapse=function(){var t=this;this.s.displayed&&(this.c.collapse||!0===this.s.colOpts.collapse)&&!1!==this.s.colOpts.collapse&&(_(this.s.dtPane.table().container()).addClass(this.classes.hidden),this.dom.topRow.addClass(this.classes.bordered),this.dom.nameButton.addClass(this.classes.disabledButton),this.dom.countButton.addClass(this.classes.disabledButton),this.dom.searchButton.addClass(this.classes.disabledButton),this.dom.collapseButton.addClass(this.classes.rotated),this.dom.topRow.one("click.dtsp",function(){return t.show()}),this.dom.topRow.trigger("collapse.dtsps"))},B.prototype.destroy=function(){this.s.dtPane&&this.s.dtPane.off(".dtsp"),this.s.dt.off(".dtsp"),this.dom.clear.off(".dtsp"),this.dom.nameButton.off(".dtsp"),this.dom.countButton.off(".dtsp"),this.dom.searchButton.off(".dtsp"),this.dom.collapseButton.off(".dtsp"),_(this.s.dt.table().node()).off(".dtsp"),this.dom.container.detach();for(var t=_.fn.dataTable.ext.search.indexOf(this.s.searchFunction);-1!==t;)_.fn.dataTable.ext.search.splice(t,1),t=_.fn.dataTable.ext.search.indexOf(this.s.searchFunction);this.s.dtPane&&this.s.dtPane.destroy(),this.s.listSet=!1},B.prototype.emptyMessage=function(){var t=this.c.i18n.emptyMessage;return this.c.emptyMessage&&(t=this.c.emptyMessage),!1!==this.s.colOpts.emptyMessage&&null!==this.s.colOpts.emptyMessage&&(t=this.s.colOpts.emptyMessage),this.s.dt.i18n("searchPanes.emptyMessage",t)},B.prototype.getPaneCount=function(){return this.s.dtPane?this.s.dtPane.rows({selected:!0}).data().toArray().length:0},B.prototype.rebuildPane=function(t,s){void 0===t&&(t=null),void 0===s&&(s=!1),this.clearData();var e=[],a=(this.s.serverSelect=[],null);return this.s.dtPane&&(s&&(this.s.dt.page.info().serverSide?this.s.serverSelect=this.s.dtPane.rows({selected:!0}).data().toArray():e=this.s.dtPane.rows({selected:!0}).data().toArray()),this.s.dtPane.clear().destroy(),a=this.dom.container.prev(),this.destroy(),this.s.dtPane=void 0,_.fn.dataTable.ext.search.push(this.s.searchFunction)),this.dom.container.removeClass(this.classes.hidden),this.s.displayed=!1,this._buildPane(this.s.dt.page.info().serverSide?this.s.serverSelect:e,t,a),this},B.prototype.resize=function(t){this.c.layout=t,this.dom.container.removeClass().addClass(this.classes.show).addClass(this.classes.container).addClass(this.s.colOpts.className).addClass(this.classes.layout+(parseInt(t.split("-")[1],10)<10?t:t.split("-")[0]+"-9")).addClass(null!==this.s.customPaneSettings&&this.s.customPaneSettings.className?this.s.customPaneSettings.className:""),this.adjustTopRow()},B.prototype.setListeners=function(){var h=this;this.s.dtPane&&(this.s.dtPane.off("select.dtsp").on("select.dtsp",function(){clearTimeout(h.s.deselectTimeout),h._updateSelection(!h.s.updating),h.dom.clear.removeClass(h.classes.disabledButton).removeAttr("disabled")}),this.s.dtPane.off("deselect.dtsp").on("deselect.dtsp",function(){h.s.deselectTimeout=setTimeout(function(){h._updateSelection(!0),0===h.s.dtPane.rows({selected:!0}).data().toArray().length&&h.dom.clear.addClass(h.classes.disabledButton).attr("disabled","true")},50)}),this.s.firstSet&&(this.s.firstSet=!1,this.s.dt.on("stateSaveParams.dtsp",function(t,s,e){if(_.isEmptyObject(e))h.s.dtPane.state.clear();else{var a,i,n,o,r,l=[];h.s.dtPane&&(l=h.s.dtPane.rows({selected:!0}).data().map(function(t){return null!==t.filter?t.filter.toString():null}).toArray(),o=h.dom.searchBox.val(),i=h.s.dtPane.order(),a=h.s.rowData.binsOriginal,r=h.s.rowData.arrayOriginal,n=h.dom.collapseButton.hasClass(h.classes.rotated)),void 0===e.searchPanes&&(e.searchPanes={}),void 0===e.searchPanes.panes&&(e.searchPanes.panes=[]);for(var d=0;d<e.searchPanes.panes.length;d++)e.searchPanes.panes[d].id===h.s.index&&(e.searchPanes.panes.splice(d,1),d--);e.searchPanes.panes.push({arrayFilter:r,bins:a,collapsed:n,id:h.s.index,order:i,searchTerm:o,selected:l})}})),this.s.dtPane.off("user-select.dtsp").on("user-select.dtsp",function(t,s,e,a,i){i.stopPropagation()}),this.s.dtPane.off("draw.dtsp").on("draw.dtsp",function(){return h.adjustTopRow()}),this.dom.nameButton.off("click.dtsp").on("click.dtsp",function(){var t=h.s.dtPane.order()[0][1];h.s.dtPane.order([0,"asc"===t?"desc":"asc"]).draw(),h.s.dt.state.save()}),this.dom.countButton.off("click.dtsp").on("click.dtsp",function(){var t=h.s.dtPane.order()[0][1];h.s.dtPane.order([1,"asc"===t?"desc":"asc"]).draw(),h.s.dt.state.save()}),this.dom.collapseButton.off("click.dtsp").on("click.dtsp",function(t){t.stopPropagation();t=_(h.s.dtPane.table().container());t.toggleClass(h.classes.hidden),h.dom.topRow.toggleClass(h.classes.bordered),h.dom.nameButton.toggleClass(h.classes.disabledButton),h.dom.countButton.toggleClass(h.classes.disabledButton),h.dom.searchButton.toggleClass(h.classes.disabledButton),h.dom.collapseButton.toggleClass(h.classes.rotated),t.hasClass(h.classes.hidden)?h.dom.topRow.on("click.dtsp",function(){return h.dom.collapseButton.click()}):h.dom.topRow.off("click.dtsp"),h.s.dt.state.save(),h.dom.topRow.trigger("collapse.dtsps")}),this.dom.clear.off("click.dtsp").on("click.dtsp",function(){h.dom.container.find("."+h.classes.search.replace(/ /g,".")).each(function(){_(this).val("").trigger("input")}),h.clearPane()}),this.dom.searchButton.off("click.dtsp").on("click.dtsp",function(){return h.dom.searchBox.focus()}),this.dom.searchBox.off("click.dtsp").on("input.dtsp",function(){var t=h.dom.searchBox.val();h.s.dtPane.search(t).draw(),"string"==typeof t&&(0<t.length||0===t.length&&0<h.s.dtPane.rows({selected:!0}).data().toArray().length)?h.dom.clear.removeClass(h.classes.disabledButton).removeAttr("disabled"):h.dom.clear.addClass(h.classes.disabledButton).attr("disabled","true"),h.s.dt.state.save()}),this.s.dtPane.select.style(this.s.colOpts.dtOpts&&this.s.colOpts.dtOpts.select&&this.s.colOpts.dtOpts.select.style?this.s.colOpts.dtOpts.select.style:this.c.dtOpts&&this.c.dtOpts.select&&this.c.dtOpts.select.style?this.c.dtOpts.select.style:"os"))},B.prototype._serverPopulate=function(t){t.tableLength?(this.s.tableLength=t.tableLength,this.s.rowData.totalOptions=this.s.tableLength):(null===this.s.tableLength||this.s.dt.rows()[0].length>this.s.tableLength)&&(this.s.tableLength=this.s.dt.rows()[0].length,this.s.rowData.totalOptions=this.s.tableLength);var s=this.s.dt.column(this.s.index).dataSrc();if(t.searchPanes.options[s])for(var e=0,a=t.searchPanes.options[s];e<a.length;e++){var i=a[e];this.s.rowData.arrayFilter.push({display:i.label,filter:i.value,sort:i.label,type:i.label}),this.s.rowData.bins[i.value]=i.total}t=Object.keys(this.s.rowData.bins).length,s=this._uniqueRatio(t,this.s.tableLength);!1===this.s.displayed&&((void 0===this.s.colOpts.show&&null===this.s.colOpts.threshold?s>this.c.threshold:s>this.s.colOpts.threshold)||!0!==this.s.colOpts.show&&t<=1)?(this.dom.container.addClass(this.classes.hidden),this.s.displayed=!1):(this.s.rowData.arrayOriginal=this.s.rowData.arrayFilter,this.s.rowData.binsOriginal=this.s.rowData.bins,this.s.displayed=!0)},B.prototype.show=function(){this.s.displayed&&(this.dom.topRow.removeClass(this.classes.bordered),this.dom.nameButton.removeClass(this.classes.disabledButton),this.dom.countButton.removeClass(this.classes.disabledButton),this.dom.searchButton.removeClass(this.classes.disabledButton),this.dom.collapseButton.removeClass(this.classes.rotated),_(this.s.dtPane.table().container()).removeClass(this.classes.hidden),this.dom.topRow.trigger("collapse.dtsps"))},B.prototype._uniqueRatio=function(t,s){return 0<s&&(0<this.s.rowData.totalOptions&&!this.s.dt.page.info().serverSide||this.s.dt.page.info().serverSide&&0<this.s.tableLength)?t/this.s.rowData.totalOptions:1},B.prototype.updateTable=function(){var t=this.s.dtPane.rows({selected:!0}).data().toArray().map(function(t){return t.filter});this.s.selections=t,this._searchExtras()},B.prototype._getComparisonRows=function(){var t=this.s.colOpts.options||(this.s.customPaneSettings&&this.s.customPaneSettings.options?this.s.customPaneSettings.options:void 0);if(void 0!==t){var s=this.s.dt.rows(),e=s.data().toArray(),a=[];this.s.dtPane.clear(),this.s.indexes=[];for(var i=0,n=t;i<n.length;i++){var o=n[i],r=""!==o.label?o.label:this.emptyMessage(),l={className:o.className,display:r,filter:"function"==typeof o.value?o.value:[],sort:void 0!==o.order?o.order:r,total:0,type:r};if("function"==typeof o.value){for(var d=0;d<e.length;d++)o.value.call(this.s.dt,e[d],s[0][d])&&l.total++;"function"!=typeof l.filter&&l.filter.push(o.filter)}a.push(this.addRow(l.display,l.filter,l.sort,l.type,l.className,l.total))}return a}},B.prototype._getMessage=function(t){return this.s.dt.i18n("searchPanes.count",this.c.i18n.count).replace(/{total}/g,t.total)},B.prototype._getShown=function(t){},B.prototype._getPaneConfig=function(){var a=this,t=l.Scroller,s=this.s.dt.settings()[0].oLanguage;return s.url=void 0,s.sUrl=void 0,{columnDefs:[{className:"dtsp-nameColumn",data:"display",render:function(t,s,e){return"sort"===s?e.sort:"type"===s?e.type:(e=a._getMessage(e),e='<span class="'+a.classes.pill+'">'+e+"</span>",a.c.viewCount&&a.s.colOpts.viewCount||(e=""),"filter"===s?"string"==typeof t&&null!==t.match(/<[^>]*>/)?t.replace(/<[^>]*>/g,""):t:'<div class="'+a.classes.nameCont+'"><span title="'+("string"==typeof t&&null!==t.match(/<[^>]*>/)?t.replace(/<[^>]*>/g,""):t)+'" class="'+a.classes.name+'">'+t+"</span>"+e+"</div>")},targets:0,type:this.s.dt.settings()[0].aoColumns[this.s.index]?this.s.dt.settings()[0].aoColumns[this.s.index]._sManualType:null},{className:"dtsp-countColumn "+this.classes.badgePill,data:"total",searchable:!1,targets:1,visible:!1}],deferRender:!0,info:!1,language:s,paging:!!t,scrollX:!1,scrollY:"200px",scroller:!!t,select:!0,stateSave:!!this.s.dt.settings()[0].oFeatures.bStateSave}},B.prototype._makeSelection=function(){this.updateTable(),this.s.updating=!0,this.s.dt.draw(),this.s.updating=!1},B.prototype._populatePaneArray=function(t,s,a,e){void 0===e&&(e=this.s.rowData.bins);var i,n=a.fastData||function(t,s,e){return a.oApi._fnGetCellData(a,t,s,e)};"string"==typeof this.s.colOpts.orthogonal?(i=n(t,this.s.index,this.s.colOpts.orthogonal),this.s.rowData.filterMap.set(t,i),this._addOption(i,i,i,i,s,e)):("string"==typeof(i=null===(i=n(t,this.s.index,this.s.colOpts.orthogonal.search))?"":i)&&(i=i.replace(/<[^>]*>/g,"")),this.s.rowData.filterMap.set(t,i),e[i]?e[i]++:(e[i]=1,this._addOption(i,n(t,this.s.index,this.s.colOpts.orthogonal.display),n(t,this.s.index,this.s.colOpts.orthogonal.sort),n(t,this.s.index,this.s.colOpts.orthogonal.type),s,e))),this.s.rowData.totalOptions++},B.prototype._reloadSelect=function(t){if(void 0!==t){for(var s,e=0;e<t.searchPanes.panes.length;e++)if(t.searchPanes.panes[e].id===this.s.index){s=e;break}if(s)for(var a=this.s.dtPane,i=a.rows({order:"index"}).data().map(function(t){return null!==t.filter?t.filter.toString():null}).toArray(),n=0,o=t.searchPanes.panes[s].selected;n<o.length;n++){var r=o[n],l=-1;-1<(l=null!==r?i.indexOf(r.toString()):l)&&(this.s.serverSelecting=!0,a.row(l).select(),this.s.serverSelecting=!1)}}},B.prototype._updateSelection=function(t){function s(t){T.versionCheck("2")?e.s.dt.processing(t):(t=e.s.dt.settings()[0]).oApi._fnProcessingDisplay(t,!1)}var e=this;s(!0),setTimeout(function(){e.s.scrollTop=_(e.s.dtPane.table().node()).parent()[0].scrollTop,e.s.dt.page.info().serverSide&&!e.s.updating?e.s.serverSelecting||(e.s.serverSelect=e.s.dtPane.rows({selected:!0}).data().toArray(),e.s.dt.draw(!1)):t&&e._makeSelection(),s(!1)},1)},B.prototype._addOption=function(t,s,e,a,i,n){if(Array.isArray(t)||t instanceof l.Api){if(t instanceof l.Api&&(t=t.toArray(),s=s.toArray()),t.length!==s.length)throw new Error("display and filter not the same length");for(var o=0;o<t.length;o++)n[t[o]]?n[t[o]]++:(n[t[o]]=1,i.push({display:s[o],filter:t[o],sort:e[o],type:a[o]})),this.s.rowData.totalOptions++}else"string"==typeof this.s.colOpts.orthogonal?(n[t]?n[t]++:(n[t]=1,i.push({display:s,filter:t,sort:e,type:a})),this.s.rowData.totalOptions++):i.push({display:s,filter:t,sort:e,type:a})},B.prototype._buildPane=function(t,s,e){var i=this,a=(void 0===t&&(t=[]),void 0===s&&(s=null),void 0===e&&(e=null),this.s.selections=[],this.s.dt.state.loaded());if(this.s.listSet&&(a=this.s.dt.state()),this.s.colExists){var n=-1;if(a&&a.searchPanes&&a.searchPanes.panes)for(var o=0;o<a.searchPanes.panes.length;o++)if(a.searchPanes.panes[o].id===this.s.index){n=o;break}if((!1===this.s.colOpts.show||void 0!==this.s.colOpts.show&&!0!==this.s.colOpts.show)&&-1===n)return this.dom.container.addClass(this.classes.hidden),this.s.displayed=!1;if(!0!==this.s.colOpts.show&&-1===n||(this.s.displayed=!0),this.s.dt.page.info().serverSide||s&&s.searchPanes&&s.searchPanes.options)s&&s.searchPanes&&s.searchPanes.options&&this._serverPopulate(s);else{0===this.s.rowData.arrayFilter.length&&(this.s.rowData.totalOptions=0,this._populatePane(),this.s.rowData.arrayOriginal=this.s.rowData.arrayFilter,this.s.rowData.binsOriginal=this.s.rowData.bins);var r=Object.keys(this.s.rowData.binsOriginal).length,l=this._uniqueRatio(r,this.s.dt.rows()[0].length);if(!1===this.s.displayed&&((void 0===this.s.colOpts.show&&null===this.s.colOpts.threshold?l>this.c.threshold:l>this.s.colOpts.threshold)||!0!==this.s.colOpts.show&&r<=1))return this.dom.container.addClass(this.classes.hidden),void(this.s.displayed=!1);this.dom.container.addClass(this.classes.show),this.s.displayed=!0}}else this.s.displayed=!0;this._displayPane(),this.s.listSet||this.dom.dtP.on("stateLoadParams.dtsp",function(t,s,e){_.isEmptyObject(i.s.dt.state.loaded())&&_.each(e,function(t){delete e[t]})}),null!==e&&0<this.dom.panesContainer.has(e).length?this.dom.container.insertAfter(e):this.dom.panesContainer.prepend(this.dom.container);l=_.fn.dataTable.ext.errMode,_.fn.dataTable.ext.errMode="none",this.dom.dtP.on("init.dt",function(t,s){var e=i.dom.dtP.DataTable(),a=e.select.style();e.select.style(a)}),this.s.dtPane=this.dom.dtP.DataTable(_.extend(!0,this._getPaneConfig(),this.c.dtOpts,this.s.colOpts?this.s.colOpts.dtOpts:{},this.s.colOpts.options||!this.s.colExists?{createdRow:function(t,s){_(t).addClass(s.className)}}:void 0,null!==this.s.customPaneSettings&&this.s.customPaneSettings.dtOpts?this.s.customPaneSettings.dtOpts:{},_.fn.dataTable.versionCheck("2")?{layout:{bottomStart:null,bottomEnd:null,topStart:null,topEnd:null}}:{dom:"t"})),this.dom.dtP.addClass(this.classes.table),r="Custom Pane";if(this.s.customPaneSettings&&this.s.customPaneSettings.header?r=this.s.customPaneSettings.header:this.s.colOpts.header?r=this.s.colOpts.header:this.s.colExists&&(r=_.fn.dataTable.versionCheck("2")?this.s.dt.column(this.s.index).title():this.s.dt.settings()[0].aoColumns[this.s.index].sTitle),r=this._escapeHTML(r),this.dom.searchBox.attr("placeholder",r),_.fn.dataTable.ext.errMode=l,this.s.colExists)for(var d=0,h=this.s.rowData.arrayFilter.length;d<h;d++)if(this.s.dt.page.info().serverSide)for(var c=this.addRow(this.s.rowData.arrayFilter[d].display,this.s.rowData.arrayFilter[d].filter,this.s.rowData.arrayFilter[d].sort,this.s.rowData.arrayFilter[d].type),p=0,u=this.s.serverSelect;p<u.length;p++)u[p].filter===this.s.rowData.arrayFilter[d].filter&&(this.s.serverSelecting=!0,c.select(),this.s.serverSelecting=!1);else!this.s.dt.page.info().serverSide&&this.s.rowData.arrayFilter[d]?this.addRow(this.s.rowData.arrayFilter[d].display,this.s.rowData.arrayFilter[d].filter,this.s.rowData.arrayFilter[d].sort,this.s.rowData.arrayFilter[d].type):this.s.dt.page.info().serverSide||this.addRow("","","","");(this.s.colOpts.options||this.s.customPaneSettings&&this.s.customPaneSettings.options)&&this._getComparisonRows(),this.s.dtPane.draw(),this.s.dtPane.table().node().parentNode.scrollTop=this.s.scrollTop,this.adjustTopRow(),this.setListeners(),this.s.listSet=!0;for(var f=0,g=t;f<g.length;f++){var v=g[f];if(v)for(var m=0,w=this.s.dtPane.rows().indexes().toArray();m<w.length;m++)c=w[m],this.s.dtPane.row(c).data()&&v.filter===this.s.dtPane.row(c).data().filter&&(this.s.dt.page.info().serverSide?(this.s.serverSelecting=!0,this.s.dtPane.row(c).select(),this.s.serverSelecting=!1):this.s.dtPane.row(c).select())}if(this.s.dt.page.info().serverSide&&this.s.dtPane.search(this.dom.searchBox.val()).draw(),(this.c.initCollapsed&&!1!==this.s.colOpts.initCollapsed||this.s.colOpts.initCollapsed)&&(this.c.collapse&&!1!==this.s.colOpts.collapse||this.s.colOpts.collapse)&&(this.s.dtPane.settings()[0]._bInitComplete?this.collapse():this.s.dtPane.one("init",function(){return i.collapse()})),a&&a.searchPanes&&a.searchPanes.panes&&(!s||1===s.draw)){this._reloadSelect(a);for(var P=0,y=a.searchPanes.panes;P<y.length;P++){var b=y[P];b.id===this.s.index&&(b.searchTerm&&0<b.searchTerm.length&&this.dom.searchBox.val(b.searchTerm).trigger("input"),b.order&&this.s.dtPane.order(b.order).draw(),b.collapsed?this.collapse():this.show())}}return!0},B.prototype._displayPane=function(){this.dom.dtP.empty(),this.dom.topRow.empty().addClass(this.classes.topRow),3<parseInt(this.c.layout.split("-")[1],10)&&this.dom.container.addClass(this.classes.smallGap),this.dom.topRow.addClass(this.classes.subRowsContainer).append(this.dom.upper.append(this.dom.searchCont)).append(this.dom.lower.append(this.dom.buttonGroup)),(!1===this.c.dtOpts.searching||this.s.colOpts.dtOpts&&!1===this.s.colOpts.dtOpts.searching||!this.c.controls||!this.s.colOpts.controls||this.s.customPaneSettings&&this.s.customPaneSettings.dtOpts&&void 0!==this.s.customPaneSettings.dtOpts.searching&&!this.s.customPaneSettings.dtOpts.searching)&&this.dom.searchBox.removeClass(this.classes.paneInputButton).addClass(this.classes.disabledButton).attr("disabled","true"),this.dom.searchBox.appendTo(this.dom.searchCont),this._searchContSetup(),this.c.clear&&this.c.controls&&this.s.colOpts.controls&&this.dom.clear.appendTo(this.dom.buttonGroup),this.c.orderable&&this.s.colOpts.orderable&&this.c.controls&&this.s.colOpts.controls&&this.dom.nameButton.appendTo(this.dom.buttonGroup),this.c.viewCount&&this.s.colOpts.viewCount&&this.c.orderable&&this.s.colOpts.orderable&&this.c.controls&&this.s.colOpts.controls&&this.dom.countButton.appendTo(this.dom.buttonGroup),(this.c.collapse&&!1!==this.s.colOpts.collapse||this.s.colOpts.collapse)&&this.c.controls&&this.s.colOpts.controls&&this.dom.collapseButton.appendTo(this.dom.buttonGroup),this.dom.container.prepend(this.dom.topRow).append(this.dom.dtP).show()},B.prototype._escapeHTML=function(t){return t.toString().replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&quot;/g,'"').replace(/&amp;/g,"&")},B.prototype._getBonusOptions=function(){return _.extend(!0,{},B.defaults,{threshold:null},this.c||{})},B.prototype._getOptions=function(){var t=this.s.dt.settings()[0].aoColumns[this.s.index].searchPanes,s=_.extend(!0,{},B.defaults,{collapse:null,emptyMessage:!1,initCollapsed:null,threshold:null},t);return t&&t.hideCount&&void 0===t.viewCount&&(s.viewCount=!t.hideCount),s},B.prototype._populatePane=function(){this.s.rowData.arrayFilter=[],this.s.rowData.bins={};var t=this.s.dt.context[0];if(!this.s.dt.page.info().serverSide)for(var s=0,e=this.s.dt.rows().indexes().toArray();s<e.length;s++){var a=e[s];this._populatePaneArray(a,this.s.rowData.arrayFilter,t)}},B.prototype._search=function(t,s){for(var e=this.s.colOpts,a=this.s.dt,i=0,n=this.s.selections;i<n.length;i++){var o=n[i];if("string"==typeof o&&"string"==typeof t&&(o=this._escapeHTML(o)),Array.isArray(t)){if("and"===e.combiner){if(!t.includes(o))return!1}else if(t.includes(o))return!0}else if("function"==typeof o){if(o.call(a,a.row(s).data(),s)){if("or"===e.combiner)return!0}else if("and"===e.combiner)return!1}else if(t===o||("string"!=typeof t||0!==t.length)&&t==o||null===o&&"string"==typeof t&&""===t)return!0}return"and"===e.combiner},B.prototype._searchContSetup=function(){this.c.controls&&this.s.colOpts.controls&&this.dom.searchButton.appendTo(this.dom.searchLabelCont),!1===this.c.dtOpts.searching||!1===this.s.colOpts.dtOpts.searching||this.s.customPaneSettings&&this.s.customPaneSettings.dtOpts&&void 0!==this.s.customPaneSettings.dtOpts.searching&&!this.s.customPaneSettings.dtOpts.searching||this.dom.searchLabelCont.appendTo(this.dom.searchCont)},B.prototype._searchExtras=function(){var t=this.s.updating,s=(this.s.updating=!0,this.s.dtPane.rows({selected:!0}).data().pluck("filter").toArray()),e=s.indexOf(this.emptyMessage()),a=_(this.s.dtPane.table().container());-1<e&&(s[e]=""),0<s.length?a.addClass(this.classes.selected):0===s.length&&a.removeClass(this.classes.selected),this.s.updating=t},B.version="2.1.2",B.classes={bordered:"dtsp-bordered",buttonGroup:"dtsp-buttonGroup",buttonSub:"dtsp-buttonSub",caret:"dtsp-caret",clear:"dtsp-clear",clearAll:"dtsp-clearAll",clearButton:"clearButton",collapseAll:"dtsp-collapseAll",collapseButton:"dtsp-collapseButton",container:"dtsp-searchPane",countButton:"dtsp-countButton",disabledButton:"dtsp-disabledButton",hidden:"dtsp-hidden",hide:"dtsp-hide",layout:"dtsp-",name:"dtsp-name",nameButton:"dtsp-nameButton",nameCont:"dtsp-nameCont",narrow:"dtsp-narrow",paneButton:"dtsp-paneButton",paneInputButton:"dtsp-paneInputButton",pill:"dtsp-pill",rotated:"dtsp-rotated",search:"dtsp-search",searchCont:"dtsp-searchCont",searchIcon:"dtsp-searchIcon",searchLabelCont:"dtsp-searchButtonCont",selected:"dtsp-selected",smallGap:"dtsp-smallGap",subRow1:"dtsp-subRow1",subRow2:"dtsp-subRow2",subRowsContainer:"dtsp-subRowsContainer",title:"dtsp-title",topRow:"dtsp-topRow"},B.defaults={clear:!0,collapse:!0,combiner:"or",container:function(t){return t.table().container()},controls:!0,dtOpts:{},emptyMessage:null,hideCount:!1,i18n:{clearPane:"&times;",count:"{total}",emptyMessage:"<em>No data</em>"},initCollapsed:!1,layout:"auto",name:void 0,orderable:!0,orthogonal:{display:"display",filter:"filter",hideCount:!1,search:"filter",show:void 0,sort:"sort",threshold:.6,type:"type",viewCount:!0},preSelect:[],threshold:.6,viewCount:!0},h=B,(r&&r.__extends||(a=function(t,s){return(a=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(t,s){t.__proto__=s}:function(t,s){for(var e in s)s.hasOwnProperty(e)&&(t[e]=s[e])}))(t,s)},function(t,s){function e(){this.constructor=t}a(t,s),t.prototype=null===s?Object.create(s):(e.prototype=s.prototype,new e)}))(s,d=h),s.prototype._emptyPane=function(){var t,e,s=this.s.dtPane;return T.versionCheck("2")?((t=s.select.last())&&s.row(t.row).any()&&(e=s.row(t.row).data().index),s.rows().remove(),function(){var t;void 0!==e&&(t=s.row(function(t,s){return s.index===e}).index(),s.select.last({row:t,column:0}))}):(s.rows().remove(),function(){})},s.prototype._serverPopulate=function(t){this.s.rowData.binsShown={},this.s.rowData.arrayFilter=[],void 0!==t.tableLength?(this.s.tableLength=t.tableLength,this.s.rowData.totalOptions=this.s.tableLength):(null===this.s.tableLength||this.s.dt.rows()[0].length>this.s.tableLength)&&(this.s.tableLength=this.s.dt.rows()[0].length,this.s.rowData.totalOptions=this.s.tableLength);var s,e=this.s.dt.column(this.s.index).dataSrc();if(void 0!==t.searchPanes.options[e])for(var a=0,i=t.searchPanes.options[e];a<i.length;a++){var n=i[a];this.s.rowData.arrayFilter.push({display:n.label,filter:n.value,shown:+n.count,sort:n.label,total:+n.total,type:n.label}),this.s.rowData.binsShown[n.value]=+n.count,this.s.rowData.bins[n.value]=+n.total}t=Object.keys(this.s.rowData.bins).length,e=this._uniqueRatio(t,this.s.tableLength);if(!this.s.colOpts.show&&!1===this.s.displayed&&((void 0===this.s.colOpts.show&&null===this.s.colOpts.threshold?e>this.c.threshold:e>this.s.colOpts.threshold)||!0!==this.s.colOpts.show&&t<=1))this.dom.container.addClass(this.classes.hidden),this.s.displayed=!1;else if(this.s.rowData.arrayOriginal=this.s.rowData.arrayFilter,this.s.rowData.binsOriginal=this.s.rowData.bins,this.s.displayed=!0,this.s.dtPane){for(var o=this.s.serverSelect,e=this._emptyPane(),r=0,l=this.s.rowData.arrayFilter;r<l.length;r++)if(s=l[r],this._shouldAddRow(s))for(var d=this.addRow(s.display,s.filter,s.sort,s.type),h=0;h<o.length;h++)if((u=o[h]).filter===s.filter){this.s.serverSelecting=!0,d.select(),this.s.serverSelecting=!1,o.splice(h,1),this.s.selections.push(s.filter);break}for(var c=0,p=o;c<p.length;c++)for(var u=p[c],f=0,g=this.s.rowData.arrayOriginal;f<g.length;f++)(s=g[f]).filter===u.filter&&(d=this.addRow(s.display,s.filter,s.sort,s.type),this.s.serverSelecting=!0,d.select(),this.s.serverSelecting=!1,this.s.selections.push(s.filter));this.s.serverSelect=this.s.dtPane.rows({selected:!0}).data().toArray(),this.s.dtPane.draw(),e()}},s.prototype.updateRows=function(){if(!this.s.dt.page.info().serverSide){this.s.rowData.binsShown={};for(var t=0,s=this.s.dt.rows({search:"applied"}).indexes().toArray();t<s.length;t++){var e=s[t];this._updateShown(e,this.s.dt.settings()[0],this.s.rowData.binsShown)}}for(var a=this,i=0,n=this.s.dtPane.rows().data().toArray();i<n.length;i++)!function(e){e.shown="number"==typeof a.s.rowData.binsShown[e.filter]?a.s.rowData.binsShown[e.filter]:0,a.s.dtPane.row(function(t,s){return s&&s.index===e.index}).data(e)}(n[i]);this.s.dtPane.draw(),this.s.dtPane.table().node().parentNode.scrollTop=this.s.scrollTop},s.prototype._makeSelection=function(){},s.prototype._reloadSelect=function(){},s.prototype._shouldAddRow=function(t){return!0},s.prototype._updateSelection=function(){!this.s.dt.page.info().serverSide||this.s.updating||this.s.serverSelecting||(this.s.serverSelect=this.s.dtPane.rows({selected:!0}).data().toArray())},s.prototype._updateShown=function(t,a,s){void 0===s&&(s=this.s.rowData.binsShown);function e(t){s[t]?s[t]++:s[t]=1}var i="string"==typeof this.s.colOpts.orthogonal?this.s.colOpts.orthogonal:this.s.colOpts.orthogonal.search,t=(a.fastData||function(t,s,e){return a.oApi._fnGetCellData(a,t,s,e)})(t,this.s.index,i);if(Array.isArray(t))for(var n=0,o=t;n<o.length;n++)e(o[n]);else e(t)},t=s,(r&&r.__extends||(i=function(t,s){return(i=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(t,s){t.__proto__=s}:function(t,s){for(var e in s)s.hasOwnProperty(e)&&(t[e]=s[e])}))(t,s)},function(t,s){function e(){this.constructor=t}i(t,s),t.prototype=null===s?Object.create(s):(e.prototype=s.prototype,new e)}))(e,p=t),e.prototype._getMessage=function(t){var s=this.s.dt.i18n("searchPanes.count",this.c.i18n.count),e=this.s.dt.i18n("searchPanes.countFiltered",this.c.i18n.countFiltered);return(this.s.filteringActive?e:s).replace(/{total}/g,t.total).replace(/{shown}/g,t.shown)},e.prototype._getShown=function(t){return this.s.rowData.binsShown&&this.s.rowData.binsShown[t]?this.s.rowData.binsShown[t]:0},g=e,(r&&r.__extends||(u=function(t,s){return(u=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(t,s){t.__proto__=s}:function(t,s){for(var e in s)s.hasOwnProperty(e)&&(t[e]=s[e])}))(t,s)},function(t,s){function e(){this.constructor=t}u(t,s),t.prototype=null===s?Object.create(s):(e.prototype=s.prototype,new e)}))(L,v=t),L.prototype.updateRows=function(){var t=this.s.dtPane.rows({selected:!0}).data().toArray();if(this.s.colOpts.options||this.s.customPaneSettings&&this.s.customPaneSettings.options){this._getComparisonRows();for(var s=this.s.dtPane.rows().toArray()[0],e=0;e<s.length;e++){var a=this.s.dtPane.row(s[e]),i=a.data();if(void 0!==i)if(0===i.shown)a.remove(),s=this.s.dtPane.rows().toArray()[0],e--;else for(var n=0,o=t;n<o.length;n++)if(m=o[n],i.filter===m.filter){a.select(),t.splice(e,1),this.s.selections.push(i.filter);break}}}else{if(!this.s.dt.page.info().serverSide){this._activePopulatePane(),this.s.rowData.binsShown={};for(var r=0,l=this.s.dt.rows({search:"applied"}).indexes().toArray();r<l.length;r++){var d=l[r];this._updateShown(d,this.s.dt.settings()[0],this.s.rowData.binsShown)}}this.s.dtPane.rows().remove();for(var h=0,c=this.s.rowData.arrayFilter;h<c.length;h++){var p=c[h];if(0!==p.shown)for(var u=this.addRow(p.display,p.filter,p.sort,p.type,void 0),f=0;f<t.length;f++)if(t[f].filter===p.filter){u.select(),t.splice(f,1),this.s.selections.push(p.filter);break}}for(var g=0,v=t;g<v.length;g++)for(var m=v[g],w=0,P=this.s.rowData.arrayOriginal;w<P.length;w++){var y=P[w];y.filter===m.filter&&(this.addRow(y.display,y.filter,y.sort,y.type,void 0).select(),this.s.selections.push(y.filter))}}this.s.dtPane.draw(),this.s.dtPane.table().node().parentNode.scrollTop=this.s.scrollTop,this.s.dt.page.info().serverSide||this.s.dt.draw(!1)},L.prototype._activePopulatePane=function(){this.s.rowData.arrayFilter=[],this.s.rowData.bins={};var t=this.s.dt.settings()[0];if(!this.s.dt.page.info().serverSide)for(var s=0,e=this.s.dt.rows({search:"applied"}).indexes().toArray();s<e.length;s++){var a=e[s];this._populatePaneArray(a,this.s.rowData.arrayFilter,t)}},L.prototype._getComparisonRows=function(){var t=this.s.colOpts.options||(this.s.customPaneSettings&&this.s.customPaneSettings.options?this.s.customPaneSettings.options:void 0);if(void 0!==t){var s=this.s.dt.rows(),e=this.s.dt.rows({search:"applied"}),a=s.data().toArray(),i=e.data().toArray(),n=[];this.s.dtPane.clear(),this.s.indexes=[];for(var o=0,r=t;o<r.length;o++){var l=r[o],d=""!==l.label?l.label:this.emptyMessage(),h={className:l.className,display:d,filter:"function"==typeof l.value?l.value:[],shown:0,sort:d,total:0,type:d};if("function"==typeof l.value){for(var c=0;c<a.length;c++)l.value.call(this.s.dt,a[c],s[0][c])&&h.total++;for(var p=0;p<i.length;p++)l.value.call(this.s.dt,i[p],e[0][p])&&h.shown++;"function"!=typeof h.filter&&h.filter.push(l.filter)}n.push(this.addRow(h.display,h.filter,h.sort,h.type,h.className,h.total,h.shown))}return n}},L.prototype._getMessage=function(t){return this.s.dt.i18n("searchPanes.count",this.c.i18n.count).replace(/{total}/g,t.total).replace(/{shown}/g,t.shown)},L.prototype._getShown=function(t){return this.s.rowData.binsShown&&this.s.rowData.binsShown[t]?this.s.rowData.binsShown[t]:0},L.prototype._shouldAddRow=function(t){return 0<t.shown},P=L,(r&&r.__extends||(m=function(t,s){return(m=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(t,s){t.__proto__=s}:function(t,s){for(var e in s)s.hasOwnProperty(e)&&(t[e]=s[e])}))(t,s)},function(t,s){function e(){this.constructor=t}m(t,s),t.prototype=null===s?Object.create(s):(e.prototype=s.prototype,new e)}))(R,y=P),R.prototype._activePopulatePane=function(){this.s.rowData.arrayFilter=[],this.s.rowData.binsShown={};var t=this.s.dt.settings()[0];if(!this.s.dt.page.info().serverSide)for(var s=0,e=this.s.dt.rows({search:"applied"}).indexes().toArray();s<e.length;s++){var a=e[s];this._populatePaneArray(a,this.s.rowData.arrayFilter,t,this.s.rowData.binsShown)}},R.prototype._getMessage=function(t){var s=this.s.dt.i18n("searchPanes.count",this.c.i18n.count),e=this.s.dt.i18n("searchPanes.countFiltered",this.c.i18n.countFiltered);return(this.s.filteringActive?e:s).replace(/{total}/g,t.total).replace(/{shown}/g,t.shown)},S=R,k.prototype.clearSelections=function(){for(var t,s=0,e=this.s.panes;s<e.length;s++)(t=e[s]).s.dtPane&&(t.s.scrollTop=t.s.dtPane.table().node().parentNode.scrollTop);this.dom.container.find("."+this.classes.search.replace(/\s+/g,".")).each(function(){b(this).val("").trigger("input")}),this.s.selectionList=[];for(var a=[],i=0,n=this.s.panes;i<n.length;i++)(t=n[i]).s.dtPane&&a.push(t.clearPane());return a},k.prototype.getNode=function(){return this.dom.container},k.prototype.rebuild=function(t,s){void 0===t&&(t=!1),void 0===s&&(s=!1),this.dom.emptyMessage.detach(),!1===t&&this.dom.panes.empty();for(var e=[],a=0,i=this.s.panes;a<i.length;a++){var n=i[a];!1!==t&&n.s.index!==t||(n.clearData(),n.rebuildPane(this.s.dt.page.info().serverSide?this.s.serverData:void 0,s),this.dom.panes.append(n.dom.container),e.push(n))}return this._updateSelection(),this._updateFilterCount(),this._attachPaneContainer(),this._initSelectionListeners(!1),this.s.dt.draw(!s),this.resizePanes(),1===e.length?e[0]:e},k.prototype.resizePanes=function(){var t;if("auto"===this.c.layout){for(var s=b(this.s.dt.searchPanes.container()).width(),s=Math.floor(s/this.s.minPaneWidth),e=1,a=0,i=[],n=0,o=this.s.panes;n<o.length;n++)(t=o[n]).s.displayed&&i.push(t.s.index);var r=i.length;if(s===r)e=s;else for(var l=s;1<l;l--){var d=r%l;if(0==d){e=l,a=0;break}a<d&&(e=l,a=d)}var h=0!==a?i.slice(i.length-a,i.length):[];this.s.panes.forEach(function(t){t.s.displayed&&t.resize("columns-"+(h.includes(t.s.index)?a:e))})}else for(var c=0,p=this.s.panes;c<p.length;c++)(t=p[c]).adjustTopRow();return this},k.prototype._initSelectionListeners=function(t){},k.prototype._serverTotals=function(){},k.prototype._setXHR=function(){function a(t){t&&t.searchPanes&&t.searchPanes.options&&(s.s.serverData=t,s.s.serverData.tableLength=t.recordsTotal,s._serverTotals())}var s=this,i=this.s.dt.settings()[0];this.s.dt.on("xhr.dtsps",function(t,s,e){i===s&&a(e)}),a(this.s.dt.ajax.json())},k.prototype._stateLoadListener=function(){var d=this,h=this.s.dt.settings()[0];this.s.dt.on("stateLoadParams.dtsps",function(t,s,e){if(void 0!==e.searchPanes&&s===h){if(d.clearSelections(),d.s.selectionList=e.searchPanes.selectionList||[],e.searchPanes.panes)for(var a=0,i=e.searchPanes.panes;a<i.length;a++)for(var n=i[a],o=0,r=d.s.panes;o<r.length;o++){var l=r[o];n.id===l.s.index&&l.s.dtPane&&(l.dom.searchBox.val(n.searchTerm),l.s.dtPane.order(n.order))}d._makeSelections(d.s.selectionList)}})},k.prototype._updateSelection=function(){this.s.selectionList=[];for(var t=0,s=this.s.panes;t<s.length;t++){var e,a=s[t];a.s.dtPane&&(e=a.s.dtPane.rows({selected:!0}).data().toArray().map(function(t){return t.filter})).length&&this.s.selectionList.push({column:a.s.index,rows:e})}},k.prototype._attach=function(){var t=this;this.dom.titleRow.removeClass(this.classes.hide).detach().append(this.dom.title),this.c.clear&&this.dom.clearAll.appendTo(this.dom.titleRow).off("click.dtsps").on("click.dtsps",function(){return t.clearSelections()}),this.c.collapse&&(this.dom.showAll.appendTo(this.dom.titleRow),this.dom.collapseAll.appendTo(this.dom.titleRow),this._setCollapseListener());for(var s=0,e=this.s.panes;s<e.length;s++){var a=e[s];this.dom.panes.append(a.dom.container)}this.dom.container.text("").removeClass(this.classes.hide).append(this.dom.titleRow).append(this.dom.panes),this.s.panes.forEach(function(t){return t.setListeners()}),0===b("div."+this.classes.container).length&&this.dom.container.prependTo(this.s.dt)},k.prototype._attachMessage=function(){var s;try{s=this.s.dt.i18n("searchPanes.emptyPanes",this.c.i18n.emptyPanes)}catch(t){s=null}null===s?(this.dom.container.addClass(this.classes.hide),this.dom.titleRow.removeClass(this.classes.hide)):(this.dom.container.removeClass(this.classes.hide),this.dom.titleRow.addClass(this.classes.hide),this.dom.emptyMessage.html(s).appendTo(this.dom.container))},k.prototype._attachPaneContainer=function(){for(var t=0,s=this.s.panes;t<s.length;t++)if(!0===s[t].s.displayed)return void this._attach();this._attachMessage()},k.prototype._checkCollapse=function(){for(var t=!0,s=!0,e=0,a=this.s.panes;e<a.length;e++){var i=a[e];i.s.displayed&&(i.dom.collapseButton.hasClass(i.classes.rotated)?(this.dom.showAll.removeClass(this.classes.disabledButton).removeAttr("disabled"),s=!1):(this.dom.collapseAll.removeClass(this.classes.disabledButton).removeAttr("disabled"),t=!1))}t&&this.dom.collapseAll.addClass(this.classes.disabledButton).attr("disabled","true"),s&&this.dom.showAll.addClass(this.classes.disabledButton).attr("disabled","true")},k.prototype._checkMessage=function(){for(var t=0,s=this.s.panes;t<s.length;t++)if(!0===s[t].s.displayed)return this.dom.emptyMessage.detach(),void this.dom.titleRow.removeClass(this.classes.hide);this._attachMessage()},k.prototype._collapseAll=function(){for(var t=0,s=this.s.panes;t<s.length;t++)s[t].collapse()},k.prototype._findPane=function(t){for(var s=0,e=this.s.panes;s<e.length;s++){var a=e[s];if(t===a.s.name)return a}},k.prototype._getState=function(){var t=this.s.dt.state.loaded();t&&t.searchPanes&&t.searchPanes.selectionList&&(this.s.selectionList=t.searchPanes.selectionList)},k.prototype._makeSelections=function(t){for(var s=0,e=t;s<e.length;s++){for(var a=e[s],i=void 0,n=0,o=this.s.panes;n<o.length;n++){var r=o[n];if(r.s.index===a.column){i=r;break}}if(i&&i.s.dtPane){for(var l=0;l<i.s.dtPane.rows().data().toArray().length;l++)a.rows.includes("function"==typeof i.s.dtPane.row(l).data().filter?i.s.dtPane.cell(l,0).data():i.s.dtPane.row(l).data().filter)&&i.s.dtPane.row(l).select();i.updateTable()}}},k.prototype._paneDeclare=function(t,s,e){for(var a=this,i=(t.columns(0<this.c.columns.length?this.c.columns:void 0).eq(0).each(function(t){a.s.panes.push(new a.s.paneClass(s,e,t,a.dom.panes))}),t.columns().eq(0).toArray().length),n=0;n<this.c.panes.length;n++)this.s.panes.push(new this.s.paneClass(s,e,i+n,this.dom.panes,this.c.panes[n]));0<this.c.order.length&&(this.s.panes=this.c.order.map(function(t){return a._findPane(t)})),this.s.dt.settings()[0]._bInitComplete?this._startup(t):C.versionCheck("2")?this.s.dt.settings()[0].aoInitComplete.push(function(){return a._startup(t)}):this.s.dt.settings()[0].aoInitComplete.push({fn:function(){return a._startup(t)}})},k.prototype._setCollapseListener=function(){var t=this;this.dom.collapseAll.off("click.dtsps").on("click.dtsps",function(){t._collapseAll(),t.dom.collapseAll.addClass(t.classes.disabledButton).attr("disabled","true"),t.dom.showAll.removeClass(t.classes.disabledButton).removeAttr("disabled"),t.s.dt.state.save()}),this.dom.showAll.off("click.dtsps").on("click.dtsps",function(){t._showAll(),t.dom.showAll.addClass(t.classes.disabledButton).attr("disabled","true"),t.dom.collapseAll.removeClass(t.classes.disabledButton).removeAttr("disabled"),t.s.dt.state.save()});for(var s=0,e=this.s.panes;s<e.length;s++)e[s].dom.topRow.off("collapse.dtsps").on("collapse.dtsps",function(){return t._checkCollapse()});this._checkCollapse()},k.prototype._showAll=function(){for(var t=0,s=this.s.panes;t<s.length;t++)s[t].show()},k.prototype._startup=function(i){for(var h=this,c=(this._attach(),this.dom.panes.empty(),this.s.dt.settings()[0]),t=0,s=this.s.panes;t<s.length;t++){var e=s[t];e.rebuildPane(0<Object.keys(this.s.serverData).length?this.s.serverData:void 0),this.dom.panes.append(e.dom.container)}"auto"===this.c.layout&&this.resizePanes();var a=this.s.dt.state.loaded(),n=(!this.s.stateRead&&a&&this.s.dt.page(a.start/this.s.dt.page.len()).draw("page"),this.s.stateRead=!0,this._checkMessage(),i.on("preDraw.dtsps",function(){h.s.updating||h.s.paging||(h._updateFilterCount(),h._updateSelection()),h.s.paging=!1}),b(r).on("resize.dtsps",C.util.throttle(function(){return h.resizePanes()})),this.s.dt.on("stateSaveParams.dtsps",function(t,s,e){s===c&&(void 0===e.searchPanes&&(e.searchPanes={}),e.searchPanes.selectionList=h.s.selectionList)}),this._stateLoadListener(),i.off("page.dtsps page-nc.dtsps").on("page.dtsps page-nc.dtsps",function(t,s){h.s.paging=!0,h.s.pagingST=!0,h.s.page=h.s.dt.page()}),this.s.dt.page.info().serverSide?i.off("preXhr.dtsps").on("preXhr.dtsps",function(t,s,e){if(s===c){e.searchPanes||(e.searchPanes={}),e.searchPanes_null||(e.searchPanes_null={});for(var a=0,i=0,n=h.s.panes;i<n.length;i++){var o=n[i],r=h.s.dt.column(o.s.index).dataSrc();if(e.searchPanes[r]||(e.searchPanes[r]={}),e.searchPanes_null[r]||(e.searchPanes_null[r]={}),o.s.dtPane)for(var l=o.s.dtPane.rows({selected:!0}).data().toArray(),d=0;d<l.length;d++)e.searchPanes[r][d]=l[d].filter,e.searchPanes[r][d]?e.searchPanes_null[r][d]=!1:e.searchPanes_null[r][d]=!0,a++}0<a&&(a!==h.s.filterCount?(e.start=0,h.s.page=0):e.start=h.s.page*h.s.dt.page.len(),h.s.dt.page(h.s.page),h.s.filterCount=a),0<h.s.selectionList.length&&(e.searchPanesLast=h.s.dt.column(h.s.selectionList[h.s.selectionList.length-1].column).dataSrc()),e.searchPanes_options={cascade:h.c.cascadePanes,viewCount:h.c.viewCount,viewTotal:h.c.viewTotal}}}):i.on("preXhr.dtsps",function(){return h.s.panes.forEach(function(t){return t.clearData()})}),this.s.dt.on("xhr.dtsps",function(t,s){var i;s.nTable!==h.s.dt.table().node()||h.s.dt.page.info().serverSide||(i=!1,h.s.dt.one("preDraw.dtsps",function(){if(!i){var t=h.s.dt.page();i=!0,h.s.updating=!0,h.dom.panes.empty();for(var s=0,e=h.s.panes;s<e.length;s++){var a=e[s];a.clearData(),a.rebuildPane(void 0,!0),h.dom.panes.append(a.dom.container)}h.s.dt.page.info().serverSide||h.s.dt.draw(),h.s.updating=!1,h._updateSelection(),h._checkMessage(),h.s.dt.one("draw.dtsps",function(){h.s.updating=!0,h.s.dt.page(t).draw(!1),h.s.updating=!1})}}))}),this.c.preSelect);a&&a.searchPanes&&a.searchPanes.selectionList&&(n=a.searchPanes.selectionList),this._makeSelections(n),this._updateFilterCount(),i.on("destroy.dtsps",function(t,s){if(s===c){for(var e=0,a=h.s.panes;e<a.length;e++)a[e].destroy();i.off(".dtsps"),h.dom.showAll.off(".dtsps"),h.dom.clearAll.off(".dtsps"),h.dom.collapseAll.off(".dtsps"),b(i.table().node()).off(".dtsps"),h.dom.container.detach(),h.clearSelections()}}),this.c.collapse&&this._setCollapseListener(),this.c.clear&&this.dom.clearAll.off("click.dtsps").on("click.dtsps",function(){return h.clearSelections()}),(c._searchPanes=this).s.dt.state.save()},k.prototype._updateFilterCount=function(){for(var t=0,s=0,e=0,a=this.s.panes;e<a.length;e++){var i=a[e];i.s.dtPane&&(t+=i.getPaneCount(),i.s.dtPane.search())&&s++}this.dom.title.html(this.s.dt.i18n("searchPanes.title",this.c.i18n.title,t)),this.c.filterChanged&&"function"==typeof this.c.filterChanged&&this.c.filterChanged.call(this.s.dt,t),0===t&&0===s?this.dom.clearAll.addClass(this.classes.disabledButton).attr("disabled","true"):this.dom.clearAll.removeClass(this.classes.disabledButton).removeAttr("disabled")},k.version="2.3.1",k.classes={clear:"dtsp-clear",clearAll:"dtsp-clearAll",collapseAll:"dtsp-collapseAll",container:"dtsp-searchPanes",disabledButton:"dtsp-disabledButton",emptyMessage:"dtsp-emptyMessage",hide:"dtsp-hidden",panes:"dtsp-panesContainer",search:"dtsp-search",showAll:"dtsp-showAll",title:"dtsp-title",titleRow:"dtsp-titleRow"},k.defaults={cascadePanes:!1,clear:!0,collapse:!0,columns:[],container:function(t){return t.table().container()},filterChanged:void 0,i18n:{clearMessage:"Clear All",clearPane:"&times;",collapse:{0:"SearchPanes",_:"SearchPanes (%d)"},collapseMessage:"Collapse All",count:"{total}",emptyMessage:"<em>No data</em>",emptyPanes:"No SearchPanes",loadMessage:"Loading Search Panes...",showMessage:"Show All",title:"Filters Active - %d"},layout:"auto",order:[],panes:[],preSelect:[],viewCount:!0,viewTotal:!1},O=k,(r&&r.__extends||(x=function(t,s){return(x=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(t,s){t.__proto__=s}:function(t,s){for(var e in s)s.hasOwnProperty(e)&&(t[e]=s[e])}))(t,s)},function(t,s){function e(){this.constructor=t}x(t,s),t.prototype=null===s?Object.create(s):(e.prototype=s.prototype,new e)}))(M,j=O),M.prototype._initSelectionListeners=function(t,s){void 0===s&&(s=[]),(t=void 0===t?!0:t)&&(this.s.selectionList=s);for(var e=0,a=this.s.panes;e<a.length;e++){var i=a[e];i.s.displayed&&i.s.dtPane.off("select.dtsp").on("select.dtsp",this._update(i)).off("deselect.dtsp").on("deselect.dtsp",this._updateTimeout(i))}this.s.dt.off("draw.dtsps").on("draw.dtsps",this._update()),this._updateSelectionList()},M.prototype._serverTotals=function(){for(var t=0,s=this.s.panes;t<s.length;t++){var e=s[t];if(e.s.colOpts.show){var a=this.s.dt.column(e.s.index).dataSrc(),i=!0;if(this.s.serverData.searchPanes.options[a])for(var n=0,o=this.s.serverData.searchPanes.options[a];n<o.length;n++){var r=o[n];if(r.total!==r.count){i=!1;break}}e.s.filteringActive=!i,e._serverPopulate(this.s.serverData)}}},M.prototype._stateLoadListener=function(){function t(t,s,e){if(void 0!==e.searchPanes){if(d.s.selectionList=e.searchPanes.selectionList||[],e.searchPanes.panes)for(var a=0,i=e.searchPanes.panes;a<i.length;a++)for(var n=i[a],o=0,r=d.s.panes;o<r.length;o++){var l=r[o];n.id===l.s.index&&l.s.dtPane&&(l.dom.searchBox.val(n.searchTerm),l.s.dtPane.order(n.order))}d._updateSelectionList()}}var d=this;this.s.dt.off("stateLoadParams.dtsps",t).on("stateLoadParams.dtsps",t)},M.prototype._updateSelection=function(){},M.prototype._update=function(t){var s=this;return void 0===t&&(t=void 0),function(){t&&clearTimeout(t.s.deselectTimeout),s._updateSelectionList(t)}},M.prototype._updateTimeout=function(t){var s=this;return void 0===t&&(t=void 0),function(){return t?t.s.deselectTimeout=setTimeout(function(){return s._updateSelectionList(t)},50):s._updateSelectionList()}},M.prototype._updateSelectionList=function(s){var t;void 0===s&&(s=void 0),this.s.pagingST?this.s.pagingST=!1:this.s.updating||s&&s.s.serverSelecting||(void 0!==s&&(this.s.dt.page.info().serverSide&&s._updateSelection(),t=s.s.dtPane.rows({selected:!0}).data().toArray().map(function(t){return t.filter}),this.s.selectionList=this.s.selectionList.filter(function(t){return t.column!==s.s.index}),0<t.length?(this.s.selectionList.push({column:s.s.index,rows:t}),s.dom.clear.removeClass(this.classes.disabledButton).removeAttr("disabled")):s.dom.clear.addClass(this.classes.disabledButton).attr("disabled","true"),this.s.dt.page.info().serverSide)&&this.s.dt.draw(!1),this._remakeSelections(),this._updateFilterCount())},M.prototype._remakeSelections=function(){var t;if(this.s.updating=!0,this.s.dt.page.info().serverSide){0<this.s.selectionList.length&&(h=this.s.panes[this.s.selectionList[this.s.selectionList.length-1].column]);for(var s=0,e=this.s.panes;s<e.length;s++)!(t=e[s]).s.displayed||h&&t.s.index===h.s.index||t.updateRows()}else{var a=this.s.selectionList,i=!1;this.clearSelections(),this.s.dt.draw(!1),this.s.dt.rows().toArray()[0].length>this.s.dt.rows({search:"applied"}).toArray()[0].length&&(i=!0),this.s.selectionList=a;for(var n=0,o=this.s.panes;n<o.length;n++)(h=o[n]).s.displayed&&(h.s.filteringActive=i,h.updateRows());for(var r=0,l=this.s.selectionList;r<l.length;r++){for(var d=l[r],h=null,c=0,p=this.s.panes;c<p.length;c++){var u=p[c];if(u.s.index===d.column){h=u;break}}if(h.s.dtPane){for(var f=h.s.dtPane.rows().indexes().toArray(),g=0;g<d.rows.length;g++){for(var v=!1,m=0,w=f;m<w.length;m++){var P=w[m],P=h.s.dtPane.row(P),y=P.data();d.rows[g]===y.filter&&(P.select(),v=!0)}v||(d.rows.splice(g,1),g--)}if(h.s.selections=d.rows,0!==d.rows.length){this.s.dt.draw();for(var b=0,_=0,C=0,S=0,O=this.s.panes;S<O.length;S++)(t=O[S]).s.dtPane&&_<(b+=t.getPaneCount())&&(C++,_=b);for(var x=0<b,A=0,D=this.s.panes;A<D.length;A++)(t=D[A]).s.displayed&&(i||h.s.index!==t.s.index||!x?t.s.filteringActive=x||i:1===C&&(t.s.filteringActive=!1),t.s.index!==h.s.index)&&t.updateRows()}}}this.s.dt.draw(!1)}this.s.updating=!1},A=M,l=(_=o).fn.dataTable,C=(b=o).fn.dataTable,(D=(w=f=c=o).fn.dataTable).SearchPanes=O,T.SearchPanes=O,D.SearchPanesST=A,T.SearchPanesST=A,D.SearchPane=h,T.SearchPane=h,D.SearchPaneViewTotal=g,T.SearchPaneViewTotal=g,D.SearchPaneCascade=P,T.SearchPaneCascade=P,D.SearchPaneCascadeViewTotal=S,T.SearchPaneCascadeViewTotal=S,(t=o.fn.dataTable.Api.register)("searchPanes()",function(){return this}),t("searchPanes.clearSelections()",function(){return this.iterator("table",function(t){t._searchPanes&&t._searchPanes.clearSelections()})}),t("searchPanes.rebuildPane()",function(s,e){return this.iterator("table",function(t){t._searchPanes&&t._searchPanes.rebuild(s,e)})}),t("searchPanes.resizePanes()",function(){var t=this.context[0];return t._searchPanes?t._searchPanes.resizePanes():null}),t("searchPanes.container()",function(){var t=this.context[0];return t._searchPanes?t._searchPanes.getNode():null}),T.ext.buttons.searchPanesClear={action:function(t,s){s.searchPanes.clearSelections()},text:"Clear Panes"},T.ext.buttons.searchPanes={action:function(t,s,e,a){var i=this,n=this;a._panes?(this.popover(a._panes.getNode(),{align:"container",span:"container"}),a._panes.rebuild(void 0,!0)):(this.processing(!0),setTimeout(function(){N(s,e,a),i.popover(a._panes.getNode(),{align:"container",span:"container"}),a._panes.rebuild(void 0,!0),o("table.dataTable",a._panes.getNode()).DataTable().columns.adjust(),n.processing(!1)},10))},init:function(t,s,e){t.button(s).text(e.text||t.i18n("searchPanes.collapse","SearchPanes",0)),!t.init().stateSave&&!1!==e.delayInit||N(t,s,e)},config:{},text:"",delayInit:!0},o(n).on("preInit.dt.dtsp",function(t,s){"dt"!==t.namespace||!s.oInit.searchPanes&&!T.defaults.searchPanes||s._searchPanes||F(s,null,!0)}),T.ext.feature.push({cFeature:"P",fnInit:F}),T.feature&&T.feature.register("searchPanes",F),T});

/*! Bootstrap 5 integration for DataTables' SearchPanes
 * © SpryMedia Ltd - datatables.net/license
 */
!function(n){var a,s;"function"==typeof define&&define.amd?define(["jquery","datatables.net-bs5","datatables.net-searchpanes"],function(e){return n(e,window,document)}):"object"==typeof exports?(a=require("jquery"),s=function(e,t){t.fn.dataTable||require("datatables.net-bs5")(e,t),t.fn.dataTable.SearchPanes||require("datatables.net-searchpanes")(e,t)},"undefined"==typeof window?module.exports=function(e,t){return e=e||window,t=t||a(e),s(e,t),n(t,0,e.document)}:(s(window,a),module.exports=n(a,window,window.document))):n(jQuery,window,document)}(function(e,t,n){"use strict";var a=e.fn.dataTable;return e.extend(!0,a.SearchPane.classes,{buttonGroup:"btn-group",disabledButton:"disabled",narrow:"col",pane:{container:"table"},paneButton:"btn btn-subtle",pill:"badge rounded-pill bg-secondary",search:"form-control search",table:"table table-sm table-borderless",topRow:"dtsp-topRow"}),e.extend(!0,a.SearchPanes.classes,{clearAll:"dtsp-clearAll btn btn-subtle",collapseAll:"dtsp-collapseAll btn btn-subtle",container:"dtsp-searchPanes",disabledButton:"disabled",panes:"dtsp-panes dtsp-panesContainer",search:a.SearchPane.classes.search,showAll:"dtsp-showAll btn btn-subtle",title:"dtsp-title",titleRow:"dtsp-titleRow"}),a});

