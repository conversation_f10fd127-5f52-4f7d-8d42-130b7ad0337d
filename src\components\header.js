import { BASE_URL } from '../router.js';

export default class Header {
    constructor() {
        this.state = {
            brand: {
                text: 'LockedPay',
                icon: 'shield-lock',
            },
            profile: {
                avatar: {
                    src: 'assets/images/avatar/avatar.jpg',
                    alt: 'avatar',
                    status: 'online'
                },
                user: {
                    name: localStorage.getItem('username') || 'Guest',
                    viewProfileText: 'Cashier',
                    badge: {
                        text: 'Active',
                        color: 'success'
                    }
                },
                menu: [
                    { text: 'Sign Out', icon: 'box-arrow-right', href: `${BASE_URL}/logout`, color: 'danger' }
                ]
            }
        };
    }

    getBrandLogo() {
        return `
            <a class="navbar-brand d-block d-sm-none" href="${BASE_URL}/index.html">
                <div class="d-flex align-items-center">
                    <div class="brand-icon rounded-circle bg-transparent p-2 me-2">
                        <i class="bi bi-${this.state.brand.icon} text-white fs-3"></i>
                    </div>
                    <h3 class="mb-0 fw-bold d-block d-sm-none text-white">${this.state.brand.text}</h3>
                </div>
            </a>
        `;
    }

    getNavToggle() {
        return `
            <button id="nav-toggle" type="button" class="btn btn-icon nav-link ms-2" 
                    aria-label="Toggle navigation">
                <div class="nav-icon-wrapper rounded-circle bg-primary p-2">
                    <i class="bi bi-list text-white fw-bold fs-5"></i>
                </div>
            </button>
        `;
    }

    getUserProfile() {
        const { avatar, user, menu } = this.state.profile;
        const menuItems = menu.map(item => {
            if (item.divider) return '<li><hr class="dropdown-divider opacity-25 mx-2"></li>';
            return `
                <li>
                    <a class="dropdown-item py-2 px-3${item.color ? ` text-${item.color}` : ''}" href="${item.href}">
                        <div class="d-flex align-items-center">
                            <div class="dropdown-icon-wrapper rounded-circle${item.color ? ` bg-${item.color} bg-opacity-10` : ' bg-light'} p-2 me-3">
                                <i class="bi bi-${item.icon} text-white"></i>
                            </div>
                            <span class="fw-medium text-white">${item.text}</span>
                        </div>
                    </a>
                </li>
            `;
        }).join('');

        return `
            <div class="dropdown">
                <a class="nav-link p-1 ms-2" href="#!" role="button" id="dropdownUser" 
                   data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <div class="d-flex align-items-center">
                        <div class="position-relative">
                            <img alt="${avatar.alt}" src="${avatar.src}" 
                                 class="avatar-md rounded-circle border border-2 border-white shadow-sm" />
                            <span class="position-absolute bottom-0 end-0 p-1 bg-${avatar.status === 'online' ? 'success' : 'secondary'} 
                                  border border-2 border-white rounded-circle"></span>
                        </div>
                    </div>
                </a>
                <div class="dropdown-menu dropdown-menu-end border-0 shadow-sm rounded-3 py-2" 
                     style="width: 320px;" aria-labelledby="dropdownUser">
                    <div class="p-3 border-bottom">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <img src="${avatar.src}" alt="${avatar.alt}" 
                                     class="avatar-lg rounded-circle border border-2 border-white shadow-sm" />
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1 fw-semibold">
                                    <span class="d-block text-white">${user.name}</span>
                                    <small class="text-white fw-bold">${user.viewProfileText}</small>
                                </h6>
                                <span class="badge bg-${user.badge.color}-subtle text-${user.badge.color} rounded-pill">
                                    ${user.badge.text}
                                </span>
                            </div>
                        </div>
                    </div>
                    <ul class="list-unstyled my-2">
                        ${menuItems}
                    </ul>
                </div>
            </div>
        `;
    }

    render() {
        return `
            <div class="header sticky-top">
                <nav class="navbar-custom navbar navbar-expand">
                    <div class="container-fluid px-3">
                        <div class="d-flex align-items-center">
                            ${this.getBrandLogo()}
                            ${this.getNavToggle()}
                        </div>
                        <ul class="navbar-nav align-items-center">
                            <li class="nav-item">${this.getUserProfile()}</li>
                        </ul>
                    </div>
                </nav>
            </div>
        `;
    }

    afterRender() {
        const initialize = () => {
            this.initNavToggle();
            if (window.feather) feather.replace();
        };
    
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initialize);
        } else {
            initialize();
        }
    }

    initNavToggle() {
        const maxAttempts = 20; // 20 attempts * 100ms = 2 seconds
        let attempts = 0;

        const tryInitialize = () => {
            const navToggle = document.getElementById('nav-toggle');
            const mainWrapper = document.getElementById('main-wrapper');

            if (!navToggle || !mainWrapper) {
                if (attempts < maxAttempts) {
                    attempts++;
                    setTimeout(tryInitialize, 100);
                } else {
                    console.warn('Navigation toggle elements not found after 2 seconds');
                }
                return;
            }

            // Remove existing click listeners
            const newNavToggle = navToggle.cloneNode(true);
            navToggle.parentNode.replaceChild(newNavToggle, navToggle);

            // Add click event listener
            newNavToggle.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                
                // Toggle class on main wrapper
                mainWrapper.classList.toggle('toggled');
                
                // Save state to localStorage
                const isToggled = mainWrapper.classList.contains('toggled');
                localStorage.setItem('nav-toggled', isToggled.toString());
                
                console.log('Nav toggled:', isToggled); // Debug log
            });

            // Restore previous state
            const isToggled = localStorage.getItem('nav-toggled') === 'true';
            if (isToggled) {
                mainWrapper.classList.add('toggled');
            }
            
            // Debug log
            console.log('Nav toggle initialized');
        };

        // Start initialization immediately
        tryInitialize();
    }
}