import Header from './components/header.js';
import { BASE_URL, navigateTo } from './router.js';

class App {
    constructor() {
        this.header = new Header();
        this.initAuth();
    }

    init() {
        // Render header
        document.getElementById('app').innerHTML = this.header.render();
        // Initialize header functionality
        this.header.afterRender();
    }

    initAuth() {
        // Restore authentication state
        window.is_authenticated = localStorage.getItem('is_authenticated') === 'true';
        
        // Redirect if not authenticated
        if (!window.is_authenticated && window.location.pathname !== BASE_URL + '/login') {
            navigateTo(BASE_URL + '/login');
        }
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    const app = new App();
    app.init();
});