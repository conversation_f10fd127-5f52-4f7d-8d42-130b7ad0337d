/**
 * Enhanced Modal Management Utility
 * Fixes UI freeze issues with Bootstrap modals
 */

export const modalManager = {
    // Track active modals to prevent conflicts
    activeModals: new Set(),
    
    /**
     * Safely hide a modal and return a Promise that resolves when hidden
     * @param {string|HTMLElement} modalIdentifier - Modal ID or element
     * @returns {Promise} - Resolves when modal is fully hidden
     */
    safeHideModal(modalIdentifier) {
        return new Promise((resolve, reject) => {
            try {
                const modalElement = typeof modalIdentifier === 'string' 
                    ? document.getElementById(modalIdentifier)
                    : modalIdentifier;
                
                if (!modalElement) {
                    console.warn('Modal element not found:', modalIdentifier);
                    resolve();
                    return;
                }

                const bsModal = bootstrap.Modal.getInstance(modalElement);
                
                if (!bsModal) {
                    console.warn('Bootstrap modal instance not found for:', modalIdentifier);
                    resolve();
                    return;
                }

                // Listen for the hidden event with { once: true } to prevent memory leaks
                modalElement.addEventListener('hidden.bs.modal', () => {
                    this.activeModals.delete(modalElement.id);
                    resolve();
                }, { once: true });

                // Set a timeout as fallback in case the event doesn't fire
                const timeoutId = setTimeout(() => {
                    console.warn('Modal hide timeout for:', modalIdentifier);
                    this.activeModals.delete(modalElement.id);
                    resolve();
                }, 1000);

                // Clear timeout when modal is properly hidden
                modalElement.addEventListener('hidden.bs.modal', () => {
                    clearTimeout(timeoutId);
                }, { once: true });

                // Hide the modal
                bsModal.hide();
                
            } catch (error) {
                console.error('Error hiding modal:', error);
                reject(error);
            }
        });
    },

    /**
     * Enhanced modal display function with proper cleanup and error handling
     * @param {string} modalHTML - HTML content for the modal
     * @param {string} modalId - Unique ID for the modal
     * @param {Object} options - Additional options
     * @returns {Promise} - Resolves when modal is shown
     */
    async displayModal(modalHTML, modalId, options = {}) {
        try {
            // Remove existing modal if any
            const existingModal = document.getElementById(modalId);
            if (existingModal) {
                // If modal is currently active, hide it first
                if (this.activeModals.has(modalId)) {
                    await this.safeHideModal(existingModal);
                }
                existingModal.remove();
            }

            // Add modal to body
            document.body.insertAdjacentHTML('beforeend', modalHTML);
            
            // Get the newly added modal
            const modalElement = document.getElementById(modalId);
            if (!modalElement) {
                throw new Error(`Modal element with ID ${modalId} not found after insertion`);
            }

            // Initialize Bootstrap modal with enhanced options
            const modalOptions = {
                backdrop: options.backdrop !== false ? 'static' : false,
                keyboard: options.keyboard !== false,
                focus: options.focus !== false,
                ...options.modalOptions
            };

            const modal = new bootstrap.Modal(modalElement, modalOptions);
            
            // Track active modal
            this.activeModals.add(modalId);

            // Return promise that resolves when modal is shown
            return new Promise((resolve, reject) => {
                // Listen for shown event
                modalElement.addEventListener('shown.bs.modal', () => {
                    resolve(modal);
                }, { once: true });

                // Listen for hide events to clean up
                modalElement.addEventListener('hidden.bs.modal', () => {
                    this.activeModals.delete(modalId);
                    // Clean up DOM after animation
                    setTimeout(() => {
                        if (modalElement.parentNode) {
                            modalElement.remove();
                        }
                    }, 150);
                }, { once: true });

                // Set timeout as fallback
                const timeoutId = setTimeout(() => {
                    console.warn('Modal show timeout for:', modalId);
                    reject(new Error(`Modal show timeout for ${modalId}`));
                }, 2000);

                // Clear timeout when modal is shown
                modalElement.addEventListener('shown.bs.modal', () => {
                    clearTimeout(timeoutId);
                }, { once: true });

                // Show the modal
                try {
                    modal.show();
                } catch (error) {
                    clearTimeout(timeoutId);
                    this.activeModals.delete(modalId);
                    reject(error);
                }
            });

        } catch (error) {
            console.error('Error displaying modal:', error);
            throw error;
        }
    },

    /**
     * Safely transition from one modal to another
     * @param {string|HTMLElement} sourceModal - Current modal to hide
     * @param {string} targetModalHTML - HTML for new modal
     * @param {string} targetModalId - ID for new modal
     * @param {Object} options - Options for new modal
     * @returns {Promise} - Resolves when transition is complete
     */
    async transitionModal(sourceModal, targetModalHTML, targetModalId, options = {}) {
        try {
            // First, hide the source modal and wait for it to complete
            if (sourceModal) {
                await this.safeHideModal(sourceModal);
                // Small delay to ensure smooth transition
                await new Promise(resolve => setTimeout(resolve, 100));
            }

            // Then show the target modal
            return await this.displayModal(targetModalHTML, targetModalId, options);
            
        } catch (error) {
            console.error('Error transitioning modals:', error);
            throw error;
        }
    },

    /**
     * Enhanced SweetAlert with UI freeze prevention
     * @param {Object} config - SweetAlert configuration
     * @returns {Promise} - SweetAlert promise
     */
    safeSweetAlert(config) {
        // Ensure SweetAlert is available
        if (typeof Swal === 'undefined') {
            console.error('SweetAlert2 is not available');
            return Promise.reject(new Error('SweetAlert2 not available'));
        }

        // Enhanced config with freeze prevention
        const enhancedConfig = {
            allowOutsideClick: false,
            allowEscapeKey: false,
            showConfirmButton: true,
            ...config,
            // Ensure proper cleanup
            didClose: () => {
                if (config.didClose) {
                    config.didClose();
                }
                // Force cleanup of any lingering elements
                requestAnimationFrame(() => {
                    const swalContainer = document.querySelector('.swal2-container');
                    if (swalContainer && !document.querySelector('.swal2-shown')) {
                        swalContainer.remove();
                    }
                });
            }
        };

        return Swal.fire(enhancedConfig);
    },

    /**
     * Safe timeout popup that waits for user confirmation
     * @param {Function} logoutCallback - Function to call on logout
     * @param {string} message - Timeout message
     * @returns {Promise} - Resolves with user choice
     */
    async showTimeoutPopup(logoutCallback, message = 'Your session has expired. You will be logged out.') {
        try {
            const result = await this.safeSweetAlert({
                icon: 'warning',
                title: 'Session Timeout',
                text: message,
                confirmButtonText: 'Logout Now',
                cancelButtonText: 'Stay Logged In',
                showCancelButton: true,
                allowOutsideClick: false,
                allowEscapeKey: false,
                reverseButtons: true
            });

            if (result.isConfirmed) {
                // User confirmed logout
                if (typeof logoutCallback === 'function') {
                    logoutCallback();
                }
                return true;
            }
            
            return false;
            
        } catch (error) {
            console.error('Error showing timeout popup:', error);
            // Fallback: force logout on error
            if (typeof logoutCallback === 'function') {
                logoutCallback();
            }
            return true;
        }
    },

    /**
     * Clean up all active modals (emergency cleanup)
     */
    cleanupAllModals() {
        try {
            // Hide all tracked modals
            this.activeModals.forEach(modalId => {
                const modalElement = document.getElementById(modalId);
                if (modalElement) {
                    const bsModal = bootstrap.Modal.getInstance(modalElement);
                    if (bsModal) {
                        bsModal.hide();
                    }
                }
            });

            // Clear the set
            this.activeModals.clear();

            // Remove any orphaned modal elements
            document.querySelectorAll('.modal').forEach(modal => {
                if (modal.classList.contains('show')) {
                    const bsModal = bootstrap.Modal.getInstance(modal);
                    if (bsModal) {
                        bsModal.hide();
                    }
                }
            });

            // Clean up modal backdrops
            document.querySelectorAll('.modal-backdrop').forEach(backdrop => {
                backdrop.remove();
            });

            // Reset body classes
            document.body.classList.remove('modal-open');
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';

        } catch (error) {
            console.error('Error cleaning up modals:', error);
        }
    },

    /**
     * Check if any modals are currently active
     * @returns {boolean}
     */
    hasActiveModals() {
        return this.activeModals.size > 0;
    }
};
