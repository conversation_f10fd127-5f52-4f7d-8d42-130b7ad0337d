export const referenceHandlers = {
    // Helper to collect values from dynamically added reference inputs
    collectExtraReferences(modalElement) {
        if (!modalElement) {
            console.warn('Modal element not provided to collect references');
            return [];
        }

        const refs = [];
        modalElement.querySelectorAll(".reference-input").forEach(input => {
            if (input.value.trim() !== '') {
                refs.push({
                    id: input.id,
                    label: input.previousElementSibling && input.previousElementSibling.tagName === 'LABEL' 
                        ? input.previousElementSibling.textContent 
                        : input.placeholder,
                    value: input.value.trim()
                });
            }
        });
        return refs;
    },

    // Helper to generate HTML for displaying collected references (read-only)
    generateExtraRefsHTML(extraRefs) {
        let extraRefsHTML = "";
        extraRefs.forEach((refObj, index) => {
            const labelText = refObj.label || `REFERENCE ${index + 1}`;
            extraRefsHTML += `
                <div class="mb-3">
                    <label class="form-label" for="${refObj.id}-readonly">${labelText}</label>
                    <input type="text" id="${refObj.id}-readonly" class="form-control" value="${refObj.value}" readonly>
                </div>
            `;
        });
        return extraRefsHTML;
    },

    resetExtraReferences(modalElement) {
        if (!modalElement) {
            console.warn('Modal element not provided to reset references');
            return;
        }

        modalElement.querySelectorAll(".reference-input").forEach(input => {
            input.value = '';
        });
    }
}