import { viewConfig } from './config/viewConfig.js';
import HomeView from './views/home.js';
import LoginView from './views/auth/login.js';
import LogoutView from './views/auth/logout.js';
import ProfileView from './views/profile.js';

export const BASE_URL = '/LP_Merchant_Cashier';

// Add global authentication state
window.is_authenticated = false;

// Define public routes that don't require authentication
const publicRoutes = [
    BASE_URL + '/login',
	BASE_URL + '/recover-password',
];

// Export routes separately
export const routes = {
    [BASE_URL + '/']: () => {
        const view = new HomeView();
        view.features = viewConfig.HOME.features;
        return view;
    },
    [BASE_URL + '/login']: () => {
        const view = new LoginView();
        view.features = viewConfig.LOGIN.features;
        return view;
    },
    [BASE_URL + '/logout']: () => {
        const view = new LogoutView();
        return view;
    },
    [BASE_URL + '/profile']: () => {
        const view = new ProfileView();
        return view;
    }
};

// Add authentication check
function requiresAuth(path) {
    // All routes require auth except those in publicRoutes
    return !publicRoutes.includes(path);
}

// Add navigation helper function
export function navigateTo(path) {
    // Always check auth first
    if (requiresAuth(path) && !window.is_authenticated) {
        console.log('Unauthorized access, redirecting to login');
        path = BASE_URL + '/login';
    }
    history.pushState(null, null, path);
    router();
}

export function forceLogout(reason = 'Session expired') {
    console.log('Force logout:', reason);
    authHandler.logout();
}

function router() {
    const path = window.location.pathname;
    const normalizedPath = path.replace(/\/$/, '');

    console.log('Current path:', normalizedPath);
    console.log('Is authenticated:', window.is_authenticated);

    // Check if route needs authentication
    if (requiresAuth(normalizedPath) && !window.is_authenticated) {
        console.log('Route requires auth, redirecting to login');
        navigateTo(BASE_URL + '/login');
        return;
    }

    const route = routes[normalizedPath] || routes[normalizedPath + '/'];
    if (typeof route === 'function') {
        const view = route();
        document.getElementById('app').innerHTML = view.render();
        if (typeof view.afterRender === 'function') {
            view.afterRender();
        }
    } else {
        document.getElementById('app').innerHTML = errorPage;
    }
}

const errorPage = `
    <main class="container min-vh-100 d-flex justify-content-center align-items-center">
			<div class="position-absolute end-0 top-0 p-8">
				<div class="dropdown">
					<button class="btn btn-ghost btn-icon rounded-circle" type="button" aria-expanded="false" data-bs-toggle="dropdown" aria-label="Toggle theme (auto)">
						<i class="bi theme-icon-active"></i>
						<span class="visually-hidden bs-theme-text">Toggle theme</span>
					</button>
					<ul class="dropdown-menu dropdown-menu-end shadow" aria-labelledby="bs-theme-text">
						<li>
							<button type="button" class="dropdown-item d-flex align-items-center" data-bs-theme-value="light" aria-pressed="false">
								<i class="bi theme-icon bi-sun-fill"></i>
								<span class="ms-2">Light</span>
							</button>
						</li>
						<li>
							<button type="button" class="dropdown-item d-flex align-items-center" data-bs-theme-value="dark" aria-pressed="false">
								<i class="bi theme-icon bi-moon-stars-fill"></i>
								<span class="ms-2">Dark</span>
							</button>
						</li>
						<li>
							<button type="button" class="dropdown-item d-flex align-items-center active" data-bs-theme-value="auto" aria-pressed="true">
								<i class="bi theme-icon bi-circle-half"></i>
								<span class="ms-2">Auto</span>
							</button>
						</li>
					</ul>
				</div>
			</div>
			<!-- row -->
			<div class="row">
				<!-- col -->
				<div class="col-12">
					<!-- content -->
					<div class="text-center">
						<div class="mb-3">
							<!-- img -->
							<img src="assets/images/error/404-error-img.png" alt="Image" class="img-fluid" />
						</div>
						<!-- text -->
						<h1 class="display-4">Oops! Page not found.</h1>
						<p class="mb-4">The requested page either does not exist or is currently under maintainance.</p>
						<!-- button -->
						<a href="${BASE_URL + '/'}" class="btn btn-primary">Go Home</a>
					</div>
				</div>
			</div>
		</main>
`;

// Handle link clicks
document.addEventListener('click', e => {
    const link = e.target.closest('a');
    if (link && link.href.startsWith(window.location.origin) && link.getAttribute('href') !== '#!') {
        e.preventDefault();
        navigateTo(link.pathname);
    }
});

window.addEventListener('popstate', router);
window.addEventListener('load', () => {
    // Check authentication state on load
    window.is_authenticated = localStorage.getItem('is_authenticated') === 'true';
    router();
});