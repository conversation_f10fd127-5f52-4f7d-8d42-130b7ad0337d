/**
 * Modal UI Freeze Fixes and Enhancements
 * Ensures consistent styling and prevents UI issues
 */

/* Ensure all modal titles are white */
.modal-title {
    color: #ffffff !important;
}

/* Override any conflicting color classes */
.modal-title.text-dark,
.modal-title.text-black,
.modal-title.text-muted {
    color: #ffffff !important;
}

/* Enhanced modal transitions to prevent freezes */
.modal.fade .modal-dialog {
    transition: transform 0.3s ease-out;
    transform: translate(0, -50px);
}

.modal.show .modal-dialog {
    transform: none;
}

/* Prevent modal backdrop issues */
.modal-backdrop {
    transition: opacity 0.15s linear;
}

/* Ensure proper z-index stacking */
.modal {
    z-index: 1055;
}

.modal-backdrop {
    z-index: 1050;
}

/* SweetAlert2 z-index fix */
.swal2-container {
    z-index: 2000;
}

/* Loading states for modals */
.modal-loading {
    pointer-events: none;
    opacity: 0.7;
}

.modal-loading .modal-content {
    position: relative;
}

.modal-loading .modal-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.modal-loading .modal-content::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 40px;
    height: 40px;
    margin: -20px 0 0 -20px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 11;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* File upload preview enhancements */
.attachment-preview {
    transition: all 0.3s ease;
}

.attachment-preview.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Prevent text selection during modal transitions */
.modal-transitioning {
    user-select: none;
    pointer-events: none;
}

/* Enhanced button states */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Smooth transitions for form elements */
.form-control,
.form-select {
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

/* Error state styling */
.modal .is-invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

/* Success state styling */
.modal .is-valid {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

/* Responsive modal adjustments */
@media (max-width: 576px) {
    .modal-dialog {
        margin: 0.5rem;
    }
    
    .modal-dialog-centered {
        min-height: calc(100% - 1rem);
    }
}

/* Accessibility improvements */
.modal:focus {
    outline: none;
}

.modal-content:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* Animation performance optimizations */
.modal,
.modal-backdrop {
    will-change: opacity;
}

.modal-dialog {
    will-change: transform;
}

/* Prevent layout shifts during modal operations */
body.modal-open {
    overflow: hidden;
    padding-right: 0 !important;
}

/* Enhanced close button styling */
.btn-close {
    opacity: 0.8;
    transition: opacity 0.15s ease;
}

.btn-close:hover {
    opacity: 1;
}

.btn-close-white {
    filter: invert(1) grayscale(100%) brightness(200%);
}

/* Prevent double-click issues */
.modal .btn {
    user-select: none;
}

/* Loading spinner for async operations */
.spinner-grow-sm {
    width: 1rem;
    height: 1rem;
}

/* Enhanced form validation feedback */
.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875em;
    color: #dc3545;
}

.valid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875em;
    color: #28a745;
}

/* Prevent modal content overflow */
.modal-body {
    max-height: 70vh;
    overflow-y: auto;
}

/* Smooth scrolling for modal content */
.modal-body {
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

.modal-body::-webkit-scrollbar {
    width: 6px;
}

.modal-body::-webkit-scrollbar-track {
    background: transparent;
}

.modal-body::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
}

.modal-body::-webkit-scrollbar-thumb:hover {
    background-color: rgba(0, 0, 0, 0.3);
}
