export const statsHandlers = {
    initMainContentTime() {
        // Get session start from sessionStorage or set it if not present
        let sessionStart = sessionStorage.getItem("session_start_time");
        if (!sessionStart) {
            sessionStart = new Date().toISOString();
            sessionStorage.setItem("session_start_time", sessionStart);
        }

        const sessionDate = new Date(sessionStart);

        function pad(num) {
            return num.toString().padStart(2, '0');
        }

        function formatDateTime(date) {
            const year = date.getFullYear();
            const month = pad(date.getMonth() + 1);
            const day = pad(date.getDate());
            const hours = pad(date.getHours());
            const minutes = pad(date.getMinutes());
            const seconds = pad(date.getSeconds());

            return {
                dateStr: `${year}-${month}-${day}`,
                timeStr: `${hours}:${minutes}:${seconds}`,
            };
        }

        function updateTimeDisplay() {
            const now = new Date();
            const nowFormatted = formatDateTime(now);
            const sessionFormatted = formatDateTime(sessionDate);

            const currentDateEl = document.getElementById("current_date");
            const currentTimeEl = document.getElementById("current_time");
            const sessionStartEl = document.getElementById("session_start");

            if (currentDateEl) currentDateEl.innerText = nowFormatted.dateStr;
            if (currentTimeEl) currentTimeEl.innerText = nowFormatted.timeStr;
            if (sessionStartEl) sessionStartEl.innerText = sessionFormatted.timeStr;
        }

        // Run immediately and then every second
        updateTimeDisplay();
        setInterval(updateTimeDisplay, 1000);
    },

    storeQuickStats(txnAmount, status) {
        const stats = JSON.parse(sessionStorage.getItem("quick_stats")) || {
            count: 0,
            total: 0,
            success: 0
        };

        stats.count += 1;
        stats.total += parseFloat(txnAmount.replace(/[^\d.]/g, ""));
        if (status.toLowerCase() === "success") {
            stats.success += 1;
        }

        sessionStorage.setItem("quick_stats", JSON.stringify(stats));
    },

    populateQuickStats() {
        const stats = JSON.parse(sessionStorage.getItem("quick_stats")) || {
            count: 0,
            total: 0,
            success: 0
        };

        const successRate = stats.count > 0
            ? ((stats.success / stats.count) * 100).toFixed(0) + "%"
            : "0%";

        document.getElementById("txn_count").textContent = stats.count;
        document.getElementById("txn_total").textContent = `E${stats.total.toFixed(2)}`;
        document.getElementById("txn_success").textContent = successRate;
    },

    updateLastTransaction({ type, amount, status, time }) {
        document.getElementById("last_txn_type").textContent = type;
        document.getElementById("last_txn_amount").textContent = amount;
        document.getElementById("last_txn_status").textContent = status;
        document.getElementById("last_txn_time").textContent = time;
    },

    storeLastTransaction({ type, amount, status, time }) {
        const txnData = { type, amount, status, time };
        sessionStorage.setItem("last_transaction", JSON.stringify(txnData));
    },

    updateLastTransactionUI() {
        const txnData = JSON.parse(sessionStorage.getItem("last_transaction"));
        if (txnData) {
            document.getElementById("last_txn_type").textContent = txnData.type;
            document.getElementById("last_txn_amount").textContent = txnData.amount;
            document.getElementById("last_txn_status").textContent = txnData.status;
            document.getElementById("last_txn_time").textContent = txnData.time;
        }
    }
}