<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modal Fix Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }
        .test-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="test-card p-4">
                    <h3 class="mb-4">
                        <i class="bi bi-bug-fill me-2"></i>
                        Modal Repeated Opening Test
                    </h3>
                    <p class="text-muted mb-4">
                        This test simulates the "Initiate Payment Voucher" modal being opened multiple times to verify the fix.
                    </p>
                    
                    <div class="d-flex gap-3 mb-4">
                        <button class="btn btn-primary" id="open-modal-btn">
                            <i class="bi bi-plus-circle me-2"></i>
                            Open Payment Modal
                        </button>
                        <button class="btn btn-info" id="rapid-test-btn">
                            <i class="bi bi-lightning me-2"></i>
                            Rapid Test (5x)
                        </button>
                        <button class="btn btn-success" id="stress-test-btn">
                            <i class="bi bi-speedometer me-2"></i>
                            Stress Test (20x)
                        </button>
                    </div>
                    
                    <div id="test-results" class="mt-4"></div>
                    
                    <div class="mt-4">
                        <h5>Test Log:</h5>
                        <div id="test-log" class="bg-light p-3 rounded" style="height: 200px; overflow-y: auto; font-family: monospace; font-size: 0.9em;">
                            <!-- Test logs will appear here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script type="module">
        // Import the modal manager
        import { modalManager } from './src/utils/modalManager.js';

        let testCount = 0;
        let successCount = 0;
        let errorCount = 0;

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('test-log');
            const color = type === 'error' ? 'text-danger' : type === 'success' ? 'text-success' : 'text-primary';
            logElement.innerHTML += `<div class="${color}">[${timestamp}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function updateResults() {
            const resultsElement = document.getElementById('test-results');
            resultsElement.innerHTML = `
                <div class="row text-center">
                    <div class="col-md-4">
                        <div class="h4 text-primary">${testCount}</div>
                        <small class="text-muted">Total Tests</small>
                    </div>
                    <div class="col-md-4">
                        <div class="h4 text-success">${successCount}</div>
                        <small class="text-muted">Successful</small>
                    </div>
                    <div class="col-md-4">
                        <div class="h4 text-danger">${errorCount}</div>
                        <small class="text-muted">Errors</small>
                    </div>
                </div>
            `;
        }

        // Mock transaction modal HTML (simplified version)
        function getTransactionModalHTML() {
            return `
                <div class="modal fade" id="newTransactionModal" tabindex="-1" role="dialog">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header bg-primary">
                                <h5 class="modal-title text-white">
                                    <i class="bi bi-plus-circle me-2"></i>
                                    Initiate Payment Voucher
                                </h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <form>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <label for="phone" class="form-label">Phone Number</label>
                                            <input type="tel" class="form-control" id="phone" placeholder="Enter phone number">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="amount" class="form-label">Amount</label>
                                            <input type="number" class="form-control" id="amount" placeholder="Enter amount">
                                        </div>
                                    </div>
                                    <div class="row mt-3">
                                        <div class="col-md-6">
                                            <label for="pay_from" class="form-label">Pay From</label>
                                            <select class="form-select" id="pay_from">
                                                <option value="">Select payment method</option>
                                                <option value="wallet">Wallet</option>
                                                <option value="bank">Bank Account</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="default_ref" class="form-label">Reference</label>
                                            <input type="text" class="form-control" id="default_ref" placeholder="Reference">
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                <button type="button" class="btn btn-primary">Continue</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        async function testModalOpening() {
            testCount++;
            log(`Test #${testCount}: Opening modal...`);
            
            try {
                // Remove any existing modal instances first
                const existingModal = document.getElementById('newTransactionModal');
                if (existingModal) {
                    existingModal.remove();
                }

                // Use enhanced modal manager for safe modal handling
                const modal = await modalManager.displayModal(getTransactionModalHTML(), 'newTransactionModal');
                
                // Reset modal data after modal is displayed and elements exist
                const phoneElement = document.getElementById("phone");
                const amountElement = document.getElementById("amount");
                const payFromElement = document.getElementById("pay_from");
                const defaultRefElement = document.getElementById('default_ref');
                
                if (phoneElement) {
                    phoneElement.value = "";
                    log(`✓ Phone field reset successfully`);
                } else {
                    throw new Error('Phone element not found');
                }
                
                if (amountElement) {
                    amountElement.value = "";
                    log(`✓ Amount field reset successfully`);
                } else {
                    throw new Error('Amount element not found');
                }
                
                if (payFromElement) {
                    payFromElement.value = "";
                    log(`✓ Pay from field reset successfully`);
                } else {
                    throw new Error('Pay from element not found');
                }
                
                if (defaultRefElement) {
                    defaultRefElement.value = "";
                    log(`✓ Reference field reset successfully`);
                } else {
                    throw new Error('Reference element not found');
                }
                
                successCount++;
                log(`✅ Test #${testCount} completed successfully!`, 'success');
                
                // Auto-close modal after 1 second for testing
                setTimeout(() => {
                    modal.hide();
                }, 1000);
                
            } catch (error) {
                errorCount++;
                log(`❌ Test #${testCount} failed: ${error.message}`, 'error');
            }
            
            updateResults();
        }

        // Event listeners
        document.getElementById('open-modal-btn').addEventListener('click', testModalOpening);

        document.getElementById('rapid-test-btn').addEventListener('click', async () => {
            log('🚀 Starting rapid test (5 consecutive openings)...', 'info');
            for (let i = 0; i < 5; i++) {
                await testModalOpening();
                await new Promise(resolve => setTimeout(resolve, 500)); // Wait 500ms between tests
            }
            log('🏁 Rapid test completed!', 'success');
        });

        document.getElementById('stress-test-btn').addEventListener('click', async () => {
            log('⚡ Starting stress test (20 consecutive openings)...', 'info');
            for (let i = 0; i < 20; i++) {
                await testModalOpening();
                await new Promise(resolve => setTimeout(resolve, 200)); // Wait 200ms between tests
            }
            log('🎯 Stress test completed!', 'success');
        });

        // Initialize
        log('Modal fix test initialized. Click buttons to test modal opening.', 'info');
        updateResults();
    </script>
</body>
</html>
